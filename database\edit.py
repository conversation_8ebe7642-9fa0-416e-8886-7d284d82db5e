import sqlite3

def add():
    cur = connection.cursor()

    cur.execute("INSERT INTO members (firstname, lastname, artistname, genre, email, job, birth, location, instagram, mixtape, about, phone) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            ('<PERSON>', 'Sc<PERSON><PERSON>an', 'Schatteman Visuals', 'Allround', 'x', 'Fotografie','20-08-2002', 'Oostkamp', 'schatteman_visuals', 'x', "Schatteman Visuals, ook wel Arthur <PERSON>. Deze jonge kerel focust zich ondertussen al enkele jaren op zowel fotografie als videografie, door de jaren heen is hij er daardoor ook een expert in geworden. Hij heeft al heel wat grote evenementen op beeld gebracht zoals festival Park Pop en club Carré. <PERSON> is zeker dé ideale persoon om uw evenement onvergetelijk te maken door het op een schitterende manier op beeld te brengen. Dus indien u op zoek bent naar een foto- en/of videograaf, dan is <PERSON><PERSON><PERSON>an Visuals zeker de persoon die u zoekt!", '1')
            )

    connection.commit()
    connection.close()

def editabout():
    cur = connection.cursor()

    cur.execute("""UPDATE members SET about="Dj Pauwie is een dj die reeds op jonge leeftijd begon met draaien. Hij begon in de regio van Brugge, maar door de jaren heen is zijn artiestennaam beginnen groeien. In 2023 draaide hij reeds op het festival Campo Solar en in discotheek Carré. Dj Pauwie zijn muziekgenre is allround, dus hij is vast en zeker dé perfecte match voor uw evenement!" WHERE firstname='Rens'""")

    connection.commit()
    connection.close()

def editjob():
    cur = connection.cursor()

    cur.execute("""UPDATE members SET job="Fotografie & Videografie" WHERE artistname='Schatteman Visuals'""")

    connection.commit()
    connection.close()

def editlink():
    cur = connection.cursor()

    cur.execute("""UPDATE members SET mixtape="https://soundcloud.com/djjuniormassive/website-promomix-1?in=crash-events/sets/mixtapes-crash-artiesten&utm_source=clipboard&utm_medium=text&utm_campaign=social_sharing" WHERE firstname='Jarne'""")

    connection.commit()
    connection.close()

def addcol():

    cur = connection.cursor()

    cur.execute("ALTER TABLE members ADD mixtape TEXT;")

    connection.commit()

    connection.close()

if __name__ == "__main__":
    connection = sqlite3.connect('database/artists.db')
    editjob()