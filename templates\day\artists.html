<html>
    <head>
      <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-LNXR38Z22V"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-LNXR38Z22V');
</script>
        <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    
    <!-- Favicon -->
    <link rel="shortcut icon" href="/static/favicon/logo.png" type="image/x-icon" />    

    <!-- Map CSS -->
    <link rel="stylesheet" href="https://api.mapbox.com/mapbox-gl-js/v0.53.0/mapbox-gl.css" />
    
    <!-- Libs CSS -->
    <link rel="stylesheet" href="/static/css/libs.bundle.css" />
    
    <!-- Theme CSS -->
    <link rel="stylesheet" href="/static/css/theme-day.bundle.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">

    
    <title>{{ _("Artiesten") }} | Crash Events</title>
    </head><!-- Google tag (gtag.js) -->

    <body class="bg-black">

        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container-fluid">
          
              <!-- Brand -->
              <a class="text-white" href="{{ url_for('artists') }}">
                <img src="static/img/crashevents/icon_full.png" class="navbar-brand-img" alt="...">                
        
              </a>
          
              <!-- Toggler -->
              <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
              </button>
          
              <!-- Collapse -->
              <div class="collapse navbar-collapse" id="navbarCollapse">
          
                <!-- Toggler -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
                  <i class="fe fe-x"></i>
                </button>
          
                <!-- Navigation -->
                <ul class="navbar-nav ms-auto">
                  <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" id="navbarDocumentation" data-scroll href="{{ url_for('artists') }}" aria-haspopup="true" aria-expanded="false">
                      {{ _("Artiesten") }}
                    </a>
                    <div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDocumentation">
                      <div class="list-group list-group-flush">
                        <a class="list-group-item" href="{{ url_for('artists') }}">
          
                          <!-- Icon -->
                          <div class="icon icon-sm text-dark">
                            <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M0 0h24v24H0z"></path><path d="M18 2h2a3 3 0 013 3v14a3 3 0 01-3 3h-2V2z" fill="#080d18" opacity=".3"></path><path d="M5 2h12a3 3 0 013 3v14a3 3 0 01-3 3H5a1 1 0 01-1-1V3a1 1 0 011-1zm7 9a2 2 0 100-4 2 2 0 000 4zm-5 5.5c-.011.162.265.5.404.5h9.177c.418 0 .424-.378.418-.5-.163-3.067-2.348-4.5-5.008-4.5-2.623 0-4.775 1.517-4.99 4.5z" fill="#080d18"></path></g></svg>
                          </div>
          
                          <!-- Content -->
                          <div class="ms-4">
          
                            <!-- Heading -->
                            <h6 class="fw-bold text-uppercase text-dark mb-0">
                              {{ _("Artiesten") }}
                            </h6>
          
                            <!-- Text -->
                            <p class="fs-sm text-gray-700 mb-0">
                              {{ _("Ontdek de DJ's in ons management") }}
                            </p>
          
                          </div>
          
                        </a>
                        
                      </div>
                    </div>
                  </li>
                  <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" id="navbarLandings" data-scroll href="{{ url_for('agency') }}" aria-haspopup="true" aria-expanded="false">
                      {{ _("Agency") }}
                    </a>
                    <div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDocumentation">
                      <div class="list-group list-group-flush">
                        <a class="list-group-item" href="{{ url_for('agency') }}">
          
                            <!-- Icon -->
                            <div class="icon icon-sm text-dark">
                              <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M0 0h24v24H0z"></path><circle fill="#080d18" opacity=".3" cx="12" cy="12" r="10"></circle><path d="M12 16a1 1 0 110 2 1 1 0 010-2zm-1.409-1.132v-1.659h1.26c1.596 0 2.751-1.218 2.751-2.814s-1.155-2.814-2.751-2.814c-1.617 0-2.73 1.218-2.73 2.814H7.336c0-2.52 1.974-4.473 4.515-4.473 2.541 0 4.536 1.953 4.536 4.473 0 2.52-1.995 4.473-4.536 4.473h-1.26z" fill="#080d18"></path></g></svg>
                            </div>            
                            <!-- Content -->
                            <div class="ms-4">
            
                              <!-- Heading -->
                              <h6 class="fw-bold text-uppercase text-dark mb-0">
                                {{ _("Agency") }}
                              </h6>
            
                              <!-- Text -->
                              <p class="fs-sm text-gray-700 mb-0">
                                {{ _("Alle diensten voor jouw evenement") }}
                              </p>
            
                            </div>
            
                          </a>
                        
                        
                        
          
                      </div>
                    </div>
                  </li>
                  <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" id="navbarDocumentation" data-scroll href="{{ url_for('about') }}" aria-haspopup="true" aria-expanded="false">
                      {{ _("Contact") }}
                    </a>
                    <div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDocumentation">
                      <div class="list-group list-group-flush">
                        <a class="list-group-item" href="about#whoweare" data-scroll>
          
                          <!-- Icon -->
                          <div class="icon icon-sm text-dark">
                            <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M0 0h24v24H0z"></path><path d="M18 14a3 3 0 110-6 3 3 0 010 6zm-9-3a4 4 0 110-8 4 4 0 010 8z" fill="#080d18" opacity=".3"></path><path d="M17.601 15c3.407.038 6.188 1.76 6.397 5.4.009.147 0 .6-.542.6H19.6c0-2.25-.744-4.328-1.999-6zm-17.6 5.2C.388 15.426 4.26 13 8.983 13c4.788 0 8.722 2.293 9.015 7.2.012.195 0 .8-.751.8H.727c-.25 0-.747-.54-.726-.8z" fill="#080d18"></path></g></svg>                      <!-- Content -->
                          </div>          
                          <!-- Content -->
                          <div class="ms-4">
          
                            <!-- Heading -->
                            <h6 class="fw-bold text-uppercase text-dark mb-0">
                              {{ _("Ons team") }}
                            </h6>
          
                            <!-- Text -->
                            <p class="fs-sm text-gray-700 mb-0">
                              {{ _("Gepassioneerd & gedreven") }}
                            </p>
          
                          </div>
          
                        </a>
                        
                        <a class="list-group-item" href="about#contact" data-scroll>
          
                          <!-- Icon -->
                          <div class="icon icon-sm text-dark">
                            <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M0 0h24v24H0z"></path><path d="M11.915 14.143l2.204-2.204a2 2 0 00.375-2.309l-.125-.25a2 2 0 01.374-2.308l2.733-2.733a.5.5 0 01.8.13l1.105 2.208a4.387 4.387 0 01-.822 5.064l-5.999 6a5.427 5.427 0 01-5.554 1.31l-2.414-.805a.5.5 0 01-.195-.828l2.65-2.65a2 2 0 012.31-.375l.25.124a2 2 0 002.308-.374z" fill="#080d18"></path></g></svg>                   <!-- Content -->
                          </div>          
                          <!-- Content -->
                          <div class="ms-4">
          
                            <!-- Heading -->
                            <h6 class="fw-bold text-uppercase text-dark mb-0">
                              {{ _("Contacteer ons") }}
                            </h6>
          
                            <!-- Text -->
                            <p class="fs-sm text-gray-700 mb-0">
                              {{ _("Let's get in touch!") }}
                            </p>
          
                          </div>
          
                        </a>
                        
                      </div>
                    </div>
                  </li>
                  
                </ul>
          
                <!-- Button -->
                <div class="d-none d-lg-block ms-auto">
                  <div class="dropdown" style="display: inline-block;">
                    <a class="btn btn-rounded-circle btn-primary align-items-center" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                      <span>{{ lang }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
                      <li><a class="dropdown-item" href="language/nl">Nederlands</a></li>
                      <li><a class="dropdown-item" href="language/en">English</a></li>
                      <li><a class="dropdown-item" href="language/fr">Français</a></li>
                    </ul>
                  </div>
                  
                  <a class="btn btn-rounded-circle btn-primary" href="{{ url_for('toggle_theme', current_page=request.path) }}">
                    <i class="dark-hide fe fe-sun"></i>                    
                  </a>
                  
        
                </div>
                <div class="d-lg-none ms-auto text-center text-md-center mt-4 mb-4 m-4">
                  <div class="dropup" style="display: inline-block;">
                    <a class="btn btn-rounded-circle btn-primary align-items-center" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                      <span>{{ lang }}</span>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                      <li><a class="dropdown-item" href="language/nl">Nederlands</a></li>
                      <li><a class="dropdown-item" href="language/en">English</a></li>
                      <li><a class="dropdown-item" href="language/fr">Français</a></li>
                    </ul>
                  </div>
                  <a href="{{ url_for('toggle_theme', current_page=request.path) }}" class="btn btn-rounded-circle btn-primary">
                    <i class="dark-hide fe fe-sun"></i>  
                  </a>
                </div>
          
              </div>
          
            </div>
          </nav>
  
    <!-- WELCOME -->
    <section data-jarallax data-speed=".8" class="bg-gradient-dark-black pt-15 pb-15 jarallax bg-pattern-2" style="margin-top: -150px; background-image:  linear-gradient(to top, rgba(16, 24, 45, 1) 20%, rgba(16, 24, 45, 0.6) 86%, rgba(16, 24, 45) 100%), url(/static/img/bgs/bg-home-light.jpg);">

    
        <div class="container">
          <div class="justify-content-center text-center pb-4">
            <h1 class="pt-3 display-2 fw-bold text-white">
              {{ _('Ons management') }}
            </h1>
            <a class="text-white" href="agency" style="cursor: pointer; text-decoration: none;">
              {{ _('Ontdek wat we doen') }} <i class="fe fe-arrow-right ms-2"></i>
            </a>
          </div>
        </div> <!-- / .container -->
      </section>
  
      <!-- SHAPE -->
      <div class="position-relative">
        <div class="shape shape-bottom shape-fluid-x text-white">
          <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0 48h2880V0h-720C1442.5 52 720 0 720 0H0v48z" fill="currentColor"/></svg>      </div>
      </div>
  
      <!-- CONTENT -->
      <section class="bg-black py-8 py-md-11 mt-n11 mt-md-n15">
        <div class="container">
          <nav class="nav justify-content-center pb-6">
            <a class="badge rounded-pill bg-white-soft active me-1 mb-1" href="#" data-bs-toggle="pill" data-filter="*" data-bs-target="#artiesten">
              <span class="h6 text-uppercase">{{ _("Alles") }}</span>
            </a>
            <a class="badge rounded-pill bg-white-soft me-1 mb-1" href="#" data-bs-toggle="pill" data-filter=".allround" data-bs-target="#artiesten">
              <span class="h6 text-uppercase">allround</span>
            </a>
            <a class="badge rounded-pill bg-white-soft me-1 mb-1" href="#" data-bs-toggle="pill" data-filter=".bass" data-bs-target="#artiesten">
              <span class="h6 text-uppercase">bass</span>
            </a>
            <a class="badge rounded-pill bg-white-soft me-1 mb-1" href="#" data-bs-toggle="pill" data-filter=".urban" data-bs-target="#artiesten">
              <span class="h6 text-uppercase">urban</span>
            </a>
            <a class="badge rounded-pill bg-white-soft me-1 mb-1" href="#" data-bs-toggle="pill" data-filter=".r/&b" data-bs-target="#artiesten">
              <span class="h6 text-uppercase">r&b</span>
            </a>
            <a class="badge rounded-pill bg-white-soft me-1 mb-1" href="#" data-bs-toggle="pill" data-filter=".techno" data-bs-target="#artiesten">
              <span class="h6 text-uppercase">techno</span>
            </a>
            <a class="badge rounded-pill bg-white-soft me-1 mb-1" href="#" data-bs-toggle="pill" data-filter=".commercieel" data-bs-target="#artiesten">
              <span class="h6 text-uppercase">commercieel</span>
            </a>
          </nav>
          
          
          <div class="row" id="artiesten" data-isotope='{"layoutMode": "masonry"}'>
            {% for artist in artists %}
            <div class="col-12 col-md-4 resources {{ artist[3] }}">
  
              <!-- Card -->
              <a class="card shadow-dark-lg mb-7 bg-white" href="{{ artist[5] }}">
  
                <!-- Image -->
                <div class="card-zoom">
                  <img class="card-img" src="/static/img/artists/{{ artist[7] }}.jpg" alt="...">
                </div>
  
                <!-- Overlay -->
                <div class="card-img-overlay card-img-overlay-hover">
                  <div class="card-body bg-white">
  
                    <!-- Shape -->
                    <!-- <div class="shape shape-bottom-100 shape-fluid-x text-white">
                      <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100,100 h200 a20,20 0 0 1 20,20 v200 a20,20 0 0 1 -20,20 h-200 a20,20 0 0 1 -20,-20 v-200 a20,20 0 0 1 20,-20 z" /></svg>                  </div>
   -->
                    <!-- Preheading -->
                    <h6 class="text-uppercase mb-1 text-primary">{{ artist[3] }}</h6>
  
                    <!-- Heading -->
                    <h4 class="mb-0 text-black">{{ artist[2] }}<i class="fe fe-arrow-right ms-2"></i></h4>
  
                  </div>
                </div>
  
              </a>
  
            </div>
            
            {% endfor %}
            
            
  
          </div>
        </div>
      </section>
      <section class="bg-black">
        <div class="container pt-6 pb-14">
          <div class="row justify-content-center">
            <div class="col-12 col-md-10 col-lg-8 text-center">
  
              <!-- Pretitle -->
              <h6 class="text-uppercase text-primary mb-5">
                {{ _("Contacteer ons") }}
              </h6>
  
              <!-- Heading -->
              <h1 class="fw-bold text-white-2">
                {{ _("Vind je niet wie je zoekt?") }}
              </h1>
  
              <!-- Text -->
              <p class="fs-lg text-gray-800 mb-6 mb-md-8">
                {{ _("Niet heel ons netwerk staat op onze website. Contacteer ons en samen vinden we het juiste talent voor jouw evenement.") }}
              </p>
  
              <!-- Button -->
              <a href="about#contact" class="btn btn-primary lift">
                {{ _("Contacteer ons") }} <i class="fe fe-arrow-right ms-2"></i>
              </a>
  
            </div>
          </div> <!-- / .row -->
        </div>
      </section>
      <section class="bg-dark">
        <div class="container pb-6 pt-7">
          <div class="row align-items-center text-center">
            <div class="col-12 col-md">
  
              <!-- Heading -->
              <h1 class="fw-bold text-white mt-1 d-none d-lg-block">
                {{ _("let's talk!") }} <a href="about#contact" class="text-white underline">{{ _("Contacteer ons nu.") }}</a>
              </h1>
              <h1 class="fw-bold text-white mt-1 d-lg-none">
                {{ _("let's talk!") }}<br><a href="about#contact" class="text-white underline">{{ _("Contacteer ons nu.") }}</a>
              </h1>
  
            </div>
          </div> <!-- / .row -->
        </div> <!-- / .container -->
      </section>
  
      
      <footer class="py-8 py-md-11 bg-black">
        <div class="container">
          <div class="row">
            <div class="col-12 col-md-4 col-lg-3">
      
              <!-- Brand -->
              <img src="static/img/crashevents/icon_full.png" width="100" height="100" alt="..." class="footer-brand img-fluid mb-2">
      
              <!-- Text -->
              <p class="text-gray-800 mb-2">
                {{ _("Artist Management Agency") }}
              </p>
      
              <!-- Social -->
              <ul class="list-unstyled list-inline list-social mb-6 mb-md-0">
                  <li class="list-inline-item list-social-item me-3">
                    <a href="https://www.instagram.com/crashevents/" target="_blank" class="text-decoration-none">
                      <img src="/static/img/icons/social/instagram.svg" class="list-social-icon" alt="...">
                    </a>
                  </li>
                  <li class="list-inline-item list-social-item me-3">
                    <a href="https://www.facebook.com/profile.php?id=100090077017776" class="text-decoration-none disabled" target="_blank">
                      <img src="/static/img/icons/social/facebook.svg"  class="list-social-icon" alt="...">
                    </a>
                  </li>
                  <li class="list-inline-item list-social-item">
                    <a href="https://www.linkedin.com/company/crash-events/" class="text-decoration-none" target="_blank">
                      <img src="/static/img/icons/social/linkedin.svg"  class="list-social-icon" alt="...">
                    </a>
                  </li>
                  <li class="list-inline-item list-social-item me-2">
                    <a href="https://www.notion.so/leonmissoul/https-on-soundcloud-com-v9Mk2gXVA11J3j6r5-7687b95bcf264be7a338aaeb941293a1?pvs=4" class="text-decoration-none disabled" target="_blank">
                      <img src="/static/img/icons/social/soundcloud.svg"  class="list-social-icon" alt="...">
                    </a>
                  </li>
                </ul>
      
            </div>
            <div class="col-6 col-md-2 col-lg-2">
      
              <!-- Heading -->
              <h6 class="fw-bold text-uppercase text-gray-700">
                {{ _("Artiesten") }}
              </h6>
      
              <!-- List -->
              <ul class="list-unstyled text-gray-800 mb-6 mb-md-8 mb-lg-0">
  
                <li class="mb-3">
                  <a href="" data-scroll class="text-reset disabled">
                    {{ _("Artiesten") }}
                  </a>
                </li>
                
                
              </ul>
      
            </div>
            <div class="col-6 col-md-4 col-lg-2">
      
              <h6 class="fw-bold text-uppercase text-gray-700">
                {{ _("Agency") }}
              </h6>
      
              <ul class="list-unstyled text-gray-800 mb-6 mb-md-8 mb-lg-0">
                <li class="mb-3">
                  <a href="agency" data-scroll class="text-reset disabled">
                    {{ _("Agency") }}
                  </a>
                </li>
  
              </ul>
      
            </div>
            <div class="col-6 col-md-4 offset-md-4 col-lg-2 offset-lg-0">
      
              <!-- Heading -->
              <h6 class="fw-bold text-uppercase text-gray-700">
                {{ _("Ons team") }}
              </h6>
      
              <!-- List -->
              <ul class="list-unstyled text-gray-800 mb-0">
                <li class="mb-3">
                  <a href="about#whoweare" class="text-reset">
                    {{ _("Ons team") }}
                  </a>
                </li>
                
                
                
              </ul>
      
            </div>
            <div class="col-6 col-md-4 col-lg-2">
      
              <!-- Heading -->
              <h6 class="fw-bold text-uppercase text-gray-700">
                {{ _("Contact") }}
              </h6>
      
              <!-- List -->
              <ul class="list-unstyled text-gray-800 mb-0">
                <li class="mb-3">
                  <a href="about#contact" class="text-reset">
                    {{ _("Contact") }}
                  </a>
                </li>
                
  
              </ul>
      
            </div>
          </div> <!-- / .row -->
        </div> <!-- / .container -->
      </footer>

      <!-- Map JS -->
      <script src='https://api.mapbox.com/mapbox-gl-js/v0.53.0/mapbox-gl.js'></script>
      
      <!-- Vendor JS -->
      <script src="/static/js/vendor.bundle.js"></script>
      
      <!-- Theme JS -->
      <script src="/static/js/theme.bundle.js"></script>        
  </body>
</html>