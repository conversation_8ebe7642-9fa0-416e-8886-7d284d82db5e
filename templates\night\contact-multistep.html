<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />


  <!-- Favicon -->
  <link rel="shortcut icon" href="/static/favicon/logo.png" type="image/x-icon" />

  <!-- Map CSS -->
  <link rel="stylesheet" href="https://api.mapbox.com/mapbox-gl-js/v0.53.0/mapbox-gl.css" />

  <!-- Libs CSS -->
  <link rel="stylesheet" href="/static/css/libs.bundle.css" />

  <!-- Theme CSS -->
  <link rel="stylesheet" href="/static/css/theme-night.bundle.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">

  <link rel="stylesheet" href="/static/css/contact-form.css" />


  <title>Aanbod | Crash Events</title>
</head>

<body class="bg-white">

  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">

      <!-- Brand -->
      <a class="navbar-brand text-black" href="{{ url_for('artists') }}">
        <!-- <img src="./assets/img/brand.svg" class="navbar-brand-img" alt="..."> -->
        <span class="text-black">CRASH</span>

      </a>

      <!-- Toggler -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse"
        aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Collapse -->
      <div class="collapse navbar-collapse" id="navbarCollapse">

        <!-- Toggler -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse"
          aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
          <i class="fe fe-x"></i>
        </button>

        <!-- Navigation -->
        <ul class="navbar-nav ms-auto">
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" id="navbarDocumentation" data-scroll
              href="{{ url_for('artists') }}" aria-haspopup="true" aria-expanded="false">
              {{ _("Artiesten") }}
            </a>
            <div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDocumentation">
              <div class="list-group list-group-flush">
                <a class="list-group-item" href="{{ url_for('artists') }}">

                  <!-- Icon -->
                  <div class="icon icon-sm text-primary">
                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <g fill="none" fill-rule="evenodd">
                        <path d="M0 0h24v24H0z"></path>
                        <path d="M18 2h2a3 3 0 013 3v14a3 3 0 01-3 3h-2V2z" fill="#080d18" opacity=".3"></path>
                        <path
                          d="M5 2h12a3 3 0 013 3v14a3 3 0 01-3 3H5a1 1 0 01-1-1V3a1 1 0 011-1zm7 9a2 2 0 100-4 2 2 0 000 4zm-5 5.5c-.011.162.265.5.404.5h9.177c.418 0 .424-.378.418-.5-.163-3.067-2.348-4.5-5.008-4.5-2.623 0-4.775 1.517-4.99 4.5z"
                          fill="#080d18"></path>
                      </g>
                    </svg>
                  </div>

                  <!-- Content -->
                  <div class="ms-4">

                    <!-- Heading -->
                    <h6 class="fw-bold text-uppercase text-primary mb-0">
                      {{ _("Artiesten") }}
                    </h6>

                    <!-- Text -->
                    <p class="fs-sm text-gray-700 mb-0">
                      {{ _("Ontdek de DJ's in ons management") }}
                    </p>

                  </div>

                </a>

              </div>
            </div>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" id="navbarLandings" data-scroll
              href="{{ url_for('agency') }}" aria-haspopup="true" aria-expanded="false">
              {{ _("Agency") }}
            </a>
            <div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDocumentation">
              <div class="list-group list-group-flush">
                <a class="list-group-item" href="{{ url_for('agency') }}">

                  <!-- Icon -->
                  <div class="icon icon-sm text-primary">
                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <g fill="none" fill-rule="evenodd">
                        <path d="M0 0h24v24H0z"></path>
                        <circle fill="#080d18" opacity=".3" cx="12" cy="12" r="10"></circle>
                        <path
                          d="M12 16a1 1 0 110 2 1 1 0 010-2zm-1.409-1.132v-1.659h1.26c1.596 0 2.751-1.218 2.751-2.814s-1.155-2.814-2.751-2.814c-1.617 0-2.73 1.218-2.73 2.814H7.336c0-2.52 1.974-4.473 4.515-4.473 2.541 0 4.536 1.953 4.536 4.473 0 2.52-1.995 4.473-4.536 4.473h-1.26z"
                          fill="#080d18"></path>
                      </g>
                    </svg>
                  </div>
                  <!-- Content -->
                  <div class="ms-4">

                    <!-- Heading -->
                    <h6 class="fw-bold text-uppercase text-primary mb-0">
                      {{ _("Agency") }}
                    </h6>

                    <!-- Text -->
                    <p class="fs-sm text-gray-700 mb-0">
                      {{ _("Alle diensten voor jouw evenement") }}
                    </p>

                  </div>

                </a>




              </div>
            </div>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" id="navbarDocumentation" data-scroll
              href="{{ url_for('about") }}" aria-haspopup="true" aria-expanded="false">
              Over ons
            </a>
            <div class="dropdown-menu dropdown-menu-md" aria-labelledby="navbarDocumentation">
              <div class="list-group list-group-flush">
                <a class="list-group-item" href="{{ url_for('about") }}">

                  <!-- Icon -->
                  <div class="icon icon-sm text-primary">
                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <g fill="none" fill-rule="evenodd">
                        <path d="M0 0h24v24H0z"></path>
                        <path d="M18 14a3 3 0 110-6 3 3 0 010 6zm-9-3a4 4 0 110-8 4 4 0 010 8z" fill="#080d18"
                          opacity=".3"></path>
                        <path
                          d="M17.601 15c3.407.038 6.188 1.76 6.397 5.4.009.147 0 .6-.542.6H19.6c0-2.25-.744-4.328-1.999-6zm-17.6 5.2C.388 15.426 4.26 13 8.983 13c4.788 0 8.722 2.293 9.015 7.2.012.195 0 .8-.751.8H.727c-.25 0-.747-.54-.726-.8z"
                          fill="#080d18"></path>
                      </g>
                    </svg> <!-- Content -->
                  </div>
                  <!-- Content -->
                  <div class="ms-4">

                    <!-- Heading -->
                    <h6 class="fw-bold text-uppercase text-primary mb-0">
                      Over ons
                    </h6>

                    <!-- Text -->
                    <p class="fs-sm text-gray-700 mb-0">
                      Kom meer te weten over wie we zijn.
                    </p>

                  </div>

                </a>

              </div>
            </div>
          </li>
        </ul>

        <!-- Button -->
        <div class="ms-auto">
          <div class="dropdown" style="display: inline-block;">
            <a class="btn btn-rounded-circle btn-primary align-items-center" id="dropdownMenuButton1"
              data-bs-toggle="dropdown" aria-expanded="false">
              <span>{{ lang_code }}</span>
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
              <li><a class="dropdown-item" href="language/nl">Nederlands</a></li>
              <li><a class="dropdown-item" href="language/en">English</a></li>
              <li><a class="dropdown-item" href="language/fr">Français</a></li>
            </ul>
          </div>

          <a class="btn btn-rounded-circle btn-primary" href="{{ url_for('toggle_theme', current_page=request.path) }}">
            <i class="dark-hide fe fe-moon"></i>
          </a>


        </div>

      </div>

    </div>
  </nav>

  <!-- WELCOME -->
  <section class="mt-n11 pt-12 pb-8 pt-md-14 pb-md-11 bg-black bg-pattern-2">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-12 col-lg-10 text-center">

          <!-- Headin -->
          <h1 class="pt-5 display-2 fw-bold text-white mb-6">
            {{ _("Contact") }}
          </h1>
          <p class="lead text-gray-500 mb-6 mx-auto" style="max-width: 500px;">
            Volg de onderstaande stappen om contact op te nemen.
          </p>

        </div>
      </div> <!-- / .row -->

    </div> <!-- / .container -->
  </section>

  <section class="bg-gradient-dark-black pb-8 pt-3 py-md-11">

    <div class="container justify-content-center text-center">

      <div class="row justify-content-center">
        <div class="col-8">

          <!-- Card -->
          <!-- <div class="card card-border border-primary bg-black text-white shadow-light-lg">
              <div class="card-body"> -->

          <!-- Form -->
          <form id="regForm" action="" method="post">

            <div class="mb-7 mt-n4 text-center">
              <span class="step"></span>
              <span class="step"></span>
              <span class="step"></span>
              <span class="step"></span>

            </div>
            

            <!-- One "tab" for each step in the form: -->
            <div class="tab text-center align-items-center justify-content-center" >
              <h2 class="fw-bold text-white">
                Wat beschrijft je het best?
              </h2>
              <label for="interest" class="form-label text-white">Ben je (student-)zelfstandige?</label>
              <div class="form-group mb-5" style="float: center;">

                <select id="interest" name="interest" class="form-control bg-dark" style="color: white;border: #1B2A4E;" required>
                  <option id="JA" value="JA">JA</option>
                  <option id="NEE" value="NEE">NEE</option>
                </select>
              </div>
            </div>

            <div class="tab">Contact Info:
              <p><input placeholder="E-mail..." oninput="this.className = ''"></p>
              <p><input placeholder="Phone..." oninput="this.className = ''"></p>
            </div>

            <div class="tab">Birthday:
              <p><input placeholder="dd" oninput="this.className = ''"></p>
              <p><input placeholder="mm" oninput="this.className = ''"></p>
              <p><input placeholder="yyyy" oninput="this.className = ''"></p>
            </div>

            <div class="tab">Login Info:
              <p><input placeholder="Username..." oninput="this.className = ''"></p>
              <p><input placeholder="Password..." oninput="this.className = ''"></p>
            </div>

            <div>
              <div class="justify-content-center mt-4">
                <button type="button" class="btn btn-primary mb-6 mb-md-0 lift" id="prevBtn"
                  onclick="nextPrev(-1)">Vorige</button>
                <button type="button" class="btn btn-primary mb-6 mb-md-0 lift" id="nextBtn"
                  onclick="nextPrev(1)">Volgende</button>
              </div>
            </div>

            <!-- Circles which indicates the steps of the form: -->


          </form>

          <script>
            var currentTab = 0; // Current tab is set to be the first tab (0)
            showTab(currentTab); // Display the current tab

            function fixStepIndicator(n) {
              // This function removes the "active" class of all steps...
              var i, x = document.getElementsByClassName("step");
              for (i = 0; i < x.length; i++) {
                x[i].className = x[i].className.replace(" active", "");
              }
              //... and adds the "active" class to the current step:
              x[n].className += " active";
              x[n].classList.add("class-name");
            }

            function showTab(n) {
              // This function will display the specified tab of the form ...
              var x = document.getElementsByClassName("tab");
              x[n].style.display = "block";
              // ... and fix the Previous/Next buttons:
              if (n == 0) {
                document.getElementById("prevBtn").style.display = "none";
              } else {
                document.getElementById("prevBtn").style.display = "inline";
              }
              if (n == (x.length - 1)) {
                document.getElementById("nextBtn").innerHTML = "Verzenden";
              } else {
                document.getElementById("nextBtn").innerHTML = "Volgende";
              }
              // ... and run a function that displays the correct step indicator:
              fixStepIndicator(n)
            }

            function nextPrev(n) {
              // This function will figure out which tab to display
              var x = document.getElementsByClassName("tab");
              // Exit the function if any field in the current tab is invalid:
              if (n == 1 && !validateForm()) return false;
              // Hide the current tab:
              x[currentTab].style.display = "none";
              // Increase or decrease the current tab by 1:
              currentTab = currentTab + n;
              // if you have reached the end of the form... :
              if (currentTab >= x.length) {
                //...the form gets submitted:
                document.getElementById("regForm").submit();
                return false;
              }
              // Otherwise, display the correct tab:
              showTab(currentTab);
            }

            function validateForm() {
              // This function deals with validation of the form fields
              var x, y, i, valid = true;
              x = document.getElementsByClassName("tab");
              y = x[currentTab].getElementsByTagName("input");
              // A loop that checks every input field in the current tab:
              for (i = 0; i < y.length; i++) {
                // If a field is empty...
                if (y[i].value == "") {
                  // add an "invalid" class to the field:
                  y[i].className += " invalid";
                  // and set the current valid status to false:
                  valid = false;
                }
              }
              // If the valid status is true, mark the step as finished and valid:
              if (valid) {
                document.getElementsByClassName("step")[currentTab].className += " finish";
              }
              return valid; // return the valid status
            }

            
          </script>

          <!-- </div>
            </div> -->

        </div>
      </div> <!-- / .row -->
    </div> <!-- / .container -->


  </section>



  <footer class="py-8 py-md-11 bg-black">
    <div class="container">
      <div class="row">
        <div class="col-12 col-md-4 col-lg-3">

          <!-- Brand -->
          <img src="static/img/crashevents/icon_full.png" width="100" height="100" alt="..." class="footer-brand img-fluid mb-2">

          <!-- Text -->
          <p class="text-gray-500 mb-2">
            {{ _("Artist Management Agency") }}
          </p>

          <!-- Social -->
          <ul class="list-unstyled list-inline list-social mb-6 mb-md-0">
            <li class="list-inline-item list-social-item me-3">
              <a href="https://www.instagram.com/crashevents/" target="_blank" class="text-decoration-none">
                <img src="/static/img/icons/social/instagram.svg" class="list-social-icon" alt="...">
              </a>
            </li>
            <li class="list-inline-item list-social-item me-3">
              <a href="https://www.facebook.com/profile.php?id=100090077017776" class="text-decoration-none disabled"
                target="_blank">
                <img src="/static/img/icons/social/facebook.svg" class="list-social-icon" alt="...">
              </a>
            </li>
            <li class="list-inline-item list-social-item">
              <a href="https://www.linkedin.com/company/crash-events/" class="text-decoration-none" target="_blank">
                <img src="/static/img/icons/social/linkedin.svg" class="list-social-icon" alt="...">
              </a>
            </li>
            <li class="list-inline-item list-social-item me-2">
              <a href="https://www.facebook.com/profile.php?id=100090077017776" class="text-decoration-none disabled"
                target="_blank">
                <img src="/static/img/icons/social/soundcloud.svg" class="list-social-icon" alt="...">
              </a>
            </li>
          </ul>

        </div>
        <div class="col-6 col-md-2 col-lg-2">

          <!-- Heading -->
          <h6 class="fw-bold text-uppercase text-gray-700">
            {{ _("Artiesten") }}
          </h6>

          <!-- List -->
          <ul class="list-unstyled text-gray-500 mb-6 mb-md-8 mb-lg-0">

            <li class="mb-3">
              <a href="" data-scroll class="text-reset disabled">
                {{ _("Artiesten") }}
              </a>
            </li>


          </ul>

        </div>
        <div class="col-6 col-md-4 col-lg-2">

          <h6 class="fw-bold text-uppercase text-gray-700">
            {{ _("Agency") }}
          </h6>

          <ul class="list-unstyled text-gray-500 mb-6 mb-md-8 mb-lg-0">
            <li class="mb-3">
              <a href="events" data-scroll class="text-reset disabled">
                {{ _("Agency") }}
              </a>
            </li>

          </ul>

        </div>
        <div class="col-6 col-md-4 offset-md-4 col-lg-2 offset-lg-0">

          <!-- Heading -->
          <h6 class="fw-bold text-uppercase text-gray-700">
            Over ons
          </h6>

          <!-- List -->
          <ul class="list-unstyled text-gray-500 mb-0">
            <li class="mb-3">
              <a href="about#whoweare" class="text-reset">
                Over ons
              </a>
            </li>



          </ul>

        </div>
        <div class="col-6 col-md-4 col-lg-2">

          <!-- Heading -->
          <h6 class="fw-bold text-uppercase text-gray-700">
            {{ _("Contact") }}
          </h6>

          <!-- List -->
          <ul class="list-unstyled text-gray-500 mb-0">
            <li class="mb-3">
              <a href="#contact" class="text-reset">
                {{ _("Contact") }}
              </a>
            </li>


          </ul>

        </div>
      </div> <!-- / .row -->
    </div> <!-- / .container -->
  </footer>

  <!-- Map JS -->
  <script src='https://api.mapbox.com/mapbox-gl-js/v0.53.0/mapbox-gl.js'></script>

  <!-- Vendor JS -->
  <script src="/static/js/vendor.bundle.js"></script>

  <!-- Theme JS -->
  <script src="/static/js/theme.bundle.js"></script>
</body>

</html>