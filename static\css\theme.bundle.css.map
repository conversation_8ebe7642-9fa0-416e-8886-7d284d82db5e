{"version": 3, "sources": ["webpack://landkit/./node_modules/bootstrap/scss/bootstrap.scss", "webpack://landkit/./src/scss/theme.scss", "webpack://landkit/./node_modules/bootstrap/scss/_root.scss", "webpack://landkit/./node_modules/bootstrap/scss/_reboot.scss", "webpack://landkit/./src/scss/theme/_variables.scss", "webpack://landkit/./node_modules/bootstrap/scss/vendor/_rfs.scss", "webpack://landkit/./node_modules/bootstrap/scss/_variables.scss", "webpack://landkit/./node_modules/bootstrap/scss/_functions.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_border-radius.scss", "webpack://landkit/./node_modules/bootstrap/scss/_type.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_lists.scss", "webpack://landkit/./node_modules/bootstrap/scss/_images.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_image.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_box-shadow.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_container.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_breakpoints.scss", "webpack://landkit/./node_modules/bootstrap/scss/_containers.scss", "webpack://landkit/./node_modules/bootstrap/scss/_grid.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_grid.scss", "webpack://landkit/./node_modules/bootstrap/scss/_tables.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_table-variants.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_labels.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_form-text.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_form-control.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_transition.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_gradients.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_form-select.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_form-check.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_form-range.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_floating-labels.scss", "webpack://landkit/./node_modules/bootstrap/scss/forms/_input-group.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_forms.scss", "webpack://landkit/./node_modules/bootstrap/scss/_buttons.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_buttons.scss", "webpack://landkit/./node_modules/bootstrap/scss/_transitions.scss", "webpack://landkit/./node_modules/bootstrap/scss/_dropdown.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_caret.scss", "webpack://landkit/./node_modules/bootstrap/scss/_button-group.scss", "webpack://landkit/./node_modules/bootstrap/scss/_nav.scss", "webpack://landkit/./node_modules/bootstrap/scss/_navbar.scss", "webpack://landkit/./node_modules/bootstrap/scss/_card.scss", "webpack://landkit/./node_modules/bootstrap/scss/_accordion.scss", "webpack://landkit/./node_modules/bootstrap/scss/_breadcrumb.scss", "webpack://landkit/./node_modules/bootstrap/scss/_pagination.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_pagination.scss", "webpack://landkit/./node_modules/bootstrap/scss/_badge.scss", "webpack://landkit/./node_modules/bootstrap/scss/_alert.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_alert.scss", "webpack://landkit/./node_modules/bootstrap/scss/_progress.scss", "webpack://landkit/./node_modules/bootstrap/scss/_list-group.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_list-group.scss", "webpack://landkit/./node_modules/bootstrap/scss/_close.scss", "webpack://landkit/./node_modules/bootstrap/scss/_toasts.scss", "webpack://landkit/./node_modules/bootstrap/scss/_modal.scss", "webpack://landkit/./node_modules/bootstrap/scss/_tooltip.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_reset-text.scss", "webpack://landkit/./node_modules/bootstrap/scss/_popover.scss", "webpack://landkit/./node_modules/bootstrap/scss/_carousel.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_clearfix.scss", "webpack://landkit/./node_modules/bootstrap/scss/_spinners.scss", "webpack://landkit/./node_modules/bootstrap/scss/_offcanvas.scss", "webpack://landkit/./node_modules/bootstrap/scss/helpers/_colored-links.scss", "webpack://landkit/./node_modules/bootstrap/scss/helpers/_ratio.scss", "webpack://landkit/./node_modules/bootstrap/scss/helpers/_position.scss", "webpack://landkit/./node_modules/bootstrap/scss/helpers/_visually-hidden.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_visually-hidden.scss", "webpack://landkit/./node_modules/bootstrap/scss/helpers/_stretched-link.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_text-truncate.scss", "webpack://landkit/./node_modules/bootstrap/scss/mixins/_utilities.scss", "webpack://landkit/./node_modules/bootstrap/scss/utilities/_api.scss", "webpack://landkit/./src/scss/theme/utilities/_background.scss", "webpack://landkit/./src/scss/theme/utilities/_images.scss", "webpack://landkit/./src/scss/theme/utilities/_lift.scss", "webpack://landkit/./src/scss/theme/utilities/_overlay.scss", "webpack://landkit/./src/scss/theme/mixins/_overlay.scss", "webpack://landkit/./src/scss/theme/utilities/_position.scss", "webpack://landkit/./src/scss/theme/utilities/_shadows.scss", "webpack://landkit/./src/scss/theme/utilities/_sizing.scss", "webpack://landkit/./src/scss/theme/utilities/_type.scss", "webpack://landkit/./src/scss/theme/_reboot.scss", "webpack://landkit/./src/scss/theme/_type.scss", "webpack://landkit/./src/scss/theme/_images.scss", "webpack://landkit/./src/scss/theme/_grid.scss", "webpack://landkit/./src/scss/theme/_tables.scss", "webpack://landkit/./src/scss/theme/forms/_floating-labels.scss", "webpack://landkit/./src/scss/theme/forms/_form-check.scss", "webpack://landkit/./src/scss/theme/forms/_form-control.scss", "webpack://landkit/./src/scss/theme/forms/_form-group.scss", "webpack://landkit/./src/scss/theme/forms/_form-select.scss", "webpack://landkit/./src/scss/theme/forms/_input-group.scss", "webpack://landkit/./src/scss/theme/_buttons.scss", "webpack://landkit/./src/scss/theme/mixins/_buttons.scss", "webpack://landkit/./src/scss/theme/_dropdown.scss", "webpack://landkit/./src/scss/theme/_navbar.scss", "webpack://landkit/./src/scss/theme/_card.scss", "webpack://landkit/./src/scss/theme/_accordion.scss", "webpack://landkit/./src/scss/theme/_breadcrumb.scss", "webpack://landkit/./src/scss/theme/_pagination.scss", "webpack://landkit/./src/scss/theme/_badge.scss", "webpack://landkit/./src/scss/theme/mixins/_badge.scss", "webpack://landkit/./src/scss/theme/_alert.scss", "webpack://landkit/./src/scss/theme/_list-group.scss", "webpack://landkit/./src/scss/theme/_close.scss", "webpack://landkit/./src/scss/theme/_modal.scss", "webpack://landkit/./src/scss/theme/_popover.scss", "webpack://landkit/./src/scss/theme/_avatar.scss", "webpack://landkit/./src/scss/theme/_device.scss", "webpack://landkit/./src/scss/theme/_footer.scss", "webpack://landkit/./src/scss/theme/_icon.scss", "webpack://landkit/./src/scss/theme/_list.scss", "webpack://landkit/./src/scss/theme/_screenshot.scss", "webpack://landkit/./src/scss/theme/_section.scss", "webpack://landkit/./src/scss/theme/_shapes.scss", "webpack://landkit/./src/scss/theme/_sidenav.scss", "webpack://landkit/./src/scss/theme/vendor/_aos.scss", "webpack://landkit/./src/scss/theme/vendor/_bigpicture.scss", "webpack://landkit/./src/scss/theme/vendor/_choices.scss", "webpack://landkit/./src/scss/theme/vendor/_dropzone.scss", "webpack://landkit/./src/scss/theme/vendor/_feather.scss", "webpack://landkit/./src/scss/theme/vendor/_flickity.scss", "webpack://landkit/./src/scss/theme/vendor/_highlight.scss", "webpack://landkit/./src/scss/theme/vendor/_quill.scss"], "names": [], "mappings": "AAAA;;;;;ECKE,CDAC,MEFC,iBAAiC,CAAjC,mBAAiC,CAAjC,mBAAiC,CAAjC,iBAAiC,CAAjC,gBAAiC,CAAjC,mBAAiC,CAAjC,mBAAiC,CAAjC,kBAAiC,CAAjC,iBAAiC,CAAjC,iBAAiC,CAAjC,iBAAiC,CAAjC,sBAAiC,CAIjC,oBAAiC,CAAjC,sBAAiC,CAAjC,oBAAiC,CAAjC,iBAAiC,CAAjC,oBAAiC,CAAjC,mBAAiC,CAAjC,kBAAiC,CAAjC,iBAAiC,CAAjC,0BAAiC,CAAjC,kBAAiC,CAAjC,eAAiC,CAKnC,wMAAsD,CACtD,kGAAoD,CACpD,4EAAwC,CACzC,iBCGC,qBAAsB,CACvB,KAoCC,6CAFA,qBC1Ca,CDwCb,aC9Ba,CD0Bb,8BCuIsC,CCqElC,mBAvE+B,CFnInC,eC4IsB,CD3ItB,eC+IoB,CDnJpB,QAAS,CAQT,6BCjCa,CDmCd,GAWC,6BAA8B,CAC9B,QAAS,CAFT,aCrDgB,CDoDhB,aAAsB,CAItB,SC+KY,CD9Kb,eAGC,UC+EgB,CD9EjB,0CAcC,eCqGsB,CDpGtB,cAJA,mBCoI4B,CDrI5B,YCuIsB,CDhIvB,OEgKK,mBAvE+B,CFpFpC,OE2JK,mBAvE+B,CF/EpC,OEsJK,mBAvE+B,CF1EpC,OEiJK,mBAvE+B,CFrEpC,OE4IK,mBAvE+B,CFhEpC,OEuIK,gBAvE+B,CF3DpC,EAUC,mBADA,YCN4B,CDQ7B,yCAaC,WAAY,CADZ,wCAAiC,CAAjC,gCAAiC,CAEjC,qCAA0B,CAA1B,6BAA8B,CAC/B,QAOC,iBAAkB,CAClB,oBAFA,kBAEoB,CACrB,MAOC,iBAAkB,CACnB,SAMC,mBADA,YACmB,CACpB,wBAMC,eAAgB,CACjB,GAGC,eCSoB,CDRrB,GAKC,mBAAoB,CACpB,aAAc,CACf,WAMC,eAAgB,CACjB,SASC,eCdsB,CDevB,aE6CK,eAvE+B,CFmCpC,WAOC,yBADA,YG0SmC,CHxSpC,QE4BK,eAvE+B,CFuDnC,aAAc,CAFd,iBAAkB,CAGlB,uBAAwB,CACzB,IAEK,aAAc,CAAI,IAClB,SAAU,CAAI,EAMlB,aC/Ne,CDgOf,oBCnIoB,CDiItB,QAKI,aI1FiC,CJ2FjC,yBCtI6B,CDuI9B,4DAWC,aAAc,CACd,oBAAqB,CACtB,kBAYD,aAAoC,CAFpC,oCGmJoF,CD3KhF,aAvE+B,CFkGnC,0BAA2B,CAC5B,IAOC,aAAc,CEnCV,mBFqCJ,kBAAmB,CADnB,YAAa,CAEb,aE7GmC,CFyGrC,SAWI,aAAc,CE7CZ,iBAvE+B,CFqHjC,iBAAkB,CACnB,KAMD,qBADA,aGtQe,CDkNX,kBFqDiB,CAGrB,OACE,aAAc,CACf,IAOD,wBCxSgB,CIDd,qBLwSF,UChTa,CCgPT,kBAvE+B,CFqInC,mBC9JuB,CD6JzB,QE7DM,aAvE+B,CF8IjC,gBAFA,SClIkB,CDqInB,OASD,eAAgB,CACjB,QAOC,qBAAsB,CACvB,MASC,yBADA,mBACyB,CAC1B,QAKC,aCnVgB,CDkVhB,qBCxG2B,CDuG3B,kBCvG2B,CD0G3B,eAAgB,CACjB,GAQC,kBAAmB,CACnB,+BAAgC,CACjC,2BAUC,eAFA,oBAEe,CAChB,MAQC,oBAAqB,CACtB,OAOC,eAAgB,CACjB,iCAQC,SAAU,CACX,sCAUC,mBAAoB,CEpKhB,iBAvE+B,CF6OnC,oBAHA,QAGoB,CACrB,cAKC,mBAAoB,CACrB,cAKC,cAAe,CAChB,OAKC,gBAAiB,CAHnB,gBAOI,SAAU,CACX,0CAOD,YAAa,CACd,gDAWC,yBAA0B,CAJ5B,4GAQM,cAAe,CAChB,mBAQH,kBADA,SACkB,CACnB,SAKC,eAAgB,CACjB,SAaC,SADA,QAAS,CAFT,WAAY,CACZ,SAES,CACV,OAQC,UAAW,CE1PP,gBAvE+B,CFuUnC,oBAHA,mBGGiC,CHJjC,SAAU,CADV,UAKoB,CAPtB,SAUI,UAAW,CACZ,+OAaD,SAAU,CACX,4BAGC,WAAY,CACb,cAUC,6BADA,mBAC6B,CAC9B,4BAmBC,uBAAwB,CACzB,+BAKC,SAAU,CACX,uBAMC,YAAa,CACd,6BAOC,0BADA,YAC0B,CAC3B,OAKC,oBAAqB,CACtB,OAKC,QAAS,CACV,QAQC,eADA,iBACe,CAChB,SAQC,uBAAwB,CACzB,SAQC,sBAAwB,CACzB,MEjXK,mBAvE+B,CItJnC,eL+JsB,CK9JvB,WJ4NK,kBAvE+B,CI/IjC,eL2hBqB,CK7hBvB,WJwNI,mBAvE+B,CI/IjC,eL2hBqB,CK7hBvB,WJwNI,cAvE+B,CI/IjC,eL2hBqB,CK7hBvB,WJwNI,mBAvE+B,CI/IjC,eL2hBqB,CKrgBxB,4BCtDC,gBADA,cACgB,CD2DjB,kBAEC,oBAAqB,CADvB,mCAII,kBHgc+B,CG/bhC,YJqLG,eAvE+B,CInGnC,wBAAyB,CAC1B,YJyKK,kBIrKJ,kBJ8FmC,CI/FrC,wBAKI,eAAgB,CACjB,mBAOD,cJyJI,gBAvE+B,CIpFnC,kBLrBW,CKoBX,gBL9EgB,CK6ElB,0BAOI,oBAAqB,CE5FxB,0BCKC,YAHA,cAGY,CDLb,eAMC,qBPJa,COKb,wBPFgB,CIKd,qBJyIoB,CS9IlB,+CTOS,COPb,cCAY,CDQb,QAQC,oBAAqB,CACtB,YAIC,cADA,mBACc,CACf,gBAIC,cNmNI,eD1OY,COwBjB,oFGlCC,iBADA,iBAAkB,CADlB,uCAAsE,CADtE,wCAAuE,CADvE,UAIiB,CCwDf,yBC5CE,yBACE,eZiHG,CYhHJ,CD0CH,yBC5CE,uCACE,eZkHG,CYjHJ,CD0CH,yBC5CE,qDACE,eZmHG,CYlHJ,CD0CH,0BC5CE,mEACE,gBZoHI,CYnHL,CChBL,KCAA,oBAAwC,CACxC,eAAwC,CACxC,YAAa,CACb,cAAe,CAGf,wCADA,wCAAqE,CADrE,sCAEoE,CDNpE,OCeA,aAAc,CAKd,8BAHA,cAAe,CAEf,uCAAoE,CADpE,wCAAqE,CAFrE,UAIwD,CA8CpD,KACE,WAAY,CACb,iBAlCL,aAAc,CACd,UAAW,CAcX,cACE,aAAc,CACd,UAAoB,CAFtB,cACE,aAAc,CACd,SAAoB,CAFtB,cACE,aAAc,CACd,eAAoB,CAFtB,cACE,aAAc,CACd,SAAoB,CAFtB,cACE,aAAc,CACd,SAAoB,CAFtB,cACE,aAAc,CACd,eAAoB,CACrB,UAlBD,aAAc,CACd,UAAW,CAiDN,OA3DH,aAAc,CACd,cAAmC,CA8D7B,OA/DN,aAAc,CACd,eAAmC,CA8D7B,OA/DN,aAAc,CACd,SAAmC,CA8D7B,OA/DN,aAAc,CACd,eAAmC,CA8D7B,OA/DN,aAAc,CACd,eAAmC,CA8D7B,OA/DN,aAAc,CACd,SAAmC,CA8D7B,OA/DN,aAAc,CACd,eAAmC,CA8D7B,OA/DN,aAAc,CACd,eAAmC,CA8D7B,OA/DN,aAAc,CACd,SAAmC,CA8D7B,QA/DN,aAAc,CACd,eAAmC,CA8D7B,QA/DN,aAAc,CACd,eAAmC,CA8D7B,QA/DN,aAAc,CACd,UAAmC,CAgE5B,UAlDT,oBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,WAxDV,qBAA8C,CAwDpC,WAxDV,qBAA8C,CA0DnC,WAWH,eAAwC,CACzC,WAIC,eAAwC,CAP1C,WAEE,oBAAwC,CACzC,WAIC,oBAAwC,CAP1C,WAEE,mBAAwC,CACzC,WAIC,mBAAwC,CAP1C,WAEE,oBAAwC,CACzC,WAIC,oBAAwC,CAP1C,WAEE,kBAAwC,CACzC,WAIC,kBAAwC,CAP1C,WAEE,oBAAwC,CACzC,WAIC,oBAAwC,CAP1C,WAEE,kBAAwC,CACzC,WAIC,kBAAwC,CAP1C,WAEE,oBAAwC,CACzC,WAIC,oBAAwC,CAP1C,WAEE,kBAAwC,CACzC,WAIC,kBAAwC,CAP1C,WAEE,kBAAwC,CACzC,WAIC,kBAAwC,CAP1C,aAEE,kBAAwC,CACzC,aAIC,kBAAwC,CAP1C,aAEE,kBAAwC,CACzC,aAIC,kBAAwC,CAP1C,aAEE,kBAAwC,CACzC,aAIC,kBAAwC,CAP1C,aAEE,mBAAwC,CACzC,aAIC,mBAAwC,CAP1C,aAEE,mBAAwC,CACzC,aAIC,mBAAwC,CAP1C,aAEE,mBAAwC,CACzC,aAIC,mBAAwC,CAP1C,aAEE,mBAAwC,CACzC,aAIC,mBAAwC,CHxD9C,yBGQE,QACE,WAAY,CACb,oBAlCL,aAAc,CACd,UAAW,CAcX,iBACE,aAAc,CACd,UAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CACrB,aAlBD,aAAc,CACd,UAAW,CAiDN,UA3DH,aAAc,CACd,cAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,UAAmC,CAgE5B,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,eAAwC,CACzC,iBAIC,eAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CACzC,CHzDL,yBGQE,QACE,WAAY,CACb,oBAlCL,aAAc,CACd,UAAW,CAcX,iBACE,aAAc,CACd,UAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CACrB,aAlBD,aAAc,CACd,UAAW,CAiDN,UA3DH,aAAc,CACd,cAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,UAAmC,CAgE5B,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,eAAwC,CACzC,iBAIC,eAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CACzC,CHzDL,yBGQE,QACE,WAAY,CACb,oBAlCL,aAAc,CACd,UAAW,CAcX,iBACE,aAAc,CACd,UAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CACrB,aAlBD,aAAc,CACd,UAAW,CAiDN,UA3DH,aAAc,CACd,cAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,UAAmC,CAgE5B,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,eAAwC,CACzC,iBAIC,eAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CACzC,CHzDL,0BGQE,QACE,WAAY,CACb,oBAlCL,aAAc,CACd,UAAW,CAcX,iBACE,aAAc,CACd,UAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,SAAoB,CAFtB,iBACE,aAAc,CACd,eAAoB,CACrB,aAlBD,aAAc,CACd,UAAW,CAiDN,UA3DH,aAAc,CACd,cAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,eAAmC,CA8D7B,UA/DN,aAAc,CACd,SAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,UAAmC,CAgE5B,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,eAAwC,CACzC,iBAIC,eAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,iBAEE,kBAAwC,CACzC,iBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CACzC,CHzDL,0BGQE,SACE,WAAY,CACb,qBAlCL,aAAc,CACd,UAAW,CAcX,kBACE,aAAc,CACd,UAAoB,CAFtB,kBACE,aAAc,CACd,SAAoB,CAFtB,kBACE,aAAc,CACd,eAAoB,CAFtB,kBACE,aAAc,CACd,SAAoB,CAFtB,kBACE,aAAc,CACd,SAAoB,CAFtB,kBACE,aAAc,CACd,eAAoB,CACrB,cAlBD,aAAc,CACd,UAAW,CAiDN,WA3DH,aAAc,CACd,cAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,SAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,SAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,eAAmC,CA8D7B,WA/DN,aAAc,CACd,SAAmC,CA8D7B,YA/DN,aAAc,CACd,eAAmC,CA8D7B,YA/DN,aAAc,CACd,eAAmC,CA8D7B,YA/DN,aAAc,CACd,UAAmC,CAgE5B,cAlDT,aAA4B,CAwDlB,cAxDV,oBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,eAxDV,qBAA8C,CAwDpC,eAxDV,qBAA8C,CA0DnC,mBAWH,eAAwC,CACzC,mBAIC,eAAwC,CAP1C,mBAEE,oBAAwC,CACzC,mBAIC,oBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,oBAAwC,CACzC,mBAIC,oBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,oBAAwC,CACzC,mBAIC,oBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,oBAAwC,CACzC,mBAIC,oBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,mBAEE,kBAAwC,CACzC,mBAIC,kBAAwC,CAP1C,qBAEE,kBAAwC,CACzC,qBAIC,kBAAwC,CAP1C,qBAEE,kBAAwC,CACzC,qBAIC,kBAAwC,CAP1C,qBAEE,kBAAwC,CACzC,qBAIC,kBAAwC,CAP1C,qBAEE,mBAAwC,CACzC,qBAIC,mBAAwC,CAP1C,qBAEE,mBAAwC,CACzC,qBAIC,mBAAwC,CAP1C,qBAEE,mBAAwC,CACzC,qBAIC,mBAAwC,CAP1C,qBAEE,mBAAwC,CACzC,qBAIC,mBAAwC,CACzC,CCpHT,OACE,kBAAwC,CACxC,yBAAsD,CACtD,gCAA8D,CAC9D,6BAAwD,CACxD,+BAA4D,CAC5D,4BAAsD,CACtD,8BAA0D,CAC1D,2BAAoD,CAMpD,qBAFA,afKa,CeNb,kBf4DW,Ce1DX,kBbogB+B,CavgB/B,UfDgB,CeTlB,yBAuBI,mCAA8D,CAC9D,uBf4Hc,Ce3Hd,wDAHA,mBAGyF,CAzB7F,aA6BI,sBAAuB,CA7B3B,aAiCI,qBAAsB,CAjC1B,uCAsCI,gCbogBsC,CangBvC,aASD,gBAAiB,CAClB,4BAUG,cbodgC,CandjC,gCAeC,kBAAmC,CAFvC,kCAMM,kBfqEY,CepEb,oCAOD,qBAAsB,CACvB,yCASC,+CAAsD,CACtD,mCAAyE,CAC1E,cAQD,8CAAsD,CACtD,kCAAuE,CACxE,4BAQG,6CAAsD,CACtD,iCAAqE,CCxHvE,eAME,qBAAwC,CACxC,6BAAwD,CACxD,gCAA8D,CAC9D,4BAAsD,CACtD,+BAA4D,CAC5D,2BAAoD,CACpD,8BAA0D,CAG1D,qBADA,aACwE,CAf1E,iBAME,qBAAwC,CACxC,6BAAwD,CACxD,gCAA8D,CAC9D,4BAAsD,CACtD,+BAA4D,CAC5D,2BAAoD,CACpD,8BAA0D,CAG1D,qBADA,aACwE,CAf1E,eAME,qBAAwC,CACxC,6BAAwD,CACxD,gCAA8D,CAC9D,4BAAsD,CACtD,+BAA4D,CAC5D,2BAAoD,CACpD,8BAA0D,CAG1D,qBADA,aACwE,CAf1E,YAME,qBAAwC,CACxC,6BAAwD,CACxD,gCAA8D,CAC9D,4BAAsD,CACtD,+BAA4D,CAC5D,2BAAoD,CACpD,8BAA0D,CAG1D,qBADA,aACwE,CAf1E,eAME,qBAAwC,CACxC,6BAAwD,CACxD,gCAA8D,CAC9D,4BAAsD,CACtD,+BAA4D,CAC5D,2BAAoD,CACpD,8BAA0D,CAG1D,qBADA,aACwE,CAf1E,cAME,qBAAwC,CACxC,6BAAwD,CACxD,gCAA8D,CAC9D,4BAAsD,CACtD,+BAA4D,CAC5D,2BAAoD,CACpD,8BAA0D,CAG1D,qBADA,aACwE,CAf1E,aAME,qBAAwC,CACxC,6BAAwD,CACxD,gCAA8D,CAC9D,4BAAsD,CACtD,+BAA4D,CAC5D,2BAAoD,CACpD,8BAA0D,CAG1D,qBADA,aACwE,CAf1E,YAME,qBAAwC,CACxC,6BAAwD,CACxD,6BAA8D,CAC9D,4BAAsD,CACtD,4BAA4D,CAC5D,2BAAoD,CACpD,2BAA0D,CAG1D,qBADA,UACwE,CACzE,kBDiIG,iCADA,eACiC,CJvEnC,4BIqEA,qBAEE,iCADA,eACiC,CAClC,CJxED,4BIqEA,qBAEE,iCADA,eACiC,CAClC,CJxED,4BIqEA,qBAEE,iCADA,eACiC,CAClC,CJxED,6BIqEA,qBAEE,iCADA,eACiC,CAClC,CJxED,6BIqEA,sBAEE,iCADA,eACiC,CAClC,CEhJL,YACE,mBfypB2C,CeppB5C,gBhBiPK,iBAvE+B,CgB/JnC,gBAJA,eAAgB,CADhB,mCduK8D,CcxK9D,gCjBkLoB,CiB1KrB,mBhBoOK,oBgBhOJ,mCd4J8D,Cc7J9D,gChB0JmC,CgBvJpC,mBhB8NK,oBgB1NJ,mCdsJ8D,CcvJ9D,gChBoJmC,CgBjJpC,WC1BC,cjBkPI,eAvE+B,CiB/KnC,iBlBYgB,CkBPjB,cCMC,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAFhB,2BAA4B,CAD5B,qBnBFa,CmBIb,wBnBFgB,CIMd,qBJyIoB,CS9IlB,eT0TiB,CmB5TrB,anBSa,CmBhBb,aAAc,ClBsPV,mBAvE+B,CkB1KnC,enBmLsB,CmBlLtB,enBsLoB,CmB1LpB,wBnB2R2B,CoBlRvB,qEDVJ,UjB+sBgG,CkBjsB5F,uCDhBN,cCiBQ,eAAgB,CD2FvB,CA5GD,yBAqBI,eAAgB,CArBpB,wDAwBM,cAAe,CAxBrB,oBA+BI,qBnBxBW,CmByBX,oBnBba,CSTX,qBUoBF,anBbW,CmBgBX,SnBmQ6B,CmBpSjC,2CA+CI,YAAmE,CA/CvE,gCAoDI,anBvCc,CmByCd,SAAU,CAtDd,2BAoDI,anBvCc,CmByCd,SAAU,CAtDd,+CAgEI,wBnBvDc,CmB0Dd,SAAU,CAnEd,oCA0EI,0BnBoNyB,CqB9R3B,wBrBQgB,CmBwEd,cAAe,CAFf,oBAAqB,CAGrB,2BnBmEc,CmBlEd,eAAgB,CAPhB,anB1DW,CmBwDX,yBnBqNyB,CmBpNzB,yBnBoNyB,CmBtNzB,wBnBsNyB,CmBjNzB,mBAAoB,CCjElB,6HlBsnB6I,CkBlnB7I,uCDhBN,oCCiBQ,eAAgB,CDmErB,CApFH,yEAuFI,wBhBqEiC,CgB5JrC,0CA6FI,0BnBiMyB,CqB9R3B,wBrBQgB,CmB2Fd,cAAe,CAFf,oBAAqB,CAGrB,2BnBgDc,CmB/Cd,eAAgB,CAPhB,anB7EW,CmB2EX,yBnBkMyB,CmBjMzB,yBnBiMyB,CmBnMzB,wBnBmMyB,CmB9LzB,mBAAoB,CCpFlB,qIlBsnBkI,CkBtnBlI,6HlBsnB6I,CkBlnB7I,uCDhBN,0CCiBQ,uBAAY,CAAZ,eAAgB,CDsFrB,CAvGH,+EA0GI,wBhBkDiC,CgBjDlC,wBAeD,4BAA6B,CAE7B,4CAHA,anBxGa,CmBmGb,aAAc,CAId,enBqEoB,CmBtEpB,eAAgB,CADhB,kBAA2B,CAD3B,UAOmC,CATrC,gFAcI,eADA,eACe,CAChB,iBfnHC,sBHwOE,mBAvE+B,CkBnCnC,uChBsC8D,CgBrC9D,qBnBUsB,CmBZxB,uCASI,uBnBkJqB,CmBnJrB,sBnBmJyB,CmBlJzB,uBAFA,qBnBoJyB,CmB3J7B,6CAeI,uBnB4IqB,CmB7IrB,sBnB6IyB,CmB5IzB,uBAFA,qBnB8IyB,CmB3I1B,iBf7IC,sBHwOE,mBAvE+B,CkBhBnC,uChBmB8D,CgBlB9D,uBnBTsB,CmBOxB,uCASI,yBnBoIqB,CmBrIrB,wBnBqI2B,CmBpI3B,yBAFA,uBnBsI2B,CmB7I/B,6CAeI,yBnB8HqB,CmB/HrB,wBnB+H2B,CmB9H3B,yBAFA,uBnBgI2B,CmB7H5B,sBAQC,uChBJ4D,CgBEhE,yBAMI,uChBR4D,CgBEhE,yBAUI,uChBZ4D,CgBa7D,oBAMD,WAAY,CADZ,cAAe,CAEf,gBnBsF4B,CmBzF9B,mDAMI,cAAe,CANnB,uCfrLI,sBe+LA,YnBtDoB,CmB4CxB,0CfrLI,sBeoMA,YnB3DoB,CmB6DrB,aGnMD,uBAAY,CAAZ,oBAAY,CAAZ,gBARA,qBtBHa,CsBIb,wQnBqEgF,CmBnEhF,wCpBixBqE,CoBlxBrE,2BAA4B,CAE5B,uBtBiW2B,CsBhW3B,wBtBNgB,CIMd,qBJyIoB,CS9IlB,eT0TiB,CsB3TrB,atBQa,CsBfb,aAAc,CrBqPV,mBAvE+B,CqBzKnC,etBkLsB,CsBjLtB,etBqLoB,CsBzLpB,wBtB0R2B,CsB3R3B,UAegB,CAjBlB,mBAoBI,oBtBFa,CSTX,qBaYF,StB8Q6B,CsBnSjC,0DAiCI,sBADA,qBACsB,CAjC1B,sBAsCI,wBtB9Bc,CsBRlB,4BA4CI,iBAAkB,CAClB,yBtB7BW,CsB8BZ,gBrBwMG,oBqBnMJ,uBtBkP+B,CsBjP/B,iBtBkP2B,CsBpP3B,oBrB6HmC,CqBzHpC,gBrBgMK,oBqB5LJ,uBtBgP+B,CsB/O/B,mBtBgP6B,CsBlP7B,oBrBsHmC,CqBlHpC,YC7DC,aAAc,CAGd,sBAFA,iBrBotB2E,CqBntB3E,mBrBqtB+C,CqBxtBjD,8BAOI,UAAW,CACX,mBAA2C,CAC5C,kBAaD,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAChB,gCAAc,CAAd,mBANA,wBvBPgB,CuBShB,uBAA2B,CAD3B,2BAA4B,CAE5B,uBAAwB,CACxB,kBvB+TmC,CuBtUnC,UrBusB2C,CqBtsB3C,eAA6D,CAC7D,kBAAmB,CAHnB,SAUmB,CAXrB,iCnBGI,qBJyIoB,CuB5IxB,8BAoBI,iBrBksByC,CqBttB7C,yBAwBI,WvB0SiC,CuBlUrC,wBA4BI,wBvB4SiC,CuB1SjC,gBADA,SvB8SoC,CuB3UxC,0BAkCI,wBvB3Ba,CuB4Bb,oBvB5Ba,CuBPjB,yCAyCQ,4PpB2B0E,CoBpElF,sCAiDQ,oKpBmB0E,CoBpElF,+CAuDI,wBvBhDa,CuBsDX,uPALF,oBpBY8E,CoBpElF,2BAmEI,WAAY,CACZ,WAFA,mBrBmqByC,CqBruB7C,2FA4EM,UrBypBuC,CqBxpBxC,kBAMH,cvBkP+B,CuBjPhC,aAOC,iBvB2PoD,CuB5PtD,+BAMI,oKpB5B8E,CoB6B9E,qBAAgC,CnB9FhC,oBJkVsB,CuBtPtB,iBAA4C,CH/F1C,gDG8FF,YrBopBkE,CkB9uBhE,uCGsFN,+BHrFQ,eAAgB,CG6GrB,CAxBH,4EAYM,oKpBlC4E,CoBsBlF,uCAgBM,wBpBtC4E,CoB6C7E,mBAKH,oBAAqB,CACrB,iBrBknBoC,CqBjnBrC,WAIC,kBAAsB,CACtB,oBAFA,iBAEoB,CAHtB,mDASM,WAAY,CACZ,YAFA,mBrBwe2B,CqBre5B,YC1IH,uBAAY,CAAZ,oBAAY,CAAZ,gBADA,4BAA6B,CAF7B,WrB4K2B,CqB3K3B,SAAU,CAFV,UAIgB,CALlB,kBAQI,SAAU,CARd,wCAY8B,8BxBsRG,CwBlSjC,oCAa8B,8BxBqRG,CwBlSjC,8BAiBI,QAAS,CAjBb,kCA6BI,uBAAY,CAAZ,gBH/BF,wBrBmBe,CwBQb,QtByyBwC,CErzBxC,kBFszB2C,CO3zBzC,2CTOS,CwBOX,WtByyB2C,CsBxyB3C,kBAAqE,CJbnE,8GlB+zBkI,CkB/zBlI,sGlB+zBkI,CsBpzBpI,UAQgB,CJfd,uCIdN,kCJeQ,uBAAY,CAAZ,eAAgB,CImBrB,CAlCH,yCHFE,wBlBuJmC,CqBrJrC,2CAyCI,wBxBjCc,CwBkCd,wBAAyB,CpB7BzB,kBF+yBkC,COpzBhC,+Ce+BF,iBAAkB,CAClB,ctBkxBqC,CsBpxBrC,YtBmxBmC,CsBpxBnC,UxBtBW,CwBff,8BAuDI,oBAAY,CAAZ,gBHzDF,wBrBmBe,CwBkCb,QtB+wBwC,CErzBxC,kBFszB2C,CO3zBzC,2CTOS,CwBkCX,WtB8wB2C,CkBrzBzC,2GlB+zBkI,CkB/zBlI,sGlB+zBkI,CsBzxBpI,UAOgB,CJzCd,uCIdN,8BJeQ,oBAAY,CAAZ,eAAgB,CI6CrB,CA5DH,qCHFE,wBlBuJmC,CqBrJrC,8BAmEI,wBxB3Dc,CwB4Dd,wBAAyB,CpBvDzB,kBF+yBkC,COpzBhC,+CeyDF,iBAAkB,CAClB,ctBwvBqC,CsB1vBrC,YtByvBmC,CsB1vBnC,UxBhDW,CwBff,qBA0EI,mBAAoB,CA1ExB,2CA6EM,wBxBnEY,CwBVlB,uCAiFM,wBxBvEY,CwBwEb,eCvFH,iBAAkB,CADpB,yDAKI,yBtBkL4D,CsBjL5D,oBzB4RyB,CyBlS7B,qBAgBI,4BAA6C,CAH7C,WAAY,CADZ,MAAO,CAEP,oBzBoRyB,CyBnRzB,mBAAoB,CALpB,iBAAkB,CAClB,KAAM,CAMN,oBAAqB,CLDnB,4DlBg1B8E,CkB50B9E,uCKpBN,qBLqBQ,eAAgB,CKFrB,CAnBH,+CAwBM,iBAAkB,CAxBxB,0CAwBM,iBAAkB,CAxBxB,0DA8BM,uBADA,oBvBg0BmC,CuB71BzC,wFA8BM,uBADA,oBvBg0BmC,CuB71BzC,8CAmCM,uBADA,oBvB2zBmC,CuB71BzC,4BAyCI,uBADA,oBvBqzBqC,CuB71BzC,gEAgDM,SzB8UyB,CyB7UzB,4BzB8U4C,CyB/XlD,sIAgDM,SzB8UyB,CyB7UzB,4BzB8U4C,CyB/XlD,oDAuDM,SzBuUyB,CyBtUzB,4BzBuU4C,CyBtU7C,aCjDH,mBAAoB,CAFpB,YAAa,CACb,cAAe,CAFf,iBAAkB,CAIlB,UAAW,CALb,qDAUI,aAAc,CAEd,YAHA,iBAAkB,CAElB,QACY,CAZhB,iEAkBI,SAAU,CAlBd,kBAyBI,iBAAkB,CAClB,SAAU,CA1Bd,wBA6BM,SAAU,CACX,kBAYH,kBAAmB,CAQnB,qB1B3Ca,C0B4Cb,wB1B1CgB,CIMd,sBsBgCF,a1BlCgB,C0B4BhB,YAAa,CzB8MT,mBAvE+B,CyBnInC,e1B4IsB,C0B3ItB,e1B+IoB,C0BlJpB,wB1BmP2B,C0B9O3B,iBAAkB,CAClB,kB1BuGsB,C0BnGvB,kHtBtCG,sBHwOE,mBAvE+B,CyB/GnC,uB1BuFsB,C0BpFvB,kHtBrDG,sBHwOE,mBAvE+B,CyBtGnC,qB1B8EsB,C0B3EvB,0DAIC,oBAAsE,CAWxE,iUtB5DI,6BADA,yBsBwE8B,CAXlC,0ItB9CI,4BADA,wBsBqE8B,CAD9B,gBAC8B,CAF4B,gBCrF1D,cALA,YAAa,C1BiOX,eAvE+B,C0BxJjC,iBzB0nB0C,CyB3nB1C,U3BDa,C2BMd,eAaC,oC3BnBa,CIPb,sBuByBA,U3BjCW,C2B2BX,YAAa,C1BqNX,kBAvE+B,C0B3IjC,gBAAiB,CAFjB,cAAe,CACf,oBzB+kC2C,CyBplC3C,iBAAkB,CAClB,QAAS,CACT,S3BuHoB,C2BtJpB,8HA8CE,aAAc,CA9ChB,0DAoDE,oB3BhCW,C2BpBb,sEA+DI,oB3B3CS,C2B4CT,uC3B5CS,C2BpBb,wDAiFE,oB3B7DW,C2BpBb,oEA8FI,oB3B1ES,C2B2ET,uC3B3ES,C2BpBb,kEAsGE,oB3BlFW,C2BpBb,kFAyGI,wB3BrFS,C2BpBb,8EA6GI,uC3BzFS,C2BpBb,sGAiHI,a3B7FS,C2B8FV,qDAKD,gBAAiB,CAvHnB,sKA+HI,SAAU,CA/Hd,8LAoII,SAAU,CACX,kBA5GH,cALA,YAAa,C1BiOX,eAvE+B,C0BxJjC,iBzB0nB0C,CyB3nB1C,U3BEY,C2BGb,iBAaC,mC3BhBY,CIVZ,sBuByBA,U3BjCW,C2B2BX,YAAa,C1BqNX,kBAvE+B,C0B3IjC,gBAAiB,CAFjB,cAAe,CACf,oBzB+kC2C,CyBplC3C,iBAAkB,CAClB,QAAS,CACT,S3BuHoB,C2BtJpB,8IA8CE,aAAc,CA9ChB,8DAoDE,oB3B7BU,C2BvBZ,0EA+DI,oB3BxCQ,C2ByCR,sC3BzCQ,C2BvBZ,4DAiFE,oB3B1DU,C2BvBZ,wEA8FI,oB3BvEQ,C2BwER,sC3BxEQ,C2BvBZ,sEAsGE,oB3B/EU,C2BvBZ,sFAyGI,wB3BlFQ,C2BvBZ,kFA6GI,sC3BtFQ,C2BvBZ,0GAiHI,a3B1FQ,C2B2FT,uDAKD,gBAAiB,CAvHnB,8KAiII,SAAU,CAjId,sMAoII,SAAU,CACX,KC3HL,4BAA6B,CAC7B,4BAA2C,CxBEzC,qBJyIoB,C4BnJtB,a5BYa,C4BPb,cAA2C,CAT3C,oBAAqB,C3BsPjB,mBAvE+B,C2B7KnC,e5BuLoB,C4BtLpB,e5ByLoB,C6BlEpB,wB7BmK2B,C4BxR3B,iBAAkB,CRMd,8HQDJ,wBAAiB,CAAjB,qBAAiB,CAAjB,gBAAiB,CAFjB,qB1BynBiJ,CkBlnB7I,uCQhBN,KRiBQ,eAAgB,CQ6BvB,CA9CD,WAkBI,a5BDW,C4BEX,oBAA6D,CAC9D,iCAKC,gBADA,S5B4Q6B,C4B1Q9B,uEnBhBG,eT6SsB,C4B3R1B,+FnBlBI,oBT6SsB,C4BvT5B,mDnBUM,gBmBiCF,W1B2kB6B,C0B5kB7B,mBAEwB,CACzB,aP7CD,wBrBmBe,C6BDf,oB7BCe,CSTX,gBoBMJ,U7BsSmB,C6B7RlB,oERzBD,wBlB4JmC,C0BpIjC,qBAFA,UAW4F,CAR7F,iDpBfG,2CoBuB0F,CAK7F,0IAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,wKpB9BI,2CoB2CmG,CAKpG,4CAMD,wB7B7Ca,C6BgDb,qBAJA,U7B5Ca,C6BiDd,eRpED,wBrBcgB,C6BIhB,oB7BJgB,CSJZ,gBoBMJ,U7BsSmB,C6B7RlB,0ERzBD,wBlB4JmC,C0BpIjC,qBAFA,UAW4F,CAR7F,qDpBfG,4CoBuB0F,CAK7F,oJAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,kLpB9BI,4CoB2CmG,CAKpG,gDAMD,wB7BlDc,C6BqDd,qBAJA,U7BjDc,C6BsDf,aRpED,wBrBsBe,C6BJf,oB7BIe,CSZX,gBoBMJ,U7BsSmB,C6B7RlB,oERzBD,wBlB4JmC,C0BpIjC,qBAFA,UAW4F,CAR7F,iDpBfG,2CoBuB0F,CAK7F,0IAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,wKpB9BI,2CoB2CmG,CAKpG,4CAMD,wB7B1Ca,C6B6Cb,qBAJA,U7BzCa,C6B8Cd,URpED,wBrBuBY,C6BLZ,oB7BKY,CSbR,gBoBMJ,U7BsSmB,C6B7RlB,2DRzBD,wBlB4JmC,C0BpIjC,qBAFA,UAW4F,CAR7F,2CpBfG,4CoBuB0F,CAK7F,2HAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,yJpB9BI,4CoB2CmG,CAKpG,sCAMD,wB7BzCU,C6B4CV,qBAJA,U7BxCU,C6B6CX,aRpED,wBrBwBe,C6BNf,oB7BMe,CSdX,gBoBMJ,a7BsSmB,C6B7RlB,oERzBD,wBlBuJmC,C0B/HjC,qBAFA,aAW4F,CAR7F,iDpBfG,4CoBuB0F,CAK7F,0IAQC,wB1ByGiC,C0BtGjC,qBAJA,a1B0GiC,C0B/GnC,wKpB9BI,4CoB2CmG,CAKpG,4CAMD,wB7BxCa,C6B2Cb,qBAJA,a7BvCa,C6B4Cd,YRpED,wBrByBc,C6BPd,oB7BOc,CSfV,gBoBMJ,U7BsSmB,C6B7RlB,iERzBD,wBlB4JmC,C0BpIjC,qBAFA,UAW4F,CAR7F,+CpBfG,2CoBuB0F,CAK7F,qIAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,mKpB9BI,2CoB2CmG,CAKpG,0CAMD,wB7BvCY,C6B0CZ,qBAJA,U7BtCY,C6B2Cb,WRpED,wBrBQgB,C6BUhB,oB7BVgB,CSEZ,gBoBMJ,a7BsSmB,C6B7RlB,8DRzBD,wBlBuJmC,C0B/HjC,qBAFA,aAW4F,CAR7F,6CpBfG,4CoBuB0F,CAK7F,gIAQC,wB1ByGiC,C0BtGjC,qBAJA,a1B0GiC,C0B/GnC,8JpB9BI,4CoB2CmG,CAKpG,wCAMD,wB7BxDc,C6B2Dd,qBAJA,a7BvDc,C6B4Df,URpED,wBrBgBgB,C6BEhB,oB7BFgB,CSNZ,gBoBMJ,U7BsSmB,C6B7RlB,2DRzBD,wBlB4JmC,C0BpIjC,qBAFA,UAW4F,CAR7F,2CpBfG,0CoBuB0F,CAK7F,2HAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,yJpB9BI,0CoB2CmG,CAKpG,sCAMD,wB7BhDc,C6BmDd,qBAJA,U7B/Cc,C6BoDf,mBRpED,wBrBqBqB,C6BHrB,oB7BGqB,CSXjB,gBoBMJ,U7BsSmB,C6BjSnB,yBRrBA,wBlB4JmC,C0BpIjC,qBAFA,U1BsIiC,C0BnIlC,6DRzBD,wBlB4JmC,C0B7HjC,oB1B6HiC,CMlJ/B,6CoBmBF,UAI4F,CAK7F,wKAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,sMpB9BI,4CoB2CmG,CAKpG,wDAMD,wB7B3CmB,C6B8CnB,qBAJA,U7B1CmB,C6B+CpB,WRpED,wBrBiBa,C6BCb,oB7BDa,CSPT,gBoBMJ,U7BsSmB,C6B7RlB,8DRzBD,wBlB4JmC,C0BpIjC,qBAFA,UAW4F,CAR7F,6CpBfG,yCoBuB0F,CAK7F,gIAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,8JpB9BI,yCoB2CmG,CAKpG,wCAMD,wB7B/CW,C6BkDX,qBAJA,U7B9CW,C6BmDZ,WAlDD,iB7BXa,CSGT,gBoBMJ,a7BsSmB,C6B7RlB,8DRzBD,qBlBuJmC,C0B/HjC,kBAFA,aAW4F,CAR7F,6CpBfG,4CoBuB0F,CAK7F,gIAQC,qB1ByGiC,C0BtGjC,kBAJA,a1B0GiC,C0B/GnC,8JpB9BI,4CoB2CmG,CAKpG,wCAMD,qB7BzDW,C6B4DX,kBAJA,a7BxDW,C6B6DZ,qBAaD,qBADA,a7B7De,C6BgEf,2BAEE,wB7BlEa,C6BmEb,qBAFA,U7BjEa,C6BoEd,iEAIC,qC7BxEa,C6ByEd,iLAQC,wB7BjFa,C6BkFb,qBAFA,U7BhFa,C6B2Ef,+MpBpFI,0CTSW,C6B2FZ,4DAMD,6BADA,aAC6B,CAC9B,uBApCD,qBADA,a7BlEgB,C6BqEhB,6BAEE,wB7BvEc,C6BwEd,qBAFA,U7BtEc,C6ByEf,qEAIC,sC7B7Ec,C6B8Ef,2LAQC,wB7BtFc,C6BuFd,qBAFA,U7BrFc,C6BgFhB,yNpBpFI,2CTIY,C6BgGb,gEAMD,6BADA,aAC6B,CAC9B,qBApCD,qBADA,a7B1De,C6B6Df,2BAEE,wB7B/Da,C6BgEb,qBAFA,U7B9Da,C6BiEd,iEAIC,sC7BrEa,C6BsEd,iLAQC,wB7B9Ea,C6B+Eb,qBAFA,U7B7Ea,C6BwEf,+MpBpFI,2CTYW,C6BwFZ,4DAMD,6BADA,aAC6B,CAC9B,kBApCD,qBADA,a7BzDY,C6B4DZ,wBAEE,wB7B9DU,C6B+DV,qBAFA,U7B7DU,C6BgEX,2DAIC,uC7BpEU,C6BqEX,kKAQC,wB7B7EU,C6B8EV,qBAFA,U7B5EU,C6BuEZ,gMpBpFI,4CTaQ,C6BuFT,sDAMD,6BADA,aAC6B,CAC9B,qBApCD,qBADA,a7BxDe,C6B2Df,2BAEE,wB7B7Da,C6B8Db,qBAFA,a7B5Da,C6B+Dd,iEAIC,uC7BnEa,C6BoEd,iLAQC,wB7B5Ea,C6B6Eb,qBAFA,a7B3Ea,C6BsEf,+MpBpFI,4CTcW,C6BsFZ,4DAMD,6BADA,aAC6B,CAC9B,oBApCD,qBADA,a7BvDc,C6B0Dd,0BAEE,wB7B5DY,C6B6DZ,qBAFA,U7B3DY,C6B8Db,+DAIC,qC7BlEY,C6BmEb,4KAQC,wB7B3EY,C6B4EZ,qBAFA,U7B1EY,C6BqEd,0MpBpFI,0CTeU,C6BqFX,0DAMD,6BADA,aAC6B,CAC9B,mBApCD,qBADA,a7BxEgB,C6B2EhB,yBAEE,wB7B7Ec,C6B8Ed,qBAFA,a7B5Ec,C6B+Ef,6DAIC,uC7BnFc,C6BoFf,uKAQC,wB7B5Fc,C6B6Fd,qBAFA,a7B3Fc,C6BsFhB,qMpBpFI,4CTFY,C6BsGb,wDAMD,6BADA,aAC6B,CAC9B,kBApCD,qBADA,a7BhEgB,C6BmEhB,wBAEE,wB7BrEc,C6BsEd,qBAFA,U7BpEc,C6BuEf,2DAIC,oC7B3Ec,C6B4Ef,kKAQC,wB7BpFc,C6BqFd,qBAFA,U7BnFc,C6B8EhB,gMpBpFI,yCTMY,C6B8Fb,sDAMD,6BADA,aAC6B,CAC9B,2BApCD,qBADA,a7B3DqB,C6B8DrB,iCAEE,wB7BhEmB,C6BiEnB,qBAFA,U7B/DmB,C6BkEpB,6EAIC,uC7BtEmB,C6BuEpB,+MAQC,wB7B/EmB,C6BgFnB,qBAFA,U7B9EmB,C6ByErB,6OpBpFI,4CTWiB,C6ByFlB,wEAMD,6BADA,aAC6B,CAC9B,mBApCD,qBADA,a7B/Da,C6BkEb,yBAEE,wB7BpEW,C6BqEX,qBAFA,U7BnEW,C6BsEZ,6DAIC,oC7B1EW,C6B2EZ,uKAQC,wB7BnFW,C6BoFX,qBAFA,U7BlFW,C6B6Eb,qMpBpFI,yCTOS,C6B6FV,wDAMD,6BADA,aAC6B,CAC9B,mBApCD,kBADA,U7BzEa,C6B4Eb,yBAEE,qB7B9EW,C6B+EX,kBAFA,a7B7EW,C6BgFZ,6DAIC,qC7BpFW,C6BqFZ,uKAQC,qB7B7FW,C6B8FX,kBAFA,a7B5FW,C6BuFb,qMpBpFI,0CTHS,C6BuGV,wDAMD,6BADA,UAC6B,CAC9B,UD1CD,a5BxDe,C4BuDf,e5B+GsB,C4B7GtB,oB5BoCoB,C4BvCtB,gBAMI,a5BkC6B,C4BxCjC,gCAOI,yB5BiC6B,C4BxCjC,sCAgBI,a5B5Ec,C4B6Ef,2BxB3EC,sBHwOE,mBAvE+B,C4BrDnC,uB7B6BsB,C4BlDvB,2BxBvFG,sBHwOE,mBAvE+B,C4BrDnC,qB7B6BsB,C4B9CvB,MR9FK,8BlB4X2C,CkBxX3C,uCUpBN,MVqBQ,eAAgB,CUfvB,CAND,iBAII,SAAU,CACX,qBAMC,YAAa,CACd,YAID,QAAS,CACT,eAAgB,CVDZ,2BlB8XwC,CkB1XxC,uCULN,YVMQ,eAAgB,CUFvB,CClBD,sCAIE,iBAAkB,CL6FG,iBKzFrB,kBAAmB,CCqBjB,uBA1BF,eAAgB,CAChB,mCAFA,mCAA4C,CAD5C,qBAA8B,CAgC1B,UAAW,CAHX,oBAAqB,CACrB,kB9BwW0C,C8BvW1C,qBA5BuC,CAqCxC,6BAiBC,aAAc,CACf,eDhCH,2BAA4B,CAD5B,qB/BhBa,C+BkBb,iC/BRa,CIFX,qBJyIoB,CS9IlB,4CsBUJ,a/BHa,C+BFb,YAAa,C9BwOT,kBAvE+B,C8B1JnC,eAAgB,CAJhB,QAAS,CAFT,e/ByYwB,C+BxYxB,wB/BuZ+B,C+B3Z/B,iBAAkB,CAQlB,eAAgB,CAPhB,Y/B+I+C,C+BjJjD,+BAmBI,MAAO,CACP,aAFA,Q/B6Xe,C+B1XhB,qBAYG,mBAAc,CADhB,qCAKI,MAAM,CADN,UAC6B,CAC9B,mBAID,iBAAc,CADhB,mCAKI,SAAM,CADN,OACgC,CpBCpC,yBoBfA,wBACE,mBAAc,CADhB,wCAKI,MAAM,CADN,UAC6B,CAC9B,sBAID,iBAAc,CADhB,sCAKI,SAAM,CADN,OACgC,CACjC,CpBAH,yBoBfA,wBACE,mBAAc,CADhB,wCAKI,MAAM,CADN,UAC6B,CAC9B,sBAID,iBAAc,CADhB,sCAKI,SAAM,CADN,OACgC,CACjC,CpBAH,yBoBfA,wBACE,mBAAc,CADhB,wCAKI,MAAM,CADN,UAC6B,CAC9B,sBAID,iBAAc,CADhB,sCAKI,SAAM,CADN,OACgC,CACjC,CpBAH,0BoBfA,wBACE,mBAAc,CADhB,wCAKI,MAAM,CADN,UAC6B,CAC9B,sBAID,iBAAc,CADhB,sCAKI,SAAM,CADN,OACgC,CACjC,CpBAH,0BoBfA,yBACE,mBAAc,CADhB,yCAKI,MAAM,CADN,UAC6B,CAC9B,uBAID,iBAAc,CADhB,uCAKI,SAAM,CADN,OACgC,CACjC,CAQP,uCAGI,WAAY,CAEZ,gBADA,YAAa,CAFb,Q/BsVe,CgCjYf,+BAnBF,wBAAiC,CACjC,mCAFA,mCAA4C,CAD5C,YAAa,CAyBT,UAAW,CAHX,oBAAqB,CACrB,kB9BwW0C,C8BvW1C,qBArBuC,CA8BxC,qCAiBC,aAAc,CACf,wCD4BD,SAAU,CAEV,cADA,YAAa,CAFb,UAAW,CADX,K/ByUe,CgCjYf,gCAZF,oCAA6C,CAC7C,uBAFA,cAAe,CADf,iCAA0C,CAkBtC,UAAW,CAHX,oBAAqB,CACrB,kB9BwW0C,C8BvW1C,qBAd2B,CAuB5B,sCAiBC,aAAc,CA7BhB,gCDkEE,gBAAiB,CAClB,0CAQD,SAAU,CAEV,eADA,YAAa,CAFb,UAAW,CADX,K/BwTe,CgCjYf,kCAIE,WAHA,oBAAqB,CAenB,aAdF,kB9BwW0C,C8BvW1C,qBACW,CAaV,mCAtBL,qCADA,uBAAgC,CADhC,iCAA0C,CA8BpC,UAAW,CAHX,oBAAqB,CACrB,mB9BqVwC,C8BpVxC,qBA3BuC,CA8BxC,wCAID,aAAc,CAVd,mCDgEA,gBAAiB,CAClB,kBAUH,wCAHA,QAAS,CACT,cAAoC,CACpC,e/BtGa,C+BwGd,eAeC,4BAA6B,CAC7B,SAPA,UAAW,CAEX,a/BtHgB,C+BiHhB,aAAc,CAId,e/BsDsB,C+BxDtB,iB/BuS+B,C+BnS/B,kBAAmB,CAEnB,kBAAmB,CAPnB,UASS,CAXX,sFV9HE,sBUgKE,a/B7Ia,C+B8Ib,oB/BoQ0B,C+BvS9B,gDA2CI,6BAFA,a/B3Jc,C+B4Jd,mBAC6B,CAG9B,oBAID,aAAc,CACf,iBAQC,a/BtKe,C+BkKf,aAAc,C9BkEV,kBAvE+B,C8BOnC,eAAgB,CADhB,wB/BkP+B,C+B9O/B,kBAAmB,CACpB,oBAMC,cAFA,aAAc,CACd,iB/BlLgB,C+BoLjB,oBAKC,wB/BxLgB,C+ByLhB,gCAFA,a/BrLa,C+BoLf,mCAOI,a/BlMc,C+B2LlB,kFVrME,qCUgNI,U/BzMS,C+B8Lf,oFVrME,sBUsNI,a/B+MwB,C+BhO9B,wFAuBM,a/BhNY,C+ByLlB,sCA4BI,+B/BhNW,C+BoLf,wCAgCI,a/B3Nc,C+B2LlB,qCAoCI,a/B7Nc,C+B8Nf,+BE1OD,mBAAoB,CADpB,iBAAkB,CAElB,qBAAsB,CAJxB,yCAQI,cADA,iBACc,CARlB,kXAmBI,SAAU,CACX,aAKD,YAAa,CACb,cAAe,CACf,0BAA2B,CAH7B,0BAMI,UAAW,CACZ,0EAOC,gBjCiHc,CiCrHlB,mG7BCI,6BADA,yB6BU4B,CAVhC,6G7BeI,4BADA,wB6BM8B,CAC/B,uBAiBD,sBADA,sBACkC,CAFpC,wGAOI,aAAc,CACf,yCAGC,cAAe,CAChB,yEAKD,oBADA,oBACqC,CACtC,yEAIC,sBADA,sBACqC,CAMvC,2ExBrFM,ewB0FsB,CACzB,oBAUD,sBAAuB,CADvB,qBAAsB,CAEtB,sBAAuB,CAHzB,wDAOI,UAAW,CAPf,4FAYI,ejC2Bc,CiCvClB,qH7BtEI,4BADA,4B6ByF+B,CAlBnC,oF7BrFI,wB6B4G4B,C7B3G5B,yB6B2G4B,CAC7B,KCnID,YAAa,CACb,cAAe,CAGf,gBADA,eAAgB,CADhB,cAEgB,CACjB,UAOC,alCKe,CkCTf,aAAc,CACd,kBhCq4BsC,CkBp4BlC,iGlBy4BsH,CkBr4BtH,uCcPN,UdQQ,eAAgB,CcavB,CArBD,gCAWI,a/BwIiC,C+BvIjC,oBAA6D,CAZjE,mBAiBI,alCbc,CkCed,eADA,mBACe,CAChB,UAQD,+BlC3BgB,CkC0BlB,oBAKI,eAAgB,CAChB,4BAAgD,C9BlBhD,8BJgIoB,CI/HpB,gC8BeA,kBlCgHoB,CkCpHxB,oDAWM,oClCrCY,CkCuCZ,iBAAkB,CAbxB,6BAkBM,4BAA6B,CAC7B,yBAFA,aAEyB,CAnB/B,8DA0BI,qBlCvDW,CkCwDX,kCAFA,alCtDW,CkC6Bf,yB9BZI,wB8B8C4B,C9B7C5B,0B8B2CA,eAE4B,CAC7B,qBAUC,eAAgB,CAChB,QAAS,C9BnET,qBJyIoB,CkCzExB,uDb/EE,yBawFE,UlCrEa,CkCuEd,wCAWC,aAAc,CACd,iBAAkB,CACnB,kDAMC,YAAa,CACb,WAAY,CACZ,iBAAkB,CACnB,iEAMC,UAAW,CACZ,uBAUC,YAAa,CAFjB,qBAKI,aAAc,CACf,QCrHD,kBAAmB,CAFnB,YAAa,CACb,cAAe,CAEf,6BAA8B,CAI9B,kBARA,iBnCsXkB,CmCvXpB,oIAkBI,kBAAmB,CAFnB,YAAa,CACb,iBAAkB,CAElB,6BAA8B,CAC/B,clCuNG,gBAvE+B,CkC1HnC,cnCkVyB,CmCnVzB,qBjC63B+E,CiC93B/E,kBjC83B+E,CiCz3B/E,kBAAmB,CANrB,wCAUI,oBAA6D,CAC9D,YASD,YAAa,CACb,qBAAsB,CAGtB,gBADA,eAAgB,CADhB,cAEgB,CALlB,sBASI,eADA,eACe,CATnB,2BAaI,eAAgB,CACjB,aAUD,qBADA,iBjCkzBuC,CiChzBxC,iBAgBC,mBAJA,eAAgB,CAChB,WAGmB,CACpB,gBAOC,4BAA6B,CAC7B,4BAAuC,C/BzGrC,qBJyIoB,CC+FlB,mBAvE+B,CkC1DnC,aAAc,CAFd,qBjCk0BwC,CkB16BpC,sClB86ByD,CkB16BzD,uCemGN,gBflGQ,eAAgB,CeoHvB,CAlBD,sBAUI,oBAAqB,CAVzB,sBAgBI,mBADA,SAAU,CADV,oBnCiKqB,CmC9JtB,qBAWD,uBAA2B,CAD3B,2BAA4B,CAE5B,qBANA,oBAAqB,CAErB,YAAa,CACb,qBAAsB,CAFtB,WAKqB,CACtB,mBAGC,uCAAwE,CACxE,eAAgB,CxB1Fd,yBwBsGA,kBAEI,gBAAiB,CACjB,0BAA2B,CAH9B,8BAMK,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAcO,oBADA,oBnCuNsB,CmCpO7B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,sBAAwB,CACxB,eAAgB,CAxBrB,kCA4BK,YAAa,CACd,CxBnIL,yBwBsGA,kBAEI,gBAAiB,CACjB,0BAA2B,CAH9B,8BAMK,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAcO,oBADA,oBnCuNsB,CmCpO7B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,sBAAwB,CACxB,eAAgB,CAxBrB,kCA4BK,YAAa,CACd,CxBnIL,yBwBsGA,kBAEI,gBAAiB,CACjB,0BAA2B,CAH9B,8BAMK,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAcO,oBADA,oBnCuNsB,CmCpO7B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,sBAAwB,CACxB,eAAgB,CAxBrB,kCA4BK,YAAa,CACd,CxBnIL,0BwBsGA,kBAEI,gBAAiB,CACjB,0BAA2B,CAH9B,8BAMK,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAcO,oBADA,oBnCuNsB,CmCpO7B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,sBAAwB,CACxB,eAAgB,CAxBrB,kCA4BK,YAAa,CACd,CxBnIL,0BwBsGA,mBAEI,gBAAiB,CACjB,0BAA2B,CAH9B,+BAMK,kBAAmB,CANxB,8CASO,iBAAkB,CATzB,yCAcO,oBADA,oBnCuNsB,CmCpO7B,sCAmBK,gBAAiB,CAnBtB,oCAuBK,sBAAwB,CACxB,eAAgB,CAxBrB,mCA4BK,YAAa,CACd,CAnCT,eAQQ,gBAAiB,CACjB,0BAA2B,CATnC,2BAYU,kBAAmB,CAZ7B,0CAeY,iBAAkB,CAf9B,qCAoBY,oBADA,oBnCuNsB,CmC1OlC,kCAyBU,gBAAiB,CAzB3B,gCA6BU,sBAAwB,CACxB,eAAgB,CA9B1B,+BAkCU,YAAa,CAcvB,gGAMM,anC9LW,CmCwLjB,oCAYM,anCzMY,CmC6LlB,oFAgBQ,anCxMS,CmCwLjB,6CAoBQ,uBnC9MO,CmC0Lf,qFA0BM,anClNW,CmCwLjB,8BAgCI,yBADA,anCwK2C,CmCvM/C,mCAoCI,4PhC/J8E,CgC2HlF,2BAwCI,anCrOc,CmC6LlB,mGA6CM,anCrOW,CmC2OjB,kNAgBQ,UnCvQO,CmCuPf,4CAoBQ,yBnC3QO,CmCuPf,mFA0BM,UnCjRS,CmCuPf,6BAgCI,yBADA,UnCgH0C,CmC/I9C,kCAoCI,yPhClN8E,CgC8KlF,0HA4CM,UnCnSS,CmCoSV,MCrSH,oBAAqB,CAErB,0BAA2B,CAD3B,qBpCAa,CoCEb,sBpCAgB,CIMd,sBgCbF,YAAa,CACb,qBAAsB,CACtB,WpCoJsB,CoCxJxB,SAcI,cADA,cACc,CAdlB,kBAmBI,sBADA,kBACsB,CAnB1B,8BhCwBI,8BD+K4D,CC9K5D,gCgCHE,kBjCiL0D,CiCvMhE,6BhCuCI,kCADA,kCDiK4D,CiC5K1D,qBjC4K0D,CiCvMhE,8DAoCI,YAAa,CACd,WAMD,aAAc,CACd,YpCgZkB,CoC9YnB,YAGC,oBpC4Y0B,CoC3Y3B,eAGC,kBACgB,CACjB,qCADC,eAIgB,CACjB,iBAIG,oBAAqB,CAFzB,sBAMI,gBpCyXgB,CoCxXjB,aAWD,mCpC9Da,CoC+Db,8BAHA,eAAgB,CADhB,mBpCnEgB,CoCkElB,yBhC5DI,iCgCoE8E,CAC/E,aAMD,mCpCzEa,CoC0Eb,2BAHA,mBpC/EgB,CoC8ElB,wBhCxEI,iCDwL4D,CiCxG7D,kBAYD,gBAFA,qBAEgB,CAQjB,qCATC,iBAAqC,CAFrC,kBAeqC,CACtC,kBhCzGG,sBgCgHF,QAAS,CACT,MAAO,CACP,YpC1DW,CoCqDX,iBAAkB,CAElB,OAAQ,CADR,KjC0E8D,CiCpE/D,yCAKC,UAAW,CACZ,wBhCjHG,8BD+K4D,CC9K5D,+BD8K4D,CiCzD/D,2BhCvGG,kCADA,kCDiK4D,CiCpD/D,kBAWG,qBlCo6BsD,CSvgCtD,yByB+FJ,YAQI,YAAa,CACb,kBAAmB,CATvB,kBAcM,WAAY,CACZ,eAAgB,CAftB,wBAmBQ,cADA,aACc,CAnBtB,mChC1HI,6BADA,yBgCoJkC,CAzBtC,iGA8BY,yBAA0B,CA9BtC,oGAmCY,4BAA6B,CAnCzC,oChC5GI,4BADA,wBgCqJoC,CAxCxC,mGA6CY,wBAAyB,CA7CrC,sGAkDY,2BAA4B,CAC7B,CC7MX,kBAYE,oBAAqB,CATrB,kBAAmB,CAMnB,qBrCFa,CqCGb,QAAS,CjCKP,eiCJsB,CAJxB,arCUa,CqCfb,YAAa,CpCqPT,mBAvE+B,CoC3KnC,mBrCicwB,CqCrcxB,iBAAkB,CAOlB,eAAgB,CjBIZ,sJiBRJ,UnColCgF,CkBxkC5E,uCiBhBN,kBjBiBQ,eAAgB,CiBgCvB,CAjDD,kCAiBI,qBrCVW,CqCWX,kCAFA,arCPc,CqCTlB,wCAsBM,yBnC6kCmD,CmCnmCzD,gEAqBM,8SnC6kC6D,CmClmCnE,wBAkCI,2BAA4B,CAC5B,sBrC0ayB,CqC7azB,UAAW,CAJX,aAAc,CAEd,arC+ayB,CqC9azB,gBAAiB,CjBnBf,qCiBiBF,YnCqkC+D,CkBllC7D,uCiBhBN,wBjBiBQ,eAAgB,CiBoBrB,CArCH,wBAwCI,SAAU,CAxCd,wBA6CI,oBrC1Ba,CqC4Bb,gBADA,SAAU,CAFV,SrC+ZoC,CqC3ZrC,kBAID,eAAgB,CACjB,gBAGC,qBrCjDa,CqCkDb,wBrChDgB,CqC8ClB,8BjC/BI,8BJgIoB,CI/HpB,+BJ+HoB,CqCjGxB,gDjC/BI,0CD+K4D,CC9K5D,2CD8K4D,CkChJhE,oCAaI,YAAa,CAbjB,6BjChBI,kCADA,kCJkHoB,CqCjGxB,yDjChBI,8CADA,8CDiK4D,CkChJhE,iDjChBI,kCADA,kCJkHoB,CqCrEnB,gBAKH,mBrC8WwB,CqC7WzB,qCASG,cAAe,CAFnB,iCAOI,aAAc,CjCxFd,gBiCuFA,cAEwB,CAR5B,6CAUoB,YAAa,CAVjC,4CAWmB,eAAgB,CAI9B,+DjChGD,eJygBwB,CqCzavB,YC5GH,4BtCkhByB,CsCxhBzB,YAAa,CACb,cAAe,CrCyPX,kBAvE+B,CqC9KnC,eAAgB,CAFhB,etCohB0B,CsCrhB1B,gBtCyhB0B,CsCnhB3B,kCAKG,kBpC8zCqC,CoCj0CzC,yCAQM,atCDY,CsCEZ,wCAAS,CAHT,UAAW,CACX,mBAEyO,CAT/O,wBAcI,atCPc,CsCQf,YCzBD,YAAa,CjCIb,gBADA,cACgB,CiCFjB,WAOC,qBvCCa,CuCAb,wBvCGgB,CuCNhB,avCee,CuChBf,aAAc,CADd,iBAAkB,CnBUd,6HlB6hCoJ,CkBzhCpJ,uCmBfN,WnBgBQ,eAAgB,CmBQvB,CAxBD,iBAcI,qBAFA,oBAA6D,CAF7D,SvCDc,CuCTlB,kCAaI,wBvCLc,CuCGd,avCwR6B,CuCnSjC,iBAsBI,gBADA,SrCqgCiC,CqCxgCjC,SvCiR6B,CuC5Q9B,wCAKC,gBvCuHc,CuCzHlB,6BlB3BE,wBrBmBe,CuCiBb,qBAFA,UvC3BW,CuC0BX,SvCda,CuCQjB,+BAeI,qBvCnCW,CuCoCX,qBAHA,avC3Bc,CuC4Bd,mBvC/Bc,CuCTlB,WCAI,wBxC6RyB,CwC3R1B,kCpC2CC,kCADA,8BJ2GoB,CwCnJtB,iCpC2BE,mCADA,+BJyHoB,CwCxJtB,0BvCuPI,oBuCtPF,uBvC+KiC,CuC7KlC,iDpC2CC,gCADA,4BJ4GqB,CwC/IhB,gDpCsBL,iCADA,6BJ0HqB,CwCzJvB,0BvCuPI,mBuCtPF,qBvC+KiC,CuC7KlC,iDpC2CC,iCADA,6BJ0GqB,CwC7IhB,gDpCsBL,kCADA,8BJwHqB,CwCvIhB,OpCDL,sBqCRF,UzCAa,CyCLb,oBAAqB,CxCqPjB,eAvE+B,CwC3KnC,ezCqLoB,CyCpLpB,aAAc,CAHd,qBzC0esB,CyCretB,iBAAkB,CAElB,uBAAwB,CADxB,kBzC+IsB,CyCvJxB,aAeI,YAAa,CACd,YAKD,iBAAkB,CAClB,QAAS,CACV,OCpBC,4BAA6C,CtCW3C,sBsCZF,kBxCmvCkC,CwCpvClC,sB1C0fuB,C0C3fvB,iB1CuJsB,C0ClJvB,eAKC,aAAc,CACf,YAIC,e1C0KoB,C0CzKrB,mBAQC,qBxCouCkD,CwCruCpD,8BASI,yBAJA,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAEN,S1C4dqB,C0C1dtB,eCpCD,axCyJmC,CwCrJnC,2BACE,axCyJiC,CwCxJlC,iBAND,axCyJmC,CwCrJnC,6BACE,axCyJiC,CwCxJlC,eAND,axCyJmC,CwCrJnC,2BACE,axCyJiC,CwCxJlC,YAND,axCyJmC,CwCrJnC,wBACE,axCyJiC,CwCxJlC,eAND,axCyJmC,CwCrJnC,2BACE,axCyJiC,CwCxJlC,cAND,axCyJmC,CwCrJnC,0BACE,axCyJiC,CwCxJlC,aAND,axCyJmC,CwCrJnC,yBACE,axCyJiC,CwCxJlC,YAND,axCyJmC,CwCrJnC,wBACE,axCyJiC,CwCxJlC,qBAND,axCyJmC,CwCrJnC,iCACE,axCyJiC,CwCxJlC,aAND,axCyJmC,CwCrJnC,yBACE,axCyJiC,CwCxJlC,aAND,UxCyJmC,CwCrJnC,yBACE,axCyJiC,CwCxJlC,wCCHC,GAAK,0B1CmwC+B,EyChwCrC,gCCHC,GAAK,0B1CmwC+B,E0C9vCxC,UAKE,wB5CFgB,CIMd,qBJyIoB,CS9IlB,+CR6OA,mBAvE+B,C2CxKnC,W5CSa,C4CHd,wBAPC,YAAa,CAEb,e1CmwCgD,C0C9vCjD,cAUC,wB5CLe,C4CEf,U5Cda,C4CWb,qBAAsB,CACtB,sBAAuB,CAGvB,iBAAkB,CxBVd,0BwBWJ,kB1CqvCgD,CkB5vC5C,uCwBAN,cxBCQ,eAAgB,CwBSvB,CAED,sBvBYE,qKAA6I,CuBV7I,yB1CsuCsC,C0CruCvC,uBAIG,yDAA0C,CAA1C,iDAA8D,CAG5D,uCAJJ,uBAKM,sBAAW,CAAX,cAAe,CAGpB,CC1CH,YzCeI,sByCdF,YAAa,CACb,qBAAsB,CAItB,eAAgB,CADhB,c7CmJsB,C6ChJvB,qBAIC,sBADA,oBACsB,CAFxB,+BAMI,kCAAoC,CACpC,yBAA0B,CAC3B,wBAWD,a7CfgB,C6CgBhB,mBAFA,UAEmB,CAHrB,4DAWI,yBAFA,a7CtBc,C6CuBd,oBAAqB,CAFrB,S7C3Bc,C6CmBlB,+BAgBI,yBADA,a7CjCc,C6CmCf,iBAcD,qB7CnDa,C6CoDb,yBAHA,a7CxCgB,C6CsChB,aAAc,CACd,mB7Cgd8B,C6Cld9B,iB7C5CgB,C6C2ClB,6BzC5BI,8ByCsCkC,CzCrClC,+ByCqCkC,CAVtC,4BzCbI,kCADA,kCyC4BqC,CAdzC,oDAqBI,sBAFA,a7C1Dc,C6C2Dd,mB7CjEW,C6C6Cf,wBA4BI,wB7C7Da,C6C8Db,qBAFA,U7CxEW,C6CuEX,S7C3Da,C6CiCjB,kCAiCI,kBAAmB,CAjCvB,yCAqCM,qBADA,e7C4DY,C6C1Db,uBAcC,kBAAmB,CADrB,oDzCjCA,iCJkFoB,CI9FpB,yByCmDsC,CANtC,mDzCjCA,4BAZA,+ByCwD2C,CAX3C,+CAeM,YAAa,CAfnB,yDAoBM,oBADA,oBACoB,CApB1B,gEAwBQ,sBADA,gB7CsBM,CWzFd,yBkC4CA,0BACE,kBAAmB,CADrB,uDzCjCA,iCJkFoB,CI9FpB,yByCmDsC,CANtC,sDzCjCA,4BAZA,+ByCwD2C,CAX3C,kDAeM,YAAa,CAfnB,4DAoBM,oBADA,oBACoB,CApB1B,mEAwBQ,sBADA,gB7CsBM,C6CpBP,ClCrEP,yBkC4CA,0BACE,kBAAmB,CADrB,uDzCjCA,iCJkFoB,CI9FpB,yByCmDsC,CANtC,sDzCjCA,4BAZA,+ByCwD2C,CAX3C,kDAeM,YAAa,CAfnB,4DAoBM,oBADA,oBACoB,CApB1B,mEAwBQ,sBADA,gB7CsBM,C6CpBP,ClCrEP,yBkC4CA,0BACE,kBAAmB,CADrB,uDzCjCA,iCJkFoB,CI9FpB,yByCmDsC,CANtC,sDzCjCA,4BAZA,+ByCwD2C,CAX3C,kDAeM,YAAa,CAfnB,4DAoBM,oBADA,oBACoB,CApB1B,mEAwBQ,sBADA,gB7CsBM,C6CpBP,ClCrEP,0BkC4CA,0BACE,kBAAmB,CADrB,uDzCjCA,iCJkFoB,CI9FpB,yByCmDsC,CANtC,sDzCjCA,4BAZA,+ByCwD2C,CAX3C,kDAeM,YAAa,CAfnB,4DAoBM,oBADA,oBACoB,CApB1B,mEAwBQ,sBADA,gB7CsBM,C6CpBP,ClCrEP,0BkC4CA,2BACE,kBAAmB,CADrB,wDzCjCA,iCJkFoB,CI9FpB,yByCmDsC,CANtC,uDzCjCA,4BAZA,+ByCwD2C,CAX3C,mDAeM,YAAa,CAfnB,6DAoBM,oBADA,oBACoB,CApB1B,oEAwBQ,sBADA,gB7CsBM,C6CpBP,CAaX,kBzC9HI,eyC+HsB,CAD1B,mCAII,oB7CGc,C6CPlB,8CAOM,qBAAsB,CCpJ1B,yBAEE,yBADA,a3CsJiC,C2CvJnC,4GAQM,yBADA,a3CqJ6B,C2C5JnC,uDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,2BAEE,yBADA,a3CsJiC,C2CvJnC,gHAQM,yBADA,a3CqJ6B,C2C5JnC,yDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,yBAEE,yBADA,a3CsJiC,C2CvJnC,4GAQM,yBADA,a3CqJ6B,C2C5JnC,uDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,sBAEE,yBADA,a3CsJiC,C2CvJnC,sGAQM,yBADA,a3CqJ6B,C2C5JnC,oDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,yBAEE,yBADA,a3CsJiC,C2CvJnC,4GAQM,yBADA,a3CqJ6B,C2C5JnC,uDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,wBAEE,yBADA,a3CsJiC,C2CvJnC,0GAQM,yBADA,a3CqJ6B,C2C5JnC,sDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,uBAEE,yBADA,a3CsJiC,C2CvJnC,wGAQM,yBADA,a3CqJ6B,C2C5JnC,qDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,sBAEE,yBADA,a3CsJiC,C2CvJnC,sGAQM,yBADA,a3CqJ6B,C2C5JnC,oDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,+BAEE,yBADA,a3CsJiC,C2CvJnC,wHAQM,yBADA,a3CqJ6B,C2C5JnC,6DAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,uBAEE,yBADA,a3CsJiC,C2CvJnC,wGAQM,yBADA,a3CqJ6B,C2C5JnC,qDAaM,wB3C+I6B,C2C9I7B,qBAFA,U3CgJ6B,C2C5JnC,uBAEE,sBADA,U3CsJiC,C2CvJnC,wGAQM,yBADA,U3CqJ6B,C2C5JnC,qDAaM,qB3C+I6B,C2C9I7B,kBAFA,U3CgJ6B,C2C7I9B,WCRL,iYAA0F,CAC1F,QAAS,C3COP,qBJyIoB,C+CtJtB,sBAAuB,CAIvB,a/COgB,C+CThB,W/CyhBoB,C+CnhBpB,WALA,a7Cs4CgC,C6Cx4ChC,U7C64C6B,C6C/4C/B,iBAaI,a/CDc,C+CGd,YADA,oB7Ck4C4B,C6Ch5ChC,iBAoBI,e/C+Q6B,C+C9Q7B,UAFA,S7C83C0B,C6Cj5C9B,wCA4BI,YAFA,mBAAoB,CACpB,wBAAiB,CAAjB,qBAAiB,CAAjB,gB7Cu3C4B,C6Cr3C7B,iBAID,iD7Ck3CqE,C6Cj3CtE,OChCC,2BAA4B,CAD5B,oChDKa,CgDHb,+B9CyqCmD,CE9pCjD,sB4CVF,2ChDwJ+C,CC0F3C,iBAvE+B,C+ClLnC,cAAe,CAGf,mBAAoB,CAJpB,WhD2JsB,CgD5JxB,gCAaI,SAAU,CAbd,YAiBI,YAAa,CACd,iBAKD,cAAe,CACf,oBAFA,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAEoB,CAHtB,mCAMI,qB9CqUwC,C8CpUzC,cAKD,kBAAmB,CAInB,2BAA4B,CAD5B,oChDzBa,CgD2Bb,uC9CkpCoD,CE5pClD,0CD+K4D,CC9K5D,4C4CMF,ahDlBgB,CgDehB,YAAa,CAEb,oB7CyK8D,C6C5KhE,yBAYI,mBADA,qB9CioCsC,C8C/nCvC,YAKD,qBADA,cACqB,CACtB,OCtCC,YAAa,CAEb,WAAY,CAJZ,MAAO,CASP,UAJA,iBAAkB,CAClB,eAAgB,CARhB,cAAe,CACf,KAAM,CAIN,UAAW,CAFX,YAQU,CAIX,cAMC,Y/CkrCuC,C+ChrCvC,oBAJA,iBAAkB,CAClB,UAGoB,CAGpB,0BAEE,4B7BpBE,iClB4tCiD,CkBxtCjD,uC6BcJ,0B7BbM,eAAgB,C6BgBrB,CACD,0BACE,c/CssCoC,C+CrsCrC,kCAIC,qB/CmsC2C,C+ClsC5C,yBAID,wB9C0J8D,C8C3JhE,wCAII,eAAgB,CAChB,eAAgB,CALpB,qCASI,eAAgB,CACjB,uBAKD,kBAAmB,CADnB,YAAa,CAEb,4B9C2I8D,C8C1I/D,eAYC,2BAA4B,CAD5B,qBjDjEa,CiDmEb,kCjDzDa,CIFX,mBJ0IqB,CS/InB,+CTOS,CiDiDb,YAAa,CACb,qBAAsB,CAWtB,UAPA,mBAAoB,CANpB,iBAAkB,CAGlB,UAUU,CACX,gBAUC,yBADA,YAAa,CAHb,MAAO,CAFP,cAAe,CACf,KAAM,CAGN,WAAY,CADZ,YjDrEa,CiDiEf,qBAUW,SAAU,CAVrB,qBAWW,UjDyZgB,CiDzZoB,cAQ7C,kBAAmB,CAGnB,+BjD/FgB,CIed,wCD+K4D,CC9K5D,0C6C0EF,YAAa,CACb,aAAc,CAEd,6BAA8B,CAC9B,c9CgG8D,C8CrGhE,yBAWI,uCADA,eACyG,CAC1G,aAMD,gBADA,ejD0EoB,CiDxErB,YAQC,aAAc,CACd,eAJA,iBjD0X0B,CiDrX3B,cAOC,kBAAmB,C7C/FjB,4CADA,4CDiK4D,C8C9D9D,4BjDhIgB,CiD0HhB,YAAa,CAEb,aAAc,CADd,cAAe,CAGf,wBAAyB,CACzB,e9C+D8D,C8CrEhE,gBAcI,aAAwC,CtCrFxC,yBsCrCJ,cAmII,oBADA,eACyC,CA7G7C,yBAiHI,0B9C0C4D,C8C9IhE,uBAwGI,8B9CsC4D,C8CvIhE,exCtDM,2CTmJ2C,CiDS9C,UAEW,e/C2jC2B,C+C3jCH,CtC7GlC,yBsCiHF,oBAEE,e/CujCqC,C+CtjCtC,CtCpHC,0BsCwHF,UAAY,gB/CmjC4B,C+CnjCJ,CASlC,kBAGE,WAAY,CACZ,SAFA,cAAe,CADf,WAGS,CAJX,iCAQI,QAAS,C7CrLb,gB6CoLI,WAEwB,CAT5B,gC7C7KA,e6C0L4B,CAb5B,8BAiBI,eAAgB,CAjBpB,gC7C7KA,e6CkM4B,CtCzI5B,4BsCoHA,0BAGE,WAAY,CACZ,SAFA,cAAe,CADf,WAGS,CAJX,yCAQI,QAAS,C7CrLb,gB6CoLI,WAEwB,CAT5B,wC7C7KA,e6C0L4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC7C7KA,e6CkM4B,CACzB,CtC1IH,4BsCoHA,0BAGE,WAAY,CACZ,SAFA,cAAe,CADf,WAGS,CAJX,yCAQI,QAAS,C7CrLb,gB6CoLI,WAEwB,CAT5B,wC7C7KA,e6C0L4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC7C7KA,e6CkM4B,CACzB,CtC1IH,4BsCoHA,0BAGE,WAAY,CACZ,SAFA,cAAe,CADf,WAGS,CAJX,yCAQI,QAAS,C7CrLb,gB6CoLI,WAEwB,CAT5B,wC7C7KA,e6C0L4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC7C7KA,e6CkM4B,CACzB,CtC1IH,6BsCoHA,0BAGE,WAAY,CACZ,SAFA,cAAe,CADf,WAGS,CAJX,yCAQI,QAAS,C7CrLb,gB6CoLI,WAEwB,CAT5B,wC7C7KA,e6C0L4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC7C7KA,e6CkM4B,CACzB,CtC1IH,6BsCoHA,2BAGE,WAAY,CACZ,SAFA,cAAe,CADf,WAGS,CAJX,0CAQI,QAAS,C7CrLb,gB6CoLI,WAEwB,CAT5B,yC7C7KA,e6C0L4B,CAb5B,uCAiBI,eAAgB,CAjBpB,yC7C7KA,e6CkM4B,CACzB,CCrNP,SAUE,oBAAqB,CAPrB,aAAc,CCHd,8BnDqLsC,CCqElC,kBAvE+B,CkDjLnC,iBAAkB,CAClB,enDyLsB,CmDlLtB,qBAAsB,CAItB,eAAgB,CAVhB,enD4LoB,CkD5LpB,QhDmnCmC,CgD5mCnC,UAVA,iBAAkB,CCIlB,eAAgB,CAChB,gBAAiB,CACjB,oBAAqB,CACrB,gBAAiB,CACjB,mBAAoB,CAIpB,kBAAmB,CAFnB,iBAAkB,CAClB,mBAAoB,CDVpB,YASU,CAXZ,cAaW,UhDumC2B,CgDpnCtC,wBAiBI,aAAc,CAEd,aAHA,iBAAkB,CAElB,WhDwmCqC,CgD1nCzC,+BAwBM,wBAAyB,CACzB,mBAFA,UAAW,CADX,iBAGmB,CACpB,6DAKH,eAAgC,CADlC,2FAII,QAAS,CAJb,yGASM,yBADA,0BAAgE,CADhE,QlDjBS,CkDoBV,+DAKH,ehD6kCuC,CgD9kCzC,6FAMI,aAFA,MAAO,CACP,WhDwkCqC,CgD7kCzC,2GAWM,2BADA,gCAA2F,CAD3F,UlDjCS,CkDoCV,mEAKH,eAAgC,CADlC,iGAII,KAAM,CAJV,+GASM,4BADA,0BhDsjCmC,CgDvjCnC,WlD/CS,CkDkDV,gEAKH,ehD+iCuC,CgDhjCzC,8FAMI,aAFA,OAAQ,CACR,WhD0iCqC,CgD/iCzC,4GAWM,0BADA,gChDsiCmC,CgDviCnC,SlD/DS,CkDkEV,eAyBH,wBlD3Fa,CIFX,sB8C2FF,UlDnGa,CkDiGb,ehDqgCuC,CgDpgCvC,oBhD0gC6C,CgDxgC7C,iBlD6CsB,CkD1CvB,SEtGC,oBAAqB,CAErB,2BAA4B,CAD5B,qBpDFa,CoDIb,4BpD0cgC,CItc9B,qBJyIoB,CS9IlB,2C2CTJ,aAAc,CDJd,8BnDqLsC,CCqElC,kBAvE+B,CkDjLnC,iBAAkB,CAClB,enDyLsB,CoD1LtB,MAA6B,CDQ7B,qBAAsB,CAItB,eAAgB,CAVhB,enD4LoB,CoD3LpB,epDkduB,CoDvdvB,iBAAkB,CDKlB,eAAgB,CAChB,gBAAiB,CACjB,oBAAqB,CACrB,gBAAiB,CACjB,mBAAoB,CCRpB,KAAM,CDYN,kBAAmB,CAFnB,iBAAkB,CAClB,mBAAoB,CCTpB,YpD+JgD,CoDnKlD,wBAqBI,aAAc,CAEd,eAHA,iBAAkB,CAElB,apDgd2B,CoDte/B,6DA8BM,wBAAyB,CACzB,mBAFA,UAAW,CADX,aAAc,CADd,iBAImB,CACpB,2FAMD,2BjDqK4D,CiDvKhE,yGAOM,iCADA,8BAAgE,CADhE,QlD0nCiE,CkD/nCvE,uGAaM,sBADA,8BAAgE,CADhE,UpDpCS,CoDuCV,6FAQD,eAFA,yBjDmJ4D,CiDlJ5D,apD4ayB,CoD/a7B,2GASM,mCADA,sCAA2F,CAD3F,MlDsmCiE,CkD7mCvE,yGAeM,wBADA,sCAA2F,CAD3F,QpDxDS,CoD2DV,iGAMD,wBjD+H4D,CiDjIhE,+GAOM,oCADA,8BAA2F,CAD3F,KlDolCiE,CkDzlCvE,6GAaM,yBADA,8BAA2F,CAD3F,OpD1ES,CoD+Df,iHA0BI,oCADA,UAAW,CAHX,aAAc,CADd,QAAS,CAGT,oBAAsC,CALtC,iBAAkB,CAClB,KAAM,CAGN,apD4X2B,CoDxX5B,8FAOC,eAFA,0BjDiG4D,CiDhG5D,apD0XyB,CoD7X7B,4GASM,kCADA,sCpDsXyB,CoDvXzB,OlDojCiE,CkD3jCvE,0GAeM,uBADA,sCpDgXyB,CoDjXzB,SpD1GS,CoD6GV,gBAyBH,4BpD4U6B,CoD3U7B,sCjDcmC,CCpIjC,0CD+K4D,CC9K5D,4CH8NE,mBAvE+B,CmDtCnC,eAAgB,CADhB,mBjD8D8D,CiD/DhE,sBAUI,YAAa,CACd,cAKD,cADA,mBpD1IgB,CoD4IjB,UC/IC,iBAAkB,CACnB,wBAGC,kBAAmB,CACpB,gBAKC,gBAFA,iBAAkB,CAClB,UACgB,CCtBhB,sBAEE,UAAW,CACX,WAFA,aAEW,CACZ,eD4BD,kCAA2B,CAA3B,0BAA2B,CAJ3B,YAAa,CACb,UAAW,CAEX,kBAAmB,CAJnB,iBAAkB,CjCbd,qCiCgBJ,UnDm1CsF,CkB/1ClF,uCiCQN,ejCPQ,eAAgB,CiCevB,CAED,8DAGE,aAAc,CACf,wEAKC,0BAA2B,CAC5B,wEAIC,2BAA4B,CAC7B,8BAWG,SAAU,CAEV,eADA,2BACe,CAJnB,iJAWI,UADA,SACU,CAXd,oFAiBI,SAAU,CjC/DR,0BiC8DF,SnDoyCoC,CkB91ClC,uCiC0CN,oFjCzCQ,eAAgB,CiC4DrB,CAQH,8CAQE,kBAAmB,CAMnB,eAAgB,CAChB,QAAS,CAXT,QAAS,CAQT,UrD1Fa,CqDqFb,YAAa,CAEb,sBAAuB,CAOvB,UnDkvCqC,CmDvvCrC,SAAU,CATV,iBAAkB,CAWlB,iBAAkB,CAVlB,KAAM,CjC5EF,6BiCmFJ,SnDuvCsC,CmD5vCtC,SnD+vCoD,CkBz0ChD,uCiCqEN,8CjCpEQ,eAAgB,CiC+FvB,CA3BD,oHAsBI,UrDpGW,CqDuGX,WADA,SAAU,CADV,oBnD4uCmC,CmDzuCpC,uBAGD,MAAO,CAER,uBAEC,OAAQ,CAET,wDASC,uBAAwB,CADxB,2BAA4B,CAE5B,0BALA,oBAAqB,CAErB,WnD0uCuC,CmD3uCvC,UAI0B,CAC3B,4BAWC,sRlD9DgF,CkD+DjF,4BAEC,uRlDjEgF,CkDkEjF,qBAUC,QAAS,CAGT,YAAa,CACb,sBAAuB,CAHvB,MAAO,CASP,gBAFA,kBAAmB,CACnB,enDirCsC,CmDnrCtC,gBnDmrCsC,CmDrrCtC,SAAU,CAPV,iBAAkB,CAClB,OAAQ,CAGR,SAQgB,CAblB,sCA0BI,2BAA4B,CAD5B,qBrD3KW,CqD6KX,QAAS,CAGT,oCAAoE,CADpE,iCAAiE,CAbjE,sBAAuB,CAQvB,cAAe,CAPf,aAAc,CAEd,UnDgrCoC,CmD7qCpC,enD+qCoC,CmDhrCpC,gBnDgrCoC,CmDtqCpC,UnDuqCmC,CmDlrCnC,SAAU,CAGV,kBAAmB,CjCpKjB,4BiC+JF,UnDurCiD,CkBl1C/C,uCiCyIN,sCjCxIQ,eAAgB,CiCyKrB,CAjCH,6BAoCI,SnDoqCkC,CmDnqCnC,kBAWD,cnD8pC0C,CmD1pC1C,UrDtMa,CqDmMb,QAA0C,CAE1C,sBnD0pC0C,CmD3pC1C,mBnD2pC0C,CmD/pC1C,iBAAkB,CAClB,SAA2C,CAM3C,iBAAkB,CACnB,sFAOG,+BnD6pCyD,CmDhqC7D,qDAOI,wBrDzMW,CqDkMf,iCAWI,arD7MW,CqD8MZ,kCE7ND,GAAK,uBAA+C,EF6NnD,0BE7ND,GAAK,uBAA+C,EAItD,gBASE,qDAAoD,CAApD,8CAHA,kBAA+B,CAE/B,iBAAkB,CAFlB,oCAA+B,CAL/B,oBAAqB,CAErB,WrDi3C4B,CqDh3C5B,sBrDk3C+B,CqDp3C/B,UAOkE,CACnE,mBAKC,kBADA,WrD42C4B,CqD72C5B,UrD+2C4B,CqD52C7B,gCAQC,GACE,kBAAmB,CAErB,IACE,SAAU,CACV,cAAe,EAblB,wBAQC,GACE,kBAAmB,CAErB,IACE,SAAU,CACV,cAAe,EAKnB,cASE,mDAAoD,CAApD,4CAJA,6BAA8B,CAE9B,iBAAkB,CANlB,oBAAqB,CAErB,WrD+0C4B,CqD10C5B,SAAU,CAJV,sBrDg1C+B,CqDl1C/B,UAOgE,CACjE,iBAIC,YADA,UrD20C4B,CqDz0C7B,uCAIG,8BAEE,+BAAoB,CAApB,uBAAgD,CACjD,CClEL,WAUE,2BAA4B,CAD5B,qBxDEa,CwDTb,QAAS,C/CYL,+CTOS,CwDjBb,YAAa,CACb,qBAAsB,CACtB,cAAe,CAKf,SAAU,CAVV,cAAe,CpCeX,qCoCRJ,iBAAkB,CALlB,YAUwE,CpCOpE,uCoCpBN,WpCqBQ,eAAgB,CoCPvB,CAED,kBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,cxDoe0B,CwDxe5B,6BAQI,uCADA,eACgG,CACjG,iBAKD,gBADA,exDoKoB,CwDlKrB,gBAGC,WAAY,CAEZ,gBADA,cACgB,CACjB,iBAMC,wCxDtBa,CwDoBb,MAAO,CADP,KAAM,CAIN,4BAFA,WAE4B,CAC7B,eAMC,uCxD9Ba,CwD4Bb,OAAQ,CADR,KAAM,CAIN,2BAFA,WAE2B,CAC5B,eAQC,yCxDxCa,CwDmCb,KAAM,CAMN,2BAA4B,CAC7B,iCAJC,WtDu2CsC,CsDx2CtC,MAAO,CAEP,eAAgB,CAHhB,OAc2B,CAR5B,kBAOC,sCxDjDa,CwDkDb,0BAA2B,CAC5B,gBAGC,cAAe,CFzEf,gBAEE,UAAW,CACX,WAFA,aAEW,CACZ,cGJC,azDqBa,CyDtBf,wCAMM,atDyJ6B,CsD/JnC,gBACE,azDgBc,CyDjBhB,4CAMM,atDyJ6B,CsD/JnC,cACE,azDwBa,CyDzBf,wCAMM,atDyJ6B,CsD/JnC,WACE,azDyBU,CyD1BZ,kCAMM,atDyJ6B,CsD/JnC,cACE,azD0Ba,CyD3Bf,wCAMM,atDoJ6B,CsD1JnC,aACE,azD2BY,CyD5Bd,sCAMM,atDyJ6B,CsD/JnC,YACE,azDUc,CyDXhB,oCAMM,atDoJ6B,CsD1JnC,WACE,azDkBc,CyDnBhB,kCAMM,atDyJ6B,CsD/JnC,oBACE,azDuBmB,CyDxBrB,oDAMM,atDyJ6B,CsD/JnC,YACE,azDmBW,CyDpBb,oCAMM,atDyJ6B,CsD/JnC,gDAMM,UtDoJ6B,CsDnJ9B,OCLL,iBAAkB,CAClB,UAAW,CAFb,cAOI,WAFA,aAAc,CACd,kCACW,CAPf,SAeI,YAFA,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UACY,CACb,WAKC,sBAAgD,CADlD,WACE,qBAAgD,CADlD,YACE,wBAAgD,CADlD,YACE,2BAAgD,CACjD,WCpBD,KzDm4BsC,CyD/3BvC,yBAFC,MAAO,CAHP,cAAe,CAEf,OAAQ,CAER,YzDg4BsC,CyD/3BvC,cAKC,QzD03BsC,CyDv3BvC,YAQK,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YzD42BkC,CSv0BpC,yBgDxCA,eACE,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YzD42BkC,CyD32BnC,ChDoCD,yBgDxCA,eACE,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YzD42BkC,CyD32BnC,ChDoCD,yBgDxCA,eACE,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YzD42BkC,CyD32BnC,ChDoCD,0BgDxCA,eACE,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YzD42BkC,CyD32BnC,ChDoCD,0BgDxCA,gBACE,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YzD42BkC,CyD32BnC,CCvBL,2ECUE,4BAAiC,CAEjC,mBANA,oBAAsB,CAEtB,qBAAuB,CACvB,yBAA2B,CAF3B,mBAAqB,CAHrB,2BAA6B,CAO7B,4BAA8B,CAN9B,mBAOoB,CDTrB,sBEEG,QAAS,CAGT,WAFA,MAAO,CAJP,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAIN,SACW,CACZ,eCTD,eAAgB,CAChB,sBAAuB,CACvB,kBAAmB,CC2Cf,gBAEI,iCAA+D,CAFnE,WAEI,4BAA+D,CAFnE,cAEI,+BAA+D,CAFnE,cAEI,+BAA+D,CAFnE,mBAEI,oCAA+D,CAFnE,gBAEI,iCAA+D,CAFnE,aAEI,oBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,YAEI,oBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,iBAEI,yBAA+D,CAFnE,kBAEI,0BAA+D,CAFnE,iBAEI,yBAA+D,CAFnE,UAEI,wBAA+D,CAFnE,gBAEI,8BAA+D,CAFnE,SAEI,uBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,aAEI,2BAA+D,CAFnE,cAEI,4BAA+D,CAFnE,QAEI,sBAA+D,CAFnE,eAEI,6BAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,qDAA+D,CAFnE,WAEI,yDAA+D,CAFnE,WAEI,oDAA+D,CAFnE,cAEI,sDAA+D,CAFnE,aAEI,sDAA+D,CAFnE,iBAEI,qDAA+D,CAFnE,gBAEI,qDAA+D,CAFnE,aAEI,2FAA+D,CAFnE,aAEI,yBAA+D,CAFnE,iBAEI,yBAA+D,CAFnE,mBAEI,2BAA+D,CAFnE,mBAEI,2BAA+D,CAFnE,gBAEI,wBAA+D,CAFnE,iBAEI,iCAAqD,CAArD,yBAA+D,CAFnE,OAEI,eAA+D,CAFnE,UAEI,kBAA+D,CAFnE,SAEI,gBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,OAEI,iBAA+D,CAFnE,QAEI,mBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,kBAEI,wCAA+D,CAFnE,oBAEI,oCAA+D,CAFnE,oBAEI,oCAA+D,CAFnE,QAEI,kCAA+D,CAFnE,UAEI,kBAA+D,CAFnE,YAEI,sCAA+D,CAFnE,cAEI,sBAA+D,CAFnE,cAEI,wCAA+D,CAFnE,gBAEI,wBAA+D,CAFnE,eAEI,yCAA+D,CAFnE,iBAEI,yBAA+D,CAFnE,aAEI,uCAA+D,CAFnE,eAEI,uBAA+D,CAFnE,gBAEI,8BAA+D,CAFnE,kBAEI,8BAA+D,CAFnE,gBAEI,8BAA+D,CAFnE,aAEI,8BAA+D,CAFnE,gBAEI,8BAA+D,CAFnE,eAEI,8BAA+D,CAFnE,cAEI,8BAA+D,CAFnE,aAEI,8BAA+D,CAFnE,sBAEI,8BAA+D,CAFnE,cAEI,8BAA+D,CAFnE,cAEI,2BAA+D,CAFnE,iBAEI,yCAA+D,CAFnE,iBAEI,yCAA+D,CAFnE,iBAEI,8BAA+D,CAFnE,iBAEI,8BAA+D,CAFnE,oBAEI,yCAA+D,CAFnE,oBAEI,wCAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,MAEI,mBAA+D,CAFnE,MAEI,mBAA+D,CAFnE,MAEI,mBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,QAEI,oBAA+D,CAFnE,OAEI,uBAA+D,CAFnE,OAEI,uBAA+D,CAFnE,OAEI,uBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,YAEI,yBAA+D,CAFnE,MAEI,oBAA+D,CAFnE,MAEI,oBAA+D,CAFnE,MAEI,oBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,yBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,OAEI,qBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,YAEI,0BAA+D,CAFnE,WAEI,uBAA+D,CAFnE,UAEI,4BAA+D,CAFnE,aAEI,+BAA+D,CAFnE,kBAEI,oCAA+D,CAFnE,qBAEI,uCAA+D,CAFnE,aAEI,qBAA+D,CAFnE,aAEI,qBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,aAEI,0BAA+D,CAFnE,mBAEI,gCAA+D,CAFnE,OAEI,eAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,mBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,kBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,kBAA+D,CAFnE,OAEI,oBAA+D,CAFnE,OAEI,kBAA+D,CAFnE,OAEI,kBAA+D,CAFnE,QAEI,kBAA+D,CAFnE,QAEI,kBAA+D,CAFnE,QAEI,kBAA+D,CAFnE,QAEI,mBAA+D,CAFnE,QAEI,mBAA+D,CAFnE,QAEI,mBAA+D,CAFnE,QAEI,mBAA+D,CAFnE,uBAEI,oCAA+D,CAFnE,qBAEI,kCAA+D,CAFnE,wBAEI,gCAA+D,CAFnE,yBAEI,uCAA+D,CAFnE,wBAEI,sCAA+D,CAFnE,wBAEI,sCAA+D,CAFnE,mBAEI,gCAA+D,CAFnE,iBAEI,8BAA+D,CAFnE,oBAEI,4BAA+D,CAFnE,sBAEI,8BAA+D,CAFnE,qBAEI,6BAA+D,CAFnE,qBAEI,kCAA+D,CAFnE,mBAEI,gCAA+D,CAFnE,sBAEI,8BAA+D,CAFnE,uBAEI,qCAA+D,CAFnE,sBAEI,oCAA+D,CAFnE,uBAEI,+BAA+D,CAFnE,iBAEI,yBAA+D,CAFnE,kBAEI,+BAA+D,CAFnE,gBAEI,6BAA+D,CAFnE,mBAEI,2BAA+D,CAFnE,qBAEI,6BAA+D,CAFnE,oBAEI,4BAA+D,CAFnE,aAEI,kBAA+D,CAFnE,SAEI,iBAA+D,CAFnE,SAEI,iBAA+D,CAFnE,SAEI,iBAA+D,CAFnE,SAEI,iBAA+D,CAFnE,SAEI,iBAA+D,CAFnE,SAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,KAEI,kBAA+D,CAFnE,KAEI,uBAA+D,CAFnE,KAEI,sBAA+D,CAFnE,KAEI,uBAA+D,CAFnE,KAEI,qBAA+D,CAFnE,KAEI,uBAA+D,CAFnE,KAEI,qBAA+D,CAFnE,KAEI,uBAA+D,CAFnE,KAEI,qBAA+D,CAFnE,KAEI,qBAA+D,CAFnE,MAEI,qBAA+D,CAFnE,MAEI,qBAA+D,CAFnE,MAEI,qBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,MAEI,gDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,sDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,sDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,sDAA+D,CAFnE,MAEI,sDAA+D,CAFnE,OAEI,sDAA+D,CAFnE,OAEI,sDAA+D,CAFnE,OAEI,sDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,MAEI,gDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,sDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,sDAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,sDAA+D,CAFnE,MAEI,sDAA+D,CAFnE,OAEI,sDAA+D,CAFnE,OAEI,sDAA+D,CAFnE,OAEI,sDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,yBAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,yBAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,yBAA+D,CAFnE,MAEI,yBAA+D,CAFnE,OAEI,yBAA+D,CAFnE,OAEI,yBAA+D,CAFnE,OAEI,yBAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,MAEI,wBAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,MAEI,yBAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,MAEI,uBAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,MAEI,wBAA+D,CAFnE,MAEI,uBAA+D,CAFnE,MAEI,wBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,wBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,wBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,OAEI,sBAA+D,CAFnE,OAEI,sBAA+D,CAFnE,OAEI,sBAA+D,CAFnE,OAEI,uBAA+D,CAFnE,OAEI,uBAA+D,CAFnE,OAEI,uBAA+D,CAFnE,OAEI,uBAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,QAEI,wDAA+D,CAFnE,QAEI,wDAA+D,CAFnE,QAEI,wDAA+D,CAFnE,QAEI,0DAA+D,CAFnE,QAEI,0DAA+D,CAFnE,QAEI,0DAA+D,CAFnE,QAEI,0DAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,4DAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,QAEI,wDAA+D,CAFnE,QAEI,wDAA+D,CAFnE,QAEI,wDAA+D,CAFnE,QAEI,0DAA+D,CAFnE,QAEI,0DAA+D,CAFnE,QAEI,0DAA+D,CAFnE,QAEI,0DAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,QAEI,0BAA+D,CAFnE,QAEI,0BAA+D,CAFnE,QAEI,0BAA+D,CAFnE,QAEI,2BAA+D,CAFnE,QAEI,2BAA+D,CAFnE,QAEI,2BAA+D,CAFnE,QAEI,2BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,QAEI,4BAA+D,CAFnE,QAEI,4BAA+D,CAFnE,QAEI,4BAA+D,CAFnE,QAEI,6BAA+D,CAFnE,QAEI,6BAA+D,CAFnE,QAEI,6BAA+D,CAFnE,QAEI,6BAA+D,CAFnE,OAEI,+BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,+BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,+BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,+BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,QAEI,6BAA+D,CAFnE,QAEI,6BAA+D,CAFnE,QAEI,6BAA+D,CAFnE,QAEI,8BAA+D,CAFnE,QAEI,8BAA+D,CAFnE,QAEI,8BAA+D,CAFnE,QAEI,8BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,QAEI,2BAA+D,CAFnE,QAEI,2BAA+D,CAFnE,QAEI,2BAA+D,CAFnE,QAEI,4BAA+D,CAFnE,QAEI,4BAA+D,CAFnE,QAEI,4BAA+D,CAFnE,QAEI,4BAA+D,CAFnE,KAEI,mBAA+D,CAFnE,KAEI,wBAA+D,CAFnE,KAEI,uBAA+D,CAFnE,KAEI,wBAA+D,CAFnE,KAEI,sBAA+D,CAFnE,KAEI,wBAA+D,CAFnE,KAEI,sBAA+D,CAFnE,KAEI,wBAA+D,CAFnE,KAEI,sBAA+D,CAFnE,KAEI,sBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,sBAA+D,CAFnE,MAEI,uBAA+D,CAFnE,MAEI,uBAA+D,CAFnE,MAEI,uBAA+D,CAFnE,MAEI,uBAA+D,CAFnE,MAEI,kDAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,MAEI,kDAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,0DAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,4DAA+D,CAFnE,MAEI,wDAA+D,CAFnE,MAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,wDAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,OAEI,0DAA+D,CAFnE,MAEI,uBAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,0BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,MAEI,yBAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,MAEI,+BAA+D,CAFnE,MAEI,8BAA+D,CAFnE,MAEI,+BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,+BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,+BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,OAEI,8BAA+D,CAFnE,MAEI,wBAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,4BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,MAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,2BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,gBAEI,8CAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,6BAA+D,CAFnE,MAEI,0BAA+D,CAFnE,OAEI,4BAA+D,CAFnE,OAEI,6BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,UAEI,yBAA+D,CAFnE,YAEI,6BAA+D,CAFnE,WAEI,yBAA+D,CAFnE,SAEI,yBAA+D,CAFnE,WAEI,yBAA+D,CAFnE,MAEI,uBAA+D,CAFnE,OAEI,yBAA+D,CAFnE,SAEI,yBAA+D,CAFnE,OAEI,yBAA+D,CAFnE,YAEI,yBAA+D,CAFnE,UAEI,0BAA+D,CAFnE,aAEI,2BAA+D,CAFnE,sBAEI,8BAA+D,CAFnE,2BAEI,mCAA+D,CAFnE,8BAEI,sCAA+D,CAFnE,gBAEI,kCAA+D,CAFnE,gBAEI,kCAA+D,CAFnE,iBAEI,mCAA+D,CAFnE,WAEI,4BAA+D,CAFnE,aAEI,4BAA+D,CAFnE,YAEI,8BAA+D,CAA/D,+BAA+D,CAFnE,cAEI,uBAA+D,CAFnE,gBAEI,uBAA+D,CAFnE,cAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,cAEI,uBAA+D,CAFnE,aAEI,uBAA+D,CAFnE,YAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,oBAEI,uBAA+D,CAFnE,YAEI,uBAA+D,CAFnE,YAEI,oBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,YAEI,uBAA+D,CAFnE,eAEI,oCAA+D,CAFnE,eAEI,iCAA+D,CAFnE,eAEI,kCAA+D,CAFnE,eAEI,kCAA+D,CAFnE,eAEI,mCAA+D,CAFnE,eAEI,kCAA+D,CAFnE,YAEI,uBAA+D,CAFnE,YAEI,kCAA+D,CAFnE,cAEI,kCAA+D,CAFnE,YAEI,kCAA+D,CAFnE,SAEI,kCAA+D,CAFnE,YAEI,kCAA+D,CAFnE,WAEI,kCAA+D,CAFnE,UAEI,kCAA+D,CAFnE,SAEI,kCAA+D,CAFnE,kBAEI,kCAA+D,CAFnE,UAEI,kCAA+D,CAFnE,UAEI,+BAA+D,CAFnE,aAEI,6CAA+D,CAFnE,aAEI,kCAA+D,CAFnE,aAEI,kCAA+D,CAFnE,aAEI,kCAA+D,CAFnE,aAEI,kCAA+D,CAFnE,gBAEI,4CAA+D,CAFnE,aAEI,6CAA+D,CAFnE,iBAEI,iCAAqD,CAArD,8BAAqD,CAArD,yBAA+D,CAFnE,kBAEI,kCAAqD,CAArD,+BAAqD,CAArD,0BAA+D,CAFnE,kBAEI,kCAAqD,CAArD,+BAAqD,CAArD,0BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,WAEI,yBAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,+BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,gBAEI,2BAA+D,CAFnE,cAEI,6BAA+D,CAFnE,aAEI,wCAA+D,CAFnE,0BAEI,yCAA+D,CAFnE,6BAEI,4CAA+D,CAFnE,+BAEI,2CAA+D,CAFnE,eAEI,wCAA+D,CAFnE,SAEI,4BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,SAEI,iBAA+D,CAFnE,QAEI,gBAA+D,CAFnE,mBAEI,8BAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CrDYrE,yBqDdE,gBAEI,oBAA+D,CAFnE,cAEI,qBAA+D,CAFnE,eAEI,oBAA+D,CAFnE,aAEI,wBAA+D,CAFnE,mBAEI,8BAA+D,CAFnE,YAEI,uBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,YAEI,uBAA+D,CAFnE,gBAEI,2BAA+D,CAFnE,iBAEI,4BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,kBAEI,6BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,oBAEI,iCAAqD,CAArD,yBAA+D,CAFnE,WAEI,kCAA+D,CAFnE,aAEI,kBAA+D,CAFnE,eAEI,sCAA+D,CAFnE,iBAEI,sBAA+D,CAFnE,iBAEI,wCAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,kBAEI,yCAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,gBAEI,uCAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,cAEI,uBAA+D,CAFnE,aAEI,4BAA+D,CAFnE,gBAEI,+BAA+D,CAFnE,qBAEI,oCAA+D,CAFnE,wBAEI,uCAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,cAEI,wBAA+D,CAFnE,gBAEI,0BAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,UAEI,eAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,0BAEI,oCAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,2BAEI,gCAA+D,CAFnE,4BAEI,uCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,oBAEI,8BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,0BAEI,qCAA+D,CAFnE,yBAEI,oCAA+D,CAFnE,0BAEI,+BAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,qBAEI,+BAA+D,CAFnE,mBAEI,6BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,gBAEI,kBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,eAEI,iBAA+D,CAFnE,QAEI,kBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,YAEI,yBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,YAEI,0BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,QAEI,mBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,eAEI,yBAA+D,CAFnE,aAEI,0BAA+D,CAFnE,gBAEI,2BAA+D,CAElE,CrDUH,yBqDdE,gBAEI,oBAA+D,CAFnE,cAEI,qBAA+D,CAFnE,eAEI,oBAA+D,CAFnE,aAEI,wBAA+D,CAFnE,mBAEI,8BAA+D,CAFnE,YAEI,uBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,YAEI,uBAA+D,CAFnE,gBAEI,2BAA+D,CAFnE,iBAEI,4BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,kBAEI,6BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,oBAEI,iCAAqD,CAArD,yBAA+D,CAFnE,WAEI,kCAA+D,CAFnE,aAEI,kBAA+D,CAFnE,eAEI,sCAA+D,CAFnE,iBAEI,sBAA+D,CAFnE,iBAEI,wCAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,kBAEI,yCAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,gBAEI,uCAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,cAEI,uBAA+D,CAFnE,aAEI,4BAA+D,CAFnE,gBAEI,+BAA+D,CAFnE,qBAEI,oCAA+D,CAFnE,wBAEI,uCAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,cAEI,wBAA+D,CAFnE,gBAEI,0BAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,UAEI,eAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,0BAEI,oCAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,2BAEI,gCAA+D,CAFnE,4BAEI,uCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,oBAEI,8BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,0BAEI,qCAA+D,CAFnE,yBAEI,oCAA+D,CAFnE,0BAEI,+BAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,qBAEI,+BAA+D,CAFnE,mBAEI,6BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,gBAEI,kBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,eAEI,iBAA+D,CAFnE,QAEI,kBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,YAEI,yBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,YAEI,0BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,QAEI,mBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,eAEI,yBAA+D,CAFnE,aAEI,0BAA+D,CAFnE,gBAEI,2BAA+D,CAElE,CrDUH,yBqDdE,gBAEI,oBAA+D,CAFnE,cAEI,qBAA+D,CAFnE,eAEI,oBAA+D,CAFnE,aAEI,wBAA+D,CAFnE,mBAEI,8BAA+D,CAFnE,YAEI,uBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,YAEI,uBAA+D,CAFnE,gBAEI,2BAA+D,CAFnE,iBAEI,4BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,kBAEI,6BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,oBAEI,iCAAqD,CAArD,yBAA+D,CAFnE,WAEI,kCAA+D,CAFnE,aAEI,kBAA+D,CAFnE,eAEI,sCAA+D,CAFnE,iBAEI,sBAA+D,CAFnE,iBAEI,wCAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,kBAEI,yCAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,gBAEI,uCAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,cAEI,uBAA+D,CAFnE,aAEI,4BAA+D,CAFnE,gBAEI,+BAA+D,CAFnE,qBAEI,oCAA+D,CAFnE,wBAEI,uCAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,cAEI,wBAA+D,CAFnE,gBAEI,0BAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,UAEI,eAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,0BAEI,oCAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,2BAEI,gCAA+D,CAFnE,4BAEI,uCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,oBAEI,8BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,0BAEI,qCAA+D,CAFnE,yBAEI,oCAA+D,CAFnE,0BAEI,+BAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,qBAEI,+BAA+D,CAFnE,mBAEI,6BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,gBAEI,kBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,eAEI,iBAA+D,CAFnE,QAEI,kBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,YAEI,yBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,YAEI,0BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,QAEI,mBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,eAEI,yBAA+D,CAFnE,aAEI,0BAA+D,CAFnE,gBAEI,2BAA+D,CAElE,CrDUH,0BqDdE,gBAEI,oBAA+D,CAFnE,cAEI,qBAA+D,CAFnE,eAEI,oBAA+D,CAFnE,aAEI,wBAA+D,CAFnE,mBAEI,8BAA+D,CAFnE,YAEI,uBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,YAEI,uBAA+D,CAFnE,gBAEI,2BAA+D,CAFnE,iBAEI,4BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,kBAEI,6BAA+D,CAFnE,WAEI,sBAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,oBAEI,iCAAqD,CAArD,yBAA+D,CAFnE,WAEI,kCAA+D,CAFnE,aAEI,kBAA+D,CAFnE,eAEI,sCAA+D,CAFnE,iBAEI,sBAA+D,CAFnE,iBAEI,wCAA+D,CAFnE,mBAEI,wBAA+D,CAFnE,kBAEI,yCAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,gBAEI,uCAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,WAEI,wBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,SAEI,oBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,cAEI,uBAA+D,CAFnE,aAEI,4BAA+D,CAFnE,gBAEI,+BAA+D,CAFnE,qBAEI,oCAA+D,CAFnE,wBAEI,uCAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,gBAEI,qBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,kBAEI,uBAA+D,CAFnE,cAEI,wBAA+D,CAFnE,gBAEI,0BAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,UAEI,eAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,mBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,UAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,0BAEI,oCAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,2BAEI,gCAA+D,CAFnE,4BAEI,uCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,2BAEI,sCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,oBAEI,8BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,wBAEI,kCAA+D,CAFnE,sBAEI,gCAA+D,CAFnE,yBAEI,8BAA+D,CAFnE,0BAEI,qCAA+D,CAFnE,yBAEI,oCAA+D,CAFnE,0BAEI,+BAA+D,CAFnE,oBAEI,yBAA+D,CAFnE,qBAEI,+BAA+D,CAFnE,mBAEI,6BAA+D,CAFnE,sBAEI,2BAA+D,CAFnE,wBAEI,6BAA+D,CAFnE,uBAEI,4BAA+D,CAFnE,gBAEI,kBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,YAEI,iBAA+D,CAFnE,eAEI,iBAA+D,CAFnE,QAEI,kBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,QAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,gDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,sDAA+D,CAFnE,SAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,YAEI,sDAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,YAEI,yBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,YAEI,0BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,QAEI,mBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,uBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,wBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,QAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,kDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,0DAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,4DAA+D,CAFnE,SAEI,wDAA+D,CAFnE,SAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,SAEI,yBAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,SAEI,0BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,8BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,+BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,4BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,6BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,SAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,eAEI,yBAA+D,CAFnE,aAEI,0BAA+D,CAFnE,gBAEI,2BAA+D,CAElE,CrDUH,0BqDdE,iBAEI,oBAA+D,CAFnE,eAEI,qBAA+D,CAFnE,gBAEI,oBAA+D,CAFnE,cAEI,wBAA+D,CAFnE,oBAEI,8BAA+D,CAFnE,aAEI,uBAA+D,CAFnE,YAEI,sBAA+D,CAFnE,aAEI,uBAA+D,CAFnE,iBAEI,2BAA+D,CAFnE,kBAEI,4BAA+D,CAFnE,YAEI,sBAA+D,CAFnE,mBAEI,6BAA+D,CAFnE,YAEI,sBAA+D,CAFnE,qBAEI,yBAA+D,CAFnE,uBAEI,2BAA+D,CAFnE,uBAEI,2BAA+D,CAFnE,oBAEI,wBAA+D,CAFnE,qBAEI,iCAAqD,CAArD,yBAA+D,CAFnE,YAEI,kCAA+D,CAFnE,cAEI,kBAA+D,CAFnE,gBAEI,sCAA+D,CAFnE,kBAEI,sBAA+D,CAFnE,kBAEI,wCAA+D,CAFnE,oBAEI,wBAA+D,CAFnE,mBAEI,yCAA+D,CAFnE,qBAEI,yBAA+D,CAFnE,iBAEI,uCAA+D,CAFnE,mBAEI,uBAA+D,CAFnE,UAEI,mBAA+D,CAFnE,UAEI,mBAA+D,CAFnE,UAEI,mBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,YAEI,oBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,YAEI,wBAA+D,CAFnE,YAEI,wBAA+D,CAFnE,YAEI,wBAA+D,CAFnE,YAEI,wBAA+D,CAFnE,YAEI,wBAA+D,CAFnE,YAEI,wBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,UAEI,oBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,WAEI,qBAA+D,CAFnE,YAEI,sBAA+D,CAFnE,YAEI,sBAA+D,CAFnE,YAEI,sBAA+D,CAFnE,YAEI,sBAA+D,CAFnE,YAEI,sBAA+D,CAFnE,YAEI,sBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,cAEI,4BAA+D,CAFnE,iBAEI,+BAA+D,CAFnE,sBAEI,oCAA+D,CAFnE,yBAEI,uCAA+D,CAFnE,iBAEI,qBAA+D,CAFnE,iBAEI,qBAA+D,CAFnE,mBAEI,uBAA+D,CAFnE,mBAEI,uBAA+D,CAFnE,eAEI,wBAA+D,CAFnE,iBAEI,0BAA+D,CAFnE,uBAEI,gCAA+D,CAFnE,WAEI,eAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,mBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,oBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,WAEI,kBAA+D,CAFnE,YAEI,kBAA+D,CAFnE,YAEI,kBAA+D,CAFnE,YAEI,kBAA+D,CAFnE,YAEI,mBAA+D,CAFnE,YAEI,mBAA+D,CAFnE,YAEI,mBAA+D,CAFnE,YAEI,mBAA+D,CAFnE,2BAEI,oCAA+D,CAFnE,yBAEI,kCAA+D,CAFnE,4BAEI,gCAA+D,CAFnE,6BAEI,uCAA+D,CAFnE,4BAEI,sCAA+D,CAFnE,4BAEI,sCAA+D,CAFnE,uBAEI,gCAA+D,CAFnE,qBAEI,8BAA+D,CAFnE,wBAEI,4BAA+D,CAFnE,0BAEI,8BAA+D,CAFnE,yBAEI,6BAA+D,CAFnE,yBAEI,kCAA+D,CAFnE,uBAEI,gCAA+D,CAFnE,0BAEI,8BAA+D,CAFnE,2BAEI,qCAA+D,CAFnE,0BAEI,oCAA+D,CAFnE,2BAEI,+BAA+D,CAFnE,qBAEI,yBAA+D,CAFnE,sBAEI,+BAA+D,CAFnE,oBAEI,6BAA+D,CAFnE,uBAEI,2BAA+D,CAFnE,yBAEI,6BAA+D,CAFnE,wBAEI,4BAA+D,CAFnE,iBAEI,kBAA+D,CAFnE,aAEI,iBAA+D,CAFnE,aAEI,iBAA+D,CAFnE,aAEI,iBAA+D,CAFnE,aAEI,iBAA+D,CAFnE,aAEI,iBAA+D,CAFnE,aAEI,iBAA+D,CAFnE,gBAEI,iBAA+D,CAFnE,SAEI,kBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,SAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,qBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,YAEI,qBAA+D,CAFnE,UAEI,gDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,WAEI,sDAA+D,CAFnE,WAEI,sDAA+D,CAFnE,WAEI,sDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,aAEI,sDAA+D,CAFnE,UAEI,gDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,sDAA+D,CAFnE,UAEI,sDAA+D,CAFnE,WAEI,sDAA+D,CAFnE,WAEI,sDAA+D,CAFnE,WAEI,sDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,aAEI,sDAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,yBAA+D,CAFnE,WAEI,yBAA+D,CAFnE,WAEI,yBAA+D,CAFnE,WAEI,yBAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,aAEI,yBAA+D,CAFnE,UAEI,wBAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,aAEI,2BAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,aAEI,4BAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,aAEI,0BAA+D,CAFnE,UAEI,wBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,wBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,wBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,wBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,sBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,WAEI,uBAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,YAEI,wDAA+D,CAFnE,YAEI,wDAA+D,CAFnE,YAEI,wDAA+D,CAFnE,YAEI,0DAA+D,CAFnE,YAEI,0DAA+D,CAFnE,YAEI,0DAA+D,CAFnE,YAEI,0DAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,4DAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,YAEI,wDAA+D,CAFnE,YAEI,wDAA+D,CAFnE,YAEI,wDAA+D,CAFnE,YAEI,0DAA+D,CAFnE,YAEI,0DAA+D,CAFnE,YAEI,0DAA+D,CAFnE,YAEI,0DAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,YAEI,0BAA+D,CAFnE,YAEI,0BAA+D,CAFnE,YAEI,0BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,YAEI,6BAA+D,CAFnE,YAEI,6BAA+D,CAFnE,YAEI,6BAA+D,CAFnE,YAEI,6BAA+D,CAFnE,WAEI,+BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,+BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,+BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,+BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,YAEI,6BAA+D,CAFnE,YAEI,6BAA+D,CAFnE,YAEI,6BAA+D,CAFnE,YAEI,8BAA+D,CAFnE,YAEI,8BAA+D,CAFnE,YAEI,8BAA+D,CAFnE,YAEI,8BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,YAEI,2BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,YAEI,4BAA+D,CAFnE,SAEI,mBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,uBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,wBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,SAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,sBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,kDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,kDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,0DAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,4DAA+D,CAFnE,UAEI,wDAA+D,CAFnE,UAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,wDAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,WAEI,0DAA+D,CAFnE,UAEI,uBAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,0BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,UAEI,yBAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,UAEI,0BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,8BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,+BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,6BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,WAEI,8BAA+D,CAFnE,UAEI,wBAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,4BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,6BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,UAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,2BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,WAEI,4BAA+D,CAFnE,gBAEI,yBAA+D,CAFnE,cAEI,0BAA+D,CAFnE,iBAEI,2BAA+D,CAElE,CCfP,aDWM,gBAEI,wBAA+D,CAFnE,sBAEI,8BAA+D,CAFnE,eAEI,uBAA+D,CAFnE,cAEI,sBAA+D,CAFnE,eAEI,uBAA+D,CAFnE,mBAEI,2BAA+D,CAFnE,oBAEI,4BAA+D,CAFnE,cAEI,sBAA+D,CAFnE,qBAEI,6BAA+D,CAFnE,cAEI,sBAA+D,CAElE,CEhDP,UACE,8BAA2C,CvDsEzC,4BuDhEJ,YAGI,+BAAiC,CAQpC,CvDwCG,yBuDnDJ,YAQI,0BAA8C,CAD9C,2BAA4B,CAE5B,wBAAyB,CAE5B,CAKD,yBACE,uDAAoE,CACrE,wBAGC,0DAAmE,CACpE,wBAGC,4IAMmB,CACpB,cAMC,iGAAwH,CACzH,YCzCC,kBAAmB,CACnB,2BAA4B,CAC7B,kBAKC,oBAA+B,CADjC,mCAKI,kCAAqB,CAArB,2BADA,wCAC2B,CAC5B,gBAMD,uBAAgC,CADlC,iCAKI,kCAAqB,CAArB,2BADA,uCAC2B,CAC5B,MC3BD,mDAAqD,CACtD,wBAIC,2FAAuC,CACvC,+BAAkC,CACnC,8BAIC,0FAA0C,CAC1C,+BAAkC,CACnC,SCbC,iBAAkB,CADpB,gBAOI,sBADkB,QAAS,CAF3B,UAAW,CAEkB,MAAO,CADpC,iBAAkB,CACV,OAAQ,CAAhB,KACsB,CCLxB,wBACE,wBtEea,CsEdd,qCAMC,4DAAiE,C3DgDjE,yB2DjDF,qCAKM,uEAAiF,CAGtF,CARD,6EACE,4DAAiE,C3DgDjE,yB2DjDF,uCAKM,wEAAiF,CAGtF,CAfD,0BACE,wBtEUc,CsETf,uCAMC,4DAAiE,C3DgDjE,yB2DjDF,uCAKM,uEAAiF,CAGtF,CARD,iFACE,4DAAiE,C3DgDjE,yB2DjDF,yCAKM,wEAAiF,CAGtF,CAfD,wBACE,wBtEkBa,CsEjBd,qCAMC,4DAAiE,C3DgDjE,yB2DjDF,qCAKM,uEAAiF,CAGtF,CARD,6EACE,4DAAiE,C3DgDjE,yB2DjDF,uCAKM,wEAAiF,CAGtF,CAfD,qBACE,wBtEmBU,CsElBX,kCAMC,4DAAiE,C3DgDjE,yB2DjDF,kCAKM,uEAAiF,CAGtF,CARD,uEACE,4DAAiE,C3DgDjE,yB2DjDF,oCAKM,wEAAiF,CAGtF,CAfD,wBACE,wBtEoBa,CsEnBd,qCAMC,4DAAiE,C3DgDjE,yB2DjDF,qCAKM,uEAAiF,CAGtF,CARD,6EACE,4DAAiE,C3DgDjE,yB2DjDF,uCAKM,wEAAiF,CAGtF,CAfD,uBACE,wBtEqBY,CsEpBb,oCAMC,4DAAiE,C3DgDjE,yB2DjDF,oCAKM,uEAAiF,CAGtF,CARD,2EACE,4DAAiE,C3DgDjE,yB2DjDF,sCAKM,wEAAiF,CAGtF,CAfD,sBACE,wBtEIc,CsEHf,mCAMC,4DAAiE,C3DgDjE,yB2DjDF,mCAKM,uEAAiF,CAGtF,CARD,yEACE,4DAAiE,C3DgDjE,yB2DjDF,qCAKM,wEAAiF,CAGtF,CAfD,qBACE,wBtEYc,CsEXf,kCAMC,4DAAiE,C3DgDjE,yB2DjDF,kCAKM,uEAAiF,CAGtF,CARD,uEACE,4DAAiE,C3DgDjE,yB2DjDF,oCAKM,wEAAiF,CAGtF,CAfD,8BACE,wBtEiBmB,CsEhBpB,2CAMC,4DAAiE,C3DgDjE,yB2DjDF,2CAKM,uEAAiF,CAGtF,CARD,yFACE,4DAAiE,C3DgDjE,yB2DjDF,6CAKM,wEAAiF,CAGtF,CAfD,sBACE,wBtEaW,CsEZZ,mCAMC,4DAAiE,C3DgDjE,yB2DjDF,mCAKM,uEAAiF,CAGtF,CARD,yEACE,4DAAiE,C3DgDjE,yB2DjDF,qCAKM,wEAAiF,CAGtF,CAfD,sBACE,qBtEGW,CsEFZ,mCAMC,yDAAiE,C3DgDjE,yB2DjDF,mCAKM,iEAAiF,CAGtF,CARD,yEACE,yDAAiE,C3DgDjE,yB2DjDF,qCAKM,kEAAiF,CAGtF,CDoBD,mBAGI,UAAS,CAHb,mBAGI,UAAS,CAHb,mBAGI,UAAS,CAHb,mBAGI,UAAS,CAHb,mBAGI,UAAS,CAHb,mBAGI,UAAS,CAHb,mBAGI,UAAS,CAHb,mBAGI,UAAS,CAHb,mBAGI,UAAS,CACV,QEvCH,QAAS,CADT,OAAQ,CAER,8BAAgC,CACjC,mBCHC,uFAAiF,C7DyD/E,yB8DtDJ,SAKM,wCAAmE,CAGxE,C9D8CG,yB8DtDJ,SAKM,wCAAmE,CAGxE,C9D8CG,yB8DtDJ,SAKM,wCAAmE,CAGxE,C9D8CG,0B8DtDJ,SAKM,wCAAmE,CAGxE,C9D2DG,4B+DlEF,cACE,6BAAsC,CACvC,CCPH,KACE,iBAAkB,CACnB,oB5EsCD,K4ElCI,iBAAkB,CACnB,CCLH,WACE,wBAA2B,CAC3B,eAAgB,CAChB,yJACoF,CAGtF,WACE,wBAA2B,CAC3B,eAAgB,CAChB,uJACmF,CAGrF,WACE,wBAA2B,CAC3B,eAAgB,CAChB,mJACiF,CAMnF,WACE,mBAAsB,CAGtB,kBADA,eAAgB,CADhB,uLAEkB,C7E0DpB,c6EnDE,kBAAmB,C7EmDrB,4B6E/CE,qB5EygB8B,CD1dhC,O6E3CE,e5EugBkB,CW7ehB,4BZiBJ,O6ExCI,kB5E8gBsB,C4E5gBzB,C7E2CD,O6ExCE,gB5EggBmB,CW9ejB,4BZsBJ,O6ErCI,gB5EugBoB,C4ErgBvB,C7EwCD,O6ErCE,e5EyfkB,CW/ehB,4BZ2BJ,O6ElCI,kB5EggBsB,C4E9fzB,C7EqCD,O6ElCE,gB5EkfmB,CWhfjB,4BZgCJ,O6E/BI,mB5EyfuB,C4Evf1B,C7EkCD,O6E/BE,e5E2ekB,CDvcpB,O6EhCE,gB5EwemB,C4EzerB,wDAII,oBAAqB,CACtB,4CAOD,qB5Ege6B,CK9hB7B,WuEkEA,e5E2duB,CWpfrB,4BNzCF,WuEqEE,kB5Ege4B,C4E9d/B,CvEvEC,WuE0EA,e5EmduB,CWpfrB,4BNzCF,WuE6EE,gB5Eyd0B,C4Evd7B,CvE/EC,WuEkFA,e5E2cuB,CWpfrB,4BNzCF,WuEqFE,iB5Ekd2B,C4Ehd9B,CvEvFC,WuE0FA,e5EmcuB,CWpfrB,4BNzCF,WuE6FE,c5E2cwB,CK/iB5B,MuE8GI,mB5EocyB,C4E5c5B,CAeD,gBACE,iBAAkB,CAClB,cAAe,CACf,UAAW,CvEtDb,mBuE0DE,YAAa,CvE1Df,0BuE6DI,YAAa,CACd,kBAQD,YADA,iBACY,CACb,OAMC,UAAW,CACZ,mBAGC,UAAyB,CAC1B,OAGC,WAAY,CAEb,mBAGC,UAAyB,C7EqH3B,K6E9GE,gB5EugBqB,C4EtgBtB,WCrLC,WAAY,CACZ,6BAFA,WAE6B,CAC9B,8BAOC,eADA,iBACe,CtEQjB,QsEFE,aAAc,CtEOhB,YsEHE,kB7E6CW,C6E5CZ,mBCtBC,qBADA,qBAAqC,CAFrC,iBAAkB,CAClB,SAEoC,CACrC,aAGC,iBAAkB,CACnB,gECHG,qBAAsB,CtDX1B,qBuDME,ahFWgB,CyBjBlB,gEuDYE,gBhF6KoB,CyBzLtB,sIuDYE,gBhF6KoB,CuBrLtB,Y0DME,eAAgB,CACjB,wBAGC,kB/E8sB+C,C+E7sBhD,mCAKC,mCjFTa,CiFUd,2CAGC,qBjFba,CuB+Ff,a0DzEE,mBjFslB+B,CiFrlBhC,+BAGC,ejFklB+B,CiFjlBhC,+BAGC,gCAA+E,CAOhF,oGAGC,oK9EiCgF,C8EhCjF,uDAGC,uK9E6BgF,C8E5BjF,iBC7CC,mBADA,elFuLoB,CkFzLpB,2BlF0mByI,CkFzmBzI,sBlFiLsB,CkF9KvB,uDAWC,mBAAoB,CACpB,gBAFA,oBAAqB,CADrB,kBAAmB,CAFnB,cAAe,CACf,eAIgB,CACjB,yCAKC,eAFA,cAAe,CACf,eACe,CAChB,YCzBC,kBnF2X6B,CmF1X9B,gBCMC,yCADA,qBpF2nB4B,CoF5nB5B,kBpF+KsB,CoFlLtB,uBpF0mByI,CoFxmBzI,epFuLoB,CoFxLpB,mDAI2D,CAC5D,8BCPC,2BrFmLsB,CqF/KvB,gEADC,mBADA,erFwLoB,CqFzLpB,sBrFkLsB,CqFzKvB,SCXC,cAAe,CACf,UAAW,CACZ,+BAGC,kCAAoC,CACrC,QAOC,mBADA,sBtFwKsB,C4B/HtB,W0DjCA,qBtFfa,CsFgBb,atFJe,CsFEjB,kCAKI,qBtFnBW,CsFoBX,aAA2B,CAN/B,kBAUI,wBAAoC,CACpC,aAA2B,CAC5B,sBAID,8BAAkC,CAClC,atFzBgB,CsFuBlB,6BAKI,wBAAoC,CAEpC,gBADA,aACgB,CACjB,iBAID,qCtFtCgB,CsFuChB,UtF1Ca,CsFwCf,sEAKI,sCtF1Cc,CsF2Cd,UtF9CW,CsFoDZ,cAID,wBtFpDgB,CsFqDhB,UtFzDa,CsFuDf,6DAUI,wBAAuC,CACvC,aAAyB,CAC1B,iBAID,qCtFnEgB,CsFoEhB,UtFxEa,CsFsEf,sEAKI,sCtFvEc,CsFwEd,UtF5EW,C4BsDb,mB0DgCA,oBtFnFgB,CsFoFhB,atF7Ea,CsF2Ef,4EAKI,wBAAoC,CACpC,oBtFvFc,CsFwFd,atFlFW,CsFyFZ,sBAID,oBtFpGgB,CsFqGhB,atF5Fe,CsF0FjB,qFAWI,wBtFrGa,CsFsGb,oBtFtGa,CsFuGb,UtFnHW,CsFoHZ,kBCzHD,mCvFiBe,CuFhBf,avFgBe,CuFXd,yEAFC,oCAAmC,CACnC,avFYa,CuFNd,oBAXD,oCvFYgB,CuFXhB,avFWgB,CuFNf,+EAFC,qCAAmC,CACnC,avFOc,CuFDf,kBAXD,oCvFoBe,CuFnBf,avFmBe,CuFdd,yEAFC,qCAAmC,CACnC,avFea,CuFTd,eAXD,qCvFqBY,CuFpBZ,avFoBY,CuFfX,gEAFC,sCAAmC,CACnC,avFgBU,CuFVX,kBAXD,qCvFsBe,CuFrBf,avFqBe,CuFhBd,yEAFC,sCAAmC,CACnC,avFiBa,CuFXd,iBAXD,mCvFuBc,CuFtBd,avFsBc,CuFjBb,sEAFC,oCAAmC,CACnC,avFkBY,CuFZb,gBAXD,qCvFMgB,CuFLhB,avFKgB,CuFAf,mEAFC,sCAAmC,CACnC,avFCc,CuFKf,eAXD,kCvFcgB,CuFbhB,avFagB,CuFRf,gEAFC,mCAAmC,CACnC,avFSc,CuFHf,wBAXD,qCvFmBqB,CuFlBrB,avFkBqB,CuFbpB,2FAFC,sCAAmC,CACnC,avFcmB,CuFRpB,gBAXD,kCvFea,CuFdb,avFca,CuFTZ,mEAFC,mCAAmC,CACnC,avFUW,CuFJZ,gBAXD,mCvFKa,CuFJb,UvFIa,CuFCZ,mEAFC,oCAAmC,CACnC,UvFAW,CuFMZ,UD8HD,mBtFgBwB,CsFfzB,oBAQC,oBAFA,qBtF2I4B,CsF1I5B,sBtFQwB,CsFNzB,sBAGC,aAAc,CACd,aAAc,CACd,SAAU,CACX,iEAGC,qBtF6I+B,CsF5I/B,sBtF4I+B,CsF3IhC,iEAGC,qBtFmI+B,CsFlI/B,sBtFkI+B,CsFjIhC,2BAGC,mBtF4a6B,CsF3a7B,oBtF2a6B,C0Btfc,e8D5F3C,oBxFsa+B,CwFra/B,qBxFqa+B,C+B1SjC,eyDlHE,oBxF4Z+B,CwF3Z/B,sBAFA,cAAe,CADf,UxF8Z+B,C+B1SjC,0CyD9GI,YAAa,CACd,8BAID,iBxFkoB8B,CwFjoB/B,eAMC,aAAc,CADhB,qBAII,uBAAwB,CAJ5B,0CAQI,YAAa,CzDiJjB,iByDrIE,gBxFuJoB,CwFtJpB,exF0IoB,CwFvIpB,qBAFA,gBxFygBmB,CwF/gBnB,oBxF6X+B,CwF5X/B,qBxF4X+B,CwF1X/B,oBxFuK4B,CwFxK5B,aAAc,CAKd,wBACqB,CxDzBnB,uBwDkCF,qBAAuB,CACvB,gBAHA,mBAAsB,CACtB,qBAEgB,CACjB,gCAGC,eAAgB,CACjB,kBAMC,WxFulBuB,CwFtlBvB,oBxFilB6B,CwFhlB9B,kBAGC,exFmlB2B,CwFllB5B,kBAGC,exFglB2B,CwF/kB5B,kBAGC,exF6kB2B,CwF5kB5B,oCAIC,uBxF4jBiC,CwF3jBlC,oEAIC,mBxFujBiC,CwFtjBjC,oBxFsjBiC,CwFrjBjC,mBxFqjBiC,CwFpjBjC,oBxFojBiC,CwFnjBlC,wBAMC,axFiT2B,CmC9Y7B,QsDVE,8BvFqY+C,CuFpY/C,0CAA4C,CAC5C,YvF+3BsC,CS3zBpC,4B8E9DJ,6CAKM,azFDY,CyFJlB,8MAcM,azFLW,CyFTjB,8CAkBM,azFdY,CyFeb,CAOL,oIAEE,8BAA+C,CAC/C,+BAAgD,CtDYlD,csDLE,ezF4IoB,CyF3IrB,kBAGC,iBvF+3B6E,CuF93B7E,UAAW,CtD2Mb,2BsDvME,8BAA+B,C9EiB7B,4B8EXJ,YAKI,wBAAyB,CACzB,yBAA0B,CAC1B,oBAHA,wBzF6NyB,CyF9NzB,UzFoHsB,CyF9GzB,CtDAD,sBsDME,ezF8GoB,CyF/GtB,wDAII,YAAa,CACd,kCAOD,iBAAkB,CADpB,wCASI,wCAFA,ezFqSsB,CyFxStB,UAAW,CAIX,MAAO,CAHP,iBAAkB,CAClB,OAGoD,C9EvBpD,4BwBkCJ,iBsDKI,qBzFxGW,CyFyGX,qBzFwCoB,CyFvCpB,2CzFoDoD,CyFzDpD,WAAY,CADZ,SzFpCS,CyFsCT,sCAAkD,CAKlD,iBAAkB,CAClB,kBAVA,cAAe,CACf,QzFnCS,CyFuCT,uBAKkB,CACnB,kDAQC,2BAFA,8BvF6Q6C,CuF5Q7C,qCAC2B,CAC5B,sBAGC,SAAU,CACV,kBAAmB,CACpB,4BAGC,SAAU,CACV,mBAAoB,CACrB,CAOH,wBACE,gBAAiB,CAClB,iCAGC,iBAAkB,CAElB,UzF9EW,CyF6EX,QzF7EW,CyF+EX,SAAU,C9E3FR,yB8EuFJ,iCAOI,YAAa,CAEhB,C9EnFG,4B8EwFJ,uCAGI,czF4e4B,CyF/ehC,iDAMM,4BzF7JY,CyFkKlB,uCAII,iBADA,aACiB,CARhB,CtDjGL,2BsDiHE,eAAgB,C9E5Hd,yBwBWJ,2BsDoHI,2CzFrBoD,CyFuBvD,C9EpHG,4B8EsHJ,4CAGI,WAAY,CACZ,SAAU,CAId,sDAGI,uBAAyB,CAI7B,qDAII,mBzF1IS,CyF2IT,mBAFA,gBAEyB,CAd5B,C9EzIG,yB8EkJJ,qDASI,SAAU,CACV,UAAW,CAEd,C9EjJG,4B8EsJJ,0FAII,aAAc,CACd,cAAe,CACf,cAAe,CACf,eAAgB,CAEnB,CAKD,6CAGI,YAAa,C9EvKb,4B8E2KJ,wDAGI,oBzF2ZyC,CyF1ZzC,mBAAoB,CAEvB,CAED,sCACE,YAAa,CADf,4CAII,gBAAiB,C9EvLjB,4B8E2LJ,6DAKM,eAAgB,CALtB,iFAUM,yBADA,8BACyB,CAC1B,CAOL,gCAKE,kBAAmB,CAEnB,8BAA2C,CAE3C,kCADA,8BzFrIsB,CyF+HtB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,sBAAuB,CAEvB,wBzFnIsB,CW7FpB,yB8E0NJ,gCAYI,YAAa,CAqBhB,CAjCD,uCAwBI,mCAAwC,CACxC,sBAHA,QAAS,CAJT,UAAW,CAKX,MAAO,CAJP,iBAAkB,CAElB,OAAQ,CADR,KAKsB,CAzB1B,kCA+BI,iBAAkB,C9EzPlB,yB8E6PJ,oGAII,uBzF0V+B,CyFnVnC,2BAGI,wBzFkG6B,CyF9FjC,0FAII,uBzFwU+B,CyFxVlC,CAsBD,4CAEE,kBAAmB,CAGnB,cAJA,YAAa,CAGb,mBzFjRW,CyFgRX,gBAEc,CALhB,kDAQI,oBAAqB,C9EjSrB,yBwBWJ,2BsDgSI,YAAa,CACb,SAAU,CACV,8BvF+B6C,CuF9B7C,qCAA0D,CAC3D,oCAGC,0BAA2B,CAC5B,qCAGC,QAAS,CACT,8BAAgC,CACjC,mCAGC,aAAc,CACf,gCAGC,aAAc,CACd,SAAU,CACX,yCAGC,uBAAwB,CACzB,0CAGC,yBAA6B,CAC9B,CrDpYH,MsDIE,iBAAkB,CAClB,UAAW,CtDsEb,asDlEE,4BAA6B,CAC7B,+B1FDgB,CoC+BlB,WsD1BE,aAAc,CACd,WAAY,CtDwEd,asDpEE,sBAAuB,CACxB,yFAIC,eADA,iBACe,CAChB,6CAMC,aAAc,CADhB,qEAII,oBAAqB,CACtB,YAOD,sBAAuB,CACxB,4BAGC,cAAe,CACf,eAAgB,CACjB,aAMC,iBAAkB,CACnB,mBAaC,mDAAgC,CADhC,wBAAyB,CAEzB,8B1FmFsB,C0FlFtB,gCALA,sBAAuB,CAFvB,oBAAmC,CALnC,UAAW,CACX,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAEN,U1F0FsB,C0FjFvB,sBAIC,wCADA,oBACwE,CACzE,sBAIC,wCADA,oBACwE,C/ErBtE,yB+E2BJ,qBAKM,mB1F+kBoB,C0FplB1B,qBAYM,mB1FwkBoB,C0FvkBpB,oB1FukBoB,C0F7kBvB,CAcH,cACE,iCAAgD,C/EjD9C,yB+EgDJ,cAII,iCAAgD,CAEnD,CAED,gBACE,iCAAgD,C/EzD9C,yB+EwDJ,gBAII,iC1FiCoB,C0F1BxB,iBAGI,WAAY,CACZ,UAAW,CAJf,mBAOM,wBAA0B,CAC1B,uBAAyB,CAb9B,CtDED,kBsDoBE,YAAa,CACb,qBAAsB,CACtB,Y1F2SkB,C0F1SnB,6BAMC,kCADA,kCvFgD8D,CuFjD9D,uBvFiD8D,CuF9C/D,wBAIC,eAAgB,CACjB,mCAGC,0BAA2B,CAC3B,uBAAwB,CACzB,yCAGC,uBAAwB,C/ExDtB,kDyB4CJ,YsDsBI,+BAAiC,CtDtBrC,kBsDyBM,SAAU,CACX,CAOL,WAGE,kBAAmB,CAFnB,YAAa,CACb,cAAe,CAEf,qB1FweyB,C0Fve1B,mBAIC,kBADA,U1FoeyB,C0Fle1B,qBAOC,yBADA,yBACyB,CAC1B,WAOC,sBADA,yBACsB,CACvB,6BAIC,+BADA,uBAC+B,CAChC,yCAGC,oBAAqB,CACtB,sBAMC,iBAAkB,CACnB,6BAQC,8B1F1Ne,C0FwNf,QAAS,CAHT,UAAW,CAMX,aAFA,U1FgNkB,C0FnNlB,iBAAkB,CAClB,KAIa,CAKd,0DAGC,uBAA6B,CAC9B,qCAGC,aAAc,C/ElLZ,4B+E0LF,YAKE,gBADA,oBAAoC,CADpC,qBAAqC,CADrC,cAAe,CADf,UAIgB,CACjB,yEAOC,gBADA,oBAAoC,CADpC,qBAEgB,CACjB,yCAGC,aAA6B,CAC9B,CrDpRH,kCsDIE,eAAgB,CACjB,uDAGC,YAAa,CtDRf,wBsDYE,gB3F2DW,CqCgBb,gBsDrEE,aAAc,CACf,iBCjBC,kB5FoLsB,CsC/KxB,yCsDAM,eAAgB,CAChB,mBAAsB,CACvB,mBAQH,YAAa,CACb,gBAAiB,CACjB,eAAgB,CAHlB,oCAMI,kBAAmB,CANvB,sCAYI,YAAa,CpD5Bf,0BqDGA,oBADA,e7FkLwB,CwC5KlB,iDqDAN,kCADA,8B7FiJsB,CwC1IhB,gDqDDN,mCADA,+B7F4IsB,CwCxJtB,0BqDkBA,oBADA,e7FmKwB,CwC5KlB,iDqDeN,kCADA,8B7FkIsB,CwC1IhB,gDqDcN,mCADA,+B7F6HsB,C6F3HvB,mBCvBG,oBAAqB,CACtB,UAOD,mB9FwqByB,C8FvqB1B,sBASC,oBAHA,wBAA2C,CAC3C,mB9FsdsB,C8FrdtB,oB9FoIwB,C8FvI1B,wBAOI,aAAc,CACd,SAAU,CARd,+BAaI,YAA8C,CAC9C,iB9FmpBqB,C8FlpBrB,kB9FkpBqB,C8FjpBtB,aAOD,iBAAkB,CAClB,Y5Fq1BsC,C4Fp1BvC,oBAIC,WADA,QACW,CACZ,qBAIC,aADA,UACa,CACd,4BAMC,+BAAmC,CACnC,uBAA0B,CAF5B,oEAKI,wBAAoC,CACpC,a9F/Ca,C8FgDd,gCAID,kCAAqC,CACrC,oBAA0C,CAF5C,4EAKI,wBAAsC,CACtC,U9FrEW,C8FsEZ,uBC3ED,mC/FiBe,C+FhBf,a/FgBe,C+Fdf,sEAEE,mC/FYa,C+FXb,a/FWa,C+FVd,yBAPD,oC/FYgB,C+FXhB,a/FWgB,C+FThB,0EAEE,oC/FOc,C+FNd,a/FMc,C+FLf,uBAPD,oC/FoBe,C+FnBf,a/FmBe,C+FjBf,sEAEE,oC/Fea,C+Fdb,a/Fca,C+Fbd,oBAPD,qC/FqBY,C+FpBZ,a/FoBY,C+FlBZ,gEAEE,qC/FgBU,C+FfV,a/FeU,C+FdX,uBAPD,qC/FsBe,C+FrBf,a/FqBe,C+FnBf,sEAEE,qC/FiBa,C+FhBb,a/FgBa,C+Ffd,sBAPD,mC/FuBc,C+FtBd,a/FsBc,C+FpBd,oEAEE,mC/FkBY,C+FjBZ,a/FiBY,C+FhBb,qBAPD,qC/FMgB,C+FLhB,a/FKgB,C+FHhB,kEAEE,qC/FCc,C+FAd,a/FAc,C+FCf,oBAPD,kC/FcgB,C+FbhB,a/FagB,C+FXhB,gEAEE,kC/FSc,C+FRd,a/FQc,C+FPf,6BAPD,qC/FmBqB,C+FlBrB,a/FkBqB,C+FhBrB,kFAEE,qC/FcmB,C+FbnB,a/FamB,C+FZpB,qBAPD,kC/Fea,C+Fdb,a/Fca,C+FZb,kEAEE,kC/FUW,C+FTX,a/FSW,C+FRZ,qBAPD,mC/FKa,C+FJb,U/FIa,C+FFb,kEAEE,mC/FAW,C+FCX,U/FDW,C+FEZ,wBAPD,oC/FYgB,C+FXhB,a/FWgB,C+FThB,wEAEE,oC/FOc,C+FNd,a/FMc,C+FLf,mBDwFD,wB9FpFgB,C8FqFhB,U9F3Fa,C8FoGb,uEACE,U9FrGW,C8FoGb,kBACE,a9F5Fc,C8F2FhB,iBACE,U9FrGW,C8FoGb,gBACE,a9F5Fc,C8F2FhB,uDACE,U9FrGW,C8FoGb,gBACE,a9F5Fc,C0CgChB,erBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,2BACE,UxCyJiC,CwCxJlC,qDqDMG,UhGHS,CgGDb,kBAQI,wBAAsE,CtDkC1E,iBrBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,6BACE,UxCyJiC,CwCxJlC,yDqDMG,UhGHS,CgGDb,oBAQI,wBAAsE,CtDkC1E,erBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,2BACE,UxCyJiC,CwCxJlC,qDqDMG,UhGHS,CgGDb,kBAQI,wBAAsE,CtDkC1E,YrBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,wBACE,UxCyJiC,CwCxJlC,+CqDMG,UhGHS,CgGDb,eAQI,wBAAsE,CtDkC1E,erBhDA,wBlBuJmC,CwCvJnC,qBAFA,axCyJmC,CwCrJnC,2BACE,axCyJiC,CwCxJlC,qDqDMG,ahGMY,CgGVhB,kBAQI,wBAAsE,CtDkC1E,crBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,0BACE,UxCyJiC,CwCxJlC,mDqDMG,UhGHS,CgGDb,iBAQI,wBAAsE,CtDkC1E,arBhDA,wBlBuJmC,CwCvJnC,qBAFA,axCyJmC,CwCrJnC,yBACE,axCyJiC,CwCxJlC,iDqDMG,ahGMY,CgGVhB,gBAQI,wBAAsE,CtDkC1E,YrBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,wBACE,UxCyJiC,CwCxJlC,+CqDMG,UhGHS,CgGDb,eAQI,wBAAsE,CtDkC1E,qBrBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,iCACE,UxCyJiC,CwCxJlC,iEqDMG,UhGHS,CgGDb,wBAQI,wBAAsE,CtDkC1E,arBhDA,wBlBuJmC,CwCvJnC,qBAFA,UxCyJmC,CwCrJnC,yBACE,UxCyJiC,CwCxJlC,iDqDMG,UhGHS,CgGDb,gBAQI,wBAAsE,CtDkC1E,arBhDA,qBlBuJmC,CwCvJnC,kBAFA,axCyJmC,CwCrJnC,yBACE,axCyJiC,CwCxJlC,iDqDMG,ahGMY,CgGVhB,gBAQI,wBAAsE,CACvE,mCCXD,cAAe,CACf,eAAgB,CAJpB,+CAQM,aADA,uBACa,CARnB,8CAaM,gBADA,0BACgB,CACjB,kCCXD,YAAa,CACd,8BCHD,qBAAsB,CACvB,kBAMC,iBAAkB,CAElB,YAAoB,CADpB,UAAkB,CAElB,YjGq3BsC,CkDzvBxC,gBgDnIE,cADA,gBpGkMoB,CoGnMpB,mBpGkN4B,CoGnN5B,gBpGiBe,CoGbhB,0CAGC,aAAc,CACf,QCLC,oBAAqB,CAGrB,kBADA,erGgsByB,CqGnsBzB,iBAAkB,CAElB,cAEgC,CALlC,cAcI,+KAJA,UAAW,CAGX,QAAS,CAFT,iBAAkB,CAClB,OAKK,CACN,YAKD,WAAY,CACZ,mBAAY,CAAZ,iBAFA,UAEiB,CAClB,cAIC,kBAAmB,CAInB,wBrGxBgB,CqGyBhB,WANA,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CACvB,UrG3Ba,CqG+Bd,6CAeG,kBAJA,SAAU,CAFV,UAAW,CAKX,UAAW,CAJX,iBAAkB,CAElB,QAAS,CACT,SAEkB,CAVtB,uDAcI,sDAAiB,CAAjB,8CAAiB,CACjB,2BAAW,CAAX,mBAAoB,CACrB,sBAID,wBrGzCe,CqG0ChB,uBAGC,wBrGvDgB,CqGwDjB,WAQC,oBADA,WrG6nBmB,CqG9nBnB,UAE8B,CAC/B,WAKC,gBADA,arGwnBqB,CqGznBrB,YAE8B,CAO/B,sBAKC,iBADA,crG6mBsB,CqG9mBtB,aAE8B,C1FnC5B,yB0FgCJ,WAQI,qBADA,WrGymBiB,CqG1mBjB,UAE8B,CAEjC,CAED,YAGE,qBADA,WrGkmBmB,CqGnmBnB,UAE8B,C1F/C5B,yB0F4CJ,YAQI,qBADA,WrG8lBkB,CqG/lBlB,UAE+B,CAElC,CAKD,oBACE,YAAgC,CACjC,uBAGC,gBAA8B,CAC/B,uBAGC,UAA8B,CAC/B,uBAGC,UAA8B,CAC/B,uBAGC,gBAA8B,CAC/B,wBAGC,gBAA+B,CAChC,cAMC,mBAAoB,CADtB,8BAMI,sBAAmC,CANvC,oCAUI,mBAAiC,CAVrC,oCAcI,oBAAiC,CAdrC,oCAkBI,qBAAiC,CAlBrC,oCAsBI,iBAAiC,CAtBrC,sCA0BI,oBAAkC,CA1BtC,uCAgCI,qDAAiB,CAAjB,6CAAiB,CACjB,2BAAW,CAAX,mBAAoB,CAjCxB,4BAuCI,uBAAgB,CAAhB,eAAgB,CAChB,SAAU,CAxCd,oCA2CM,2DAAiB,CAAjB,mDAAiB,CACjB,2BAAW,CAAX,mBAAoB,CA5C1B,+CA+CQ,gEAAY,CAAZ,wDAAiB,CC/LzB,2BAII,iBAAkB,CACnB,eAKD,yBADA,UtGFgB,CsGIjB,8BAKC,mBAAY,CAAZ,iBADA,iBACiB,CAClB,sDAUuB,qBADH,gBAAiB,CAApC,gBAAiB,CACjB,mBAA2C,CAC5C,sDAKuB,qBADH,iBAAkB,CAArC,gBAAiB,CACjB,mBAA2C,CAC5C,cAQC,iBAAkB,CACnB,sBAGC,iBAAkB,CACnB,8BAGC,0BAA2B,CAC5B,0DAKG,QAAS,CAAE,MAAO,CAClB,iBAAkB,CAClB,SAAU,CALd,yDASY,OAAQ,CAAhB,KAAM,CACN,iBAAkB,CACnB,4DAKD,0BAA2B,CAC5B,4FAIC,iBAAkB,CACnB,4FAIC,iBAAkB,CAClB,SAAU,CACX,8CAGS,OAAR,KAAe,CAChB,8CAGS,QAAR,KAAgB,CACjB,8CAGC,QAAS,CAAE,OAAQ,CACpB,8CAGC,QAAS,CAAE,MAAO,CACnB,uBC9FC,8BAA+B,CAChC,UCFC,YADA,UxGktBmB,CwGhtBpB,gDAIC,2BAA6B,CAC9B,aAOC,cADA,YxGssBmB,CwGpsBpB,aAIC,eADA,axGksBoB,CwGhsBrB,aAIC,YADA,UxG8rBiB,CwG5rBlB,aAIC,YADA,UxG0rBiB,CwGxrBlB,aAOC,kBAAmB,CAInB,kBALA,mBAAoB,CAIpB,gBxGgrB0B,CwGlrB1B,sBAAuB,CACvB,eAEkB,CANpB,iBASI,kBxG6qB4B,CwG5qB7B,MC3CD,qBAFA,eAAgB,CAChB,cACqB,CACtB,sBAGC,iBzG8tB0B,CyG7tB3B,WAKC,kBAAmB,CAEnB,cAJA,YAAa,CACb,gBAAiB,CAEjB,kBzGDgB,CyGHlB,kCASI,cADA,oBzGAa,CyGEd,YCnBD,qB1G6uBiC,C0G5uBjC,wF1G6uB6G,C0G5uB9G,gBCFC,iBAAkB,CACnB,uBASC,oBAAqB,CADrB,kBAAmB,CADnB,oBAAqC,CAGrC,WAJA,MAAO,CAHP,iBAAkB,CAElB,OAAQ,CADR,KAMW,CACZ,OCXC,oBADA,iBACoB,CACrB,SAGC,aAAc,CACf,gCAGC,eAAgB,CACjB,kCAGC,kBAAmB,CACpB,2DAOC,WACE,YAAa,CACd,CAMH,WAGE,OADA,OAAQ,CADR,KAEO,CACR,aAGC,8BAA+B,CAChC,WAKC,QAAS,CACT,OAFA,OAAQ,CADR,KAGO,CACR,aAKC,cAAe,CAFf,gBAAiB,CAGjB,aAAc,CAFd,WAAY,CAGZ,4BAA6B,CAC9B,cAIC,QAAS,CACT,OAFA,OAEO,CACR,gBAGC,2BAA4B,CAC7B,kBAIC,WAAY,CACZ,OAFA,OAEO,CACR,oBAGC,2BAA4B,CAC7B,aAKC,QAAS,CACT,OAFA,OAAQ,CADR,KAGO,CACR,eAKC,cAAe,CACf,YAAa,CAHb,gBAAiB,CACjB,WAAY,CAGZ,6BAA8B,CAC/B,iBAOC,YADA,UACY,CACb,iBAIC,YADA,UACY,CACb,cAMC,oBAAqB,CACtB,cAQC,OADA,OAAQ,CADR,KAEO,CjG1DL,yBiGuDJ,cAMI,QAAS,CACT,QAAS,CACT,UAAW,CAEd,CAED,gBAEE,YADA,UACY,CjGrEV,yBiGmEJ,gBAMI,YADA,UACY,CAEf,CAED,cAGE,OADA,OAAQ,CADR,KAEO,CjGhFL,yBiG6EJ,cAMI,QAAS,CAET,UADA,SACU,CAEb,CAED,gBAEE,YADA,UACY,CjG3FV,yBiGyFJ,gBAOI,YADA,WAAY,CADZ,UAEY,CAEf,CAED,cAGE,OADA,OAAQ,CADR,KAEO,CACR,gBAIC,YADA,UACY,CACb,cAIC,QAAS,CACT,QAAS,CAFT,KAAM,CAIN,sCADA,UACsC,CACvC,gBAIC,WAAY,CACZ,gBAAiB,CACjB,kBAHA,UAGkB,CjG3HhB,yBkG1DJ,SAKI,yBAA0B,CAC1B,cAHA,cAAe,CACf,QAEc,CAIlB,eAGI,MAAO,CAIX,aAGI,OAAQ,CAZX,CAcA,gECjBC,kCAAqB,CAArB,2BAHA,SAAU,CACV,qCAA0D,CAC1D,+CAC2B,CAK5B,wFAFG,SAAU,CACX,iCAID,8DAAgH,CAKjH,6CAFG,sDAA4F,CAC7F,+BAID,6DAA+G,CAKhH,2CAFG,qDAA2F,CAC5F,cCxBD,yCAAyC,CAC1C,gBAGC,yBAA2B,CAC5B,MAGC,YAAa,CACd,SCRC,eADA,iBACe,CAChB,eAGC,YAAa,CACd,eAGC,YAAa,CACd,wBAMC,UAAW,CACZ,8CAIC,mBADA,UhHiDW,CgH/CZ,sDAGC,ahHRe,CgHShB,iFAMC,cAAe,CAEf,qBAKA,oBhHinB6B,CgHhnB7B,sBAPA,UhHunB6B,CgH/mB9B,+GAIC,oBhH2mB6B,CgH1mB7B,qBhH0mB6B,CgHxmB7B,qBhHwmB6B,CgHvmB9B,iFAIC,mBhH+lBiC,CgH9lBjC,oBhH8lBiC,CgH7lBlC,+GAIC,mBhHylBiC,CgHxlBjC,oBhHwlBiC,CgHtlBjC,oBhHslBiC,CgHrlBlC,sBAMC,ahH1DgB,CgH2DhB,SAAU,CACX,6DAQC,4BADA,4BAC4B,CAC7B,2FAMC,yBADA,yBAA0B,CAD1B,YAEyB,CAC1B,uCAYC,4BAA6B,CAD7B,eAAgB,CADhB,cAAe,CAJf,oBAAqB,CAOrB,oBANA,YAAa,CAEb,SAAU,CADV,UhHmFwB,CgHxEzB,8NAIC,mBhHoEwB,CgHnEzB,yBAGC,cAAe,CAChB,wCAQC,wBhHrHgB,CgHsHhB,qBANA,oBAAqB,CAIrB,kBhH0DsB,CgH7DtB,kBAAmB,CAEnB,oBAAqB,CADrB,qBhH6BuB,CgHxBxB,yDAQC,4BAA6B,CAG7B,sQAAoT,CADpT,uBAAkC,CADlC,2BAA4B,CAG5B,yBAA0B,CAL1B,cAAe,CAHf,iBAAkB,CAUlB,UAAW,CARX,YAAa,CADb,SAAU,CAQV,mBAAoB,CAEpB,qEAZA,W9G+kBgG,C8GlkBjG,+DAGC,SAAU,CACX,UChJC,YAAa,CACb,sBAFA,iBAEsB,CACvB,YAIC,qBjHFa,CiHGb,yBjHCgB,CiHAhB,qBjH6IsB,CiH3ItB,ajHAgB,CiHGhB,cAAe,CADf,QAAS,CAPT,iBAAkB,CAIlB,iBAAkB,CAElB,8B/GyX+C,C+GtX/C,WAAY,CAVd,kBAaI,oBjHPc,CiHQd,ajHJW,CiHKZ,WAID,eAAgB,CAChB,QAAS,CAET,cADA,iBACc,CACf,2BAGC,oBjHde,CiHef,ajHfe,CiHgBhB,+BAIC,oBADA,gBACoB,CACrB,kDAGC,kCAAsC,CACtC,UAAY,CACZ,SAAU,CAHZ,wDAMI,SAAU,CAWb,qCAQC,sBAFA,QAAS,CACT,MAAO,CAJP,iBAAkB,CAElB,OAAQ,CADR,KjHyFsB,CiHpFvB,gBAMC,sBADA,WAAY,CAFZ,mBAAiB,CAAjB,gBAAiB,CACjB,UjHgFsB,CiH7EvB,iDAIC,gBADA,gBACgB,CAKjB,0BADC,eC1EkC,CD2EnC,IC5EC,kCAAmC,CACnC,kCAPA,6BAAiC,CACjC,iBAAkB,CAElB,mBAAoB,CAEpB,aAAc,CADd,mBAGkC,CACnC,oBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,6BAGC,eAAgB,CACjB,2BAGC,eAAgB,CACjB,4BAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,6BAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,8BAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,2BAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,4BAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,2BAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,4BAGC,eAAgB,CACjB,6BAGC,eAAgB,CACjB,4BAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,6BAGC,eAAgB,CACjB,2BAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,2BAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,4BAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,2BAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,2BAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,0BAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,yBAGC,eAAgB,CACjB,uBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,cAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,wBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,qBAGC,eAAgB,CACjB,kBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,iBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,gBAGC,eAAgB,CACjB,aAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,eAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,mBAGC,eAAgB,CACjB,oBAGC,eAAgB,CACjB,OAKC,mBlHp3BsB,CWxIpB,yBuG2/BJ,OAII,kBlHzfsB,CkH2fzB,CCzjCD,2BAEE,kBAAmB,CAInB,wBnHUe,CmHTf,4CnHuJsD,CmHtJtD,WAPA,YAAa,CAIb,mChH2K8D,CgH7K9D,sBAAuB,CACvB,kCnHAa,CmHJf,mEAWI,mBAAsB,CAX1B,kEAeI,wBnHCa,CmHAb,2CnH0I6C,CmHzI9C,oCAID,MAAO,CACP,8CAAkE,CxGkChE,yBwGpCJ,oCAKI,8BAAgC,CAMnC,CAXD,2CASI,eAAgB,CACjB,gCAID,OAAQ,CACR,6CAAiE,CxGqB/D,yBwGvBJ,gCAKI,6BAA+B,CAMlC,CAXD,uCASI,eAAgB,CACjB,sBAID,YAAa,CACd,kDAMC,qBnHlDa,CmHmDb,anHvCe,CmHwChB,mDAOC,aAAc,CADd,QAAS,CAET,cAAe,CAChB,4DAGC,SAAU,CACV,cAAe,CAChB,wDAGC,SAAU,CACV,YAAa,CACd,kDAMC,0BAA2B,CAC5B,2DAGC,WAAY,CACb,uDAGC,YAAa,CACd,8CAMC,gBAAiB,CAClB,6DAMC,uBAAwB,CACzB,8BAMC,oBAAoC,CACpC,qBAAqC,CACtC,MCjHC,4BAA6B,CAC9B,cCDC,8BrHgLsC,CqH/KvC,YAKC,qBrHDa,CqHEb,wBrHAgB,CqHChB,iCAA4D,CAC5D,cAJA,wBrHuR2B,CqHxR3B,iBrHWa,CqHLd,0BAGC,erHqIgB,CqHpIjB,qCAGC,wBAAyB,CACzB,yBAA0B,CAC3B,WAWC,2BAA4B,CAD5B,qBrHxBa,CqH0Bb,wBrHxBgB,CqH6Bd,qBrHkHoB,CS9IlB,eT0TiB,CqHtSrB,arHba,CqHQb,aAAc,CAGd,mBrHwJwB,CqHvJxB,erHgKoB,CqHrKpB,iBAAmD,CAGnD,wBrHmQ2B,CoBlRvB,qEiGcJ,UnHurBgG,CkBjsB5F,uCiGON,WjGNQ,eAAgB,CiGyCvB,CAnCD,uBA4BI,4BAA6B,CAC7B,QAAS,CA7Bb,iBAiCI,oBrHrCa,CqHsCd,WAID,iBAAkB,CAClB,kBAAmB,CACpB,2BASC,cADA,iBAAkB,CADlB,YrHwN2B,CqHzN3B,YrHxDgB,CqH4DjB,wBAGC,YAAa,CACd,YAMC,kBAAmB,CACnB,mBAAoB,CAFtB,wBAKI,cAAe,CALnB,uBASI,eAAgB,CACjB,mBAKD,eAAgB,CAChB,WAAY,CACZ,arHlFa,CqHmFb,cAAe,CAJf,gBAAiB,CAKjB,8BnHkS+C,CmHxSjD,yBASI,arHrFa,CqH4EjB,+BAaI,mBAAoB,CACrB,uBAID,arH9Fe,CqH+FhB,uBAGC,gBrHkEsB,CqHjEtB,erHiEsB,CqHhEvB,uBAGC,mBAAoB,CACpB,cAAe,CACf,oBAAqB,CACrB,qBAAsB,CACtB,SAAU,CACX,qBAGC,cAAe,CAChB,qBAGC,iBAAkB,CACnB,2BAGC,iBAAkB,CAClB,kBAAmB,CACpB,YAUC,qBrH/Ia,CqHgJb,4BrH8TgC,CqH7ThC,qBrHAsB,CqHGtB,2CARA,YAAa,CACb,gBAAiB,CAMjB,gBAAiB,CADjB,wBrHqI2B,CqH5I3B,iBAAkB,CAGlB,arHUgD,CqHdlD,qCAkBI,WAAY,CAHZ,UAAW,CAEX,QAAS,CADT,iBAAkB,CAGlB,0BAA2B,CAnB/B,mBAuBI,qCrH6S8B,CqH5S9B,mCAAoC,CACpC,oCAAqC,CAzBzC,kBA6BI,8BrHvKW,CqHwKX,mCAAoC,CACpC,oCAAqC,CACtC,gCAID,sBAAwB,CACzB,wBjHvKG,qBJyIoB,CC+FlB,kBAvE+B,C4BrDnC,qB7B4K2B,CoB3RvB,8HiG6KJ,UnHyciJ,CkBlnB7I,uCiGwKN,wBjGvKQ,eAAgB,CiG2KvB,CAED,mCACE,YAAa,CACd,kBAOC,4BAA6B,CAG7B,WAAY,CACZ,cARA,YAAa,CAKb,kBrHlBsB,CqHmBtB,erHZoB,CqHSpB,mCAAqE,CADrE,gCAAkE,CADlE,UrHnLa,CqHiLf,wBAYI,YAAa,CACd,6BAID,aAAc,CACf,8CAKC,kBAAmB,CACpB,4DAUC,4BAA2C,CjHrNzC,qBJyIoB,CqH6EtB,cAAe,CAPf,oBAAqB,CpHyBjB,kBAvE+B,CoH+CnC,erHrCoB,C6B/DpB,qB7B4K2B,CqHvE3B,iBAAkB,CjGpNd,8HiGuNJ,wBAAiB,CAAjB,qBAAiB,CAAjB,gBAAiB,CADjB,qBAAsB,CADtB,kBnHiaiJ,CkBlnB7I,uCiG4MN,4DjG3MQ,eAAgB,CiGkOvB,CAvBD,gJAeI,oBAAqB,CAfzB,gJAqBI,gBADA,SrHoD6B,CqHlD9B,uEhGlPD,wBrBmBe,C6BDf,oB7BCe,CSTX,gBoBMJ,U7BsSmB,C6BjSnB,mFRrBA,wBlB4JmC,C0BpIjC,qBAFA,U1BsIiC,C0BnIlC,4LRzBD,wBlB4JmC,C0B7HjC,oB1B6HiC,CMlJ/B,4CoBmBF,UAI4F,CAK7F,qfAQC,wB1B8GiC,C0B3GjC,qBAJA,U1B+GiC,C0BpHnC,ijBpB9BI,2CoB2CmG,CAKpG,kLAMD,wB7B7Ca,C6BgDb,qBAJA,U7B5Ca,C6BiDd,8BwFuLD,cAAe,CAChB,yCAGC,cAAe,CAChB,8BhGhQC,qBrBOa,CqH8Pb,qB5G3PI,eT4Se,C6BtSnB,a7BAgB,CqHoPhB,gBrH1PgB,C6BWhB,oCRrBA,qBlBuJmC,C0B/HjC,kBAFA,a1BiIiC,C0B9HlC,mFRzBD,qBlBuJmC,C0BxHjC,iB1BwHiC,CM7I/B,6CoBmBF,aAI4F,CAK7F,+NAQC,qB1ByGiC,C0BtGjC,kBAJA,a1B0GiC,C0B/GnC,6PpB9BI,4CoB2CmG,CAKpG,8EAMD,qB7BzDW,C6B4DX,kBAJA,a7BxDW,C6B6DZ,yCwFqMD,YAAa,CACd,sBAOC,kBADA,kBrHrC4B,CqHuC7B,eAIC,YADA,cACY", "file": "./assets/css/theme.bundle.css", "sourcesContent": ["/*!\n * Bootstrap v5.0.1 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n\n// scss-docs-start import-stack\n// Configuration\n@import \"functions\";\n@import \"variables\";\n@import \"mixins\";\n@import \"utilities\";\n\n// Layout & components\n@import \"root\";\n@import \"reboot\";\n@import \"type\";\n@import \"images\";\n@import \"containers\";\n@import \"grid\";\n@import \"tables\";\n@import \"forms\";\n@import \"buttons\";\n@import \"transitions\";\n@import \"dropdown\";\n@import \"button-group\";\n@import \"nav\";\n@import \"navbar\";\n@import \"card\";\n@import \"accordion\";\n@import \"breadcrumb\";\n@import \"pagination\";\n@import \"badge\";\n@import \"alert\";\n@import \"progress\";\n@import \"list-group\";\n@import \"close\";\n@import \"toasts\";\n@import \"modal\";\n@import \"tooltip\";\n@import \"popover\";\n@import \"carousel\";\n@import \"spinners\";\n@import \"offcanvas\";\n\n// Helpers\n@import \"helpers\";\n\n// Utilities\n@import \"utilities/api\";\n// scss-docs-end import-stack\n", "/**\n * Custom variables followed by theme variables followed by Bootstrap variables\n * to ensure cascade of styles.\n */\n\n// Bootstrap functions\n@import '~bootstrap/scss/functions';\n\n// Theme functions\n@import 'theme/functions';\n\n// Custom variables\n@import 'user-variables';\n\n// Theme variables\n@import 'theme/variables';\n\n// Bootstrap core\n@import '~bootstrap/scss/bootstrap';\n\n// Theme core\n@import 'theme/theme';\n\n// Custom core\n@import 'user';", ":root {\n  // Custom variable values only support SassScript inside `#{}`.\n  @each $color, $value in $colors {\n    --#{$variable-prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$variable-prefix}#{$color}: #{$value};\n  }\n\n  // Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$variable-prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$variable-prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$variable-prefix}gradient: #{$gradient};\n}\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n\n// Root\n//\n// Ability to the value of the root font sizes, affecting the value of `rem`.\n// null by default, thus nothing is generated.\n\n:root {\n  font-size: $font-size-root;\n\n  @if $enable-smooth-scroll {\n    @media (prefers-reduced-motion: no-preference) {\n      scroll-behavior: smooth;\n    }\n  }\n}\n\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Prevent adjustments of font size after orientation changes in iOS.\n// 4. Change the default tap highlight to be completely transparent in iOS.\n\nbody {\n  margin: 0; // 1\n  font-family: $font-family-base;\n  @include font-size($font-size-base);\n  font-weight: $font-weight-base;\n  line-height: $line-height-base;\n  color: $body-color;\n  text-align: $body-text-align;\n  background-color: $body-bg; // 2\n  -webkit-text-size-adjust: 100%; // 3\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\n}\n\n\n// Content grouping\n//\n// 1. Reset Firefox's gray color\n// 2. Set correct height and prevent the `size` attribute to make the `hr` look like an input field\n\nhr {\n  margin: $hr-margin-y 0;\n  color: $hr-color; // 1\n  background-color: currentColor;\n  border: 0;\n  opacity: $hr-opacity;\n}\n\nhr:not([size]) {\n  height: $hr-height; // 2\n}\n\n\n// Typography\n//\n// 1. Remove top margins from headings\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n//    margin for easier control within type scales as it avoids margin collapsing.\n\n%heading {\n  margin-top: 0; // 1\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-style: $headings-font-style;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n}\n\nh1 {\n  @extend %heading;\n  @include font-size($h1-font-size);\n}\n\nh2 {\n  @extend %heading;\n  @include font-size($h2-font-size);\n}\n\nh3 {\n  @extend %heading;\n  @include font-size($h3-font-size);\n}\n\nh4 {\n  @extend %heading;\n  @include font-size($h4-font-size);\n}\n\nh5 {\n  @extend %heading;\n  @include font-size($h5-font-size);\n}\n\nh6 {\n  @extend %heading;\n  @include font-size($h6-font-size);\n}\n\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\n\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n\n// Abbreviations\n//\n// 1. Duplicate behavior to the data-bs-* attribute for our tooltip plugin\n// 2. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\n// 3. Add explicit cursor to indicate changed behavior.\n// 4. Prevent the text-decoration to be skipped.\n\nabbr[title],\nabbr[data-bs-original-title] { // 1\n  text-decoration: underline dotted; // 2\n  cursor: help; // 3\n  text-decoration-skip-ink: none; // 4\n}\n\n\n// Address\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\n\n// Lists\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\n// 1. Undo browser default\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // 1\n}\n\n\n// Blockquote\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\n\n// Strong\n//\n// Add the correct font weight in Chrome, Edge, and Safari\n\nb,\nstrong {\n  font-weight: $font-weight-bolder;\n}\n\n\n// Small\n//\n// Add the correct font size in all browsers\n\nsmall {\n  @include font-size($small-font-size);\n}\n\n\n// Mark\n\nmark {\n  padding: $mark-padding;\n  background-color: $mark-bg;\n}\n\n\n// Sub and Sup\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n\nsub,\nsup {\n  position: relative;\n  @include font-size($sub-sup-font-size);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n// Links\n\na {\n  color: $link-color;\n  text-decoration: $link-decoration;\n\n  &:hover {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n  &,\n  &:hover {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n// Code\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-code;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n  direction: ltr #{\"/* rtl:ignore */\"};\n  unicode-bidi: bidi-override;\n}\n\n// 1. Remove browser default top margin\n// 2. Reset browser default of `1em` to use `rem`s\n// 3. Don't allow content to break outside\n\npre {\n  display: block;\n  margin-top: 0; // 1\n  margin-bottom: 1rem; // 2\n  overflow: auto; // 3\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\ncode {\n  @include font-size($code-font-size);\n  color: $code-color;\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n\n  kbd {\n    padding: 0;\n    @include font-size(1em);\n    font-weight: $nested-kbd-font-weight;\n  }\n}\n\n\n// Figures\n//\n// Apply a consistent margin strategy (matches our type styles).\n\nfigure {\n  margin: 0 0 1rem;\n}\n\n\n// Images and content\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\n\n// Tables\n//\n// Prevent double borders\n\ntable {\n  caption-side: bottom;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: $table-cell-padding-y;\n  padding-bottom: $table-cell-padding-y;\n  color: $table-caption-color;\n  text-align: left;\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\n\n// Forms\n//\n// 1. Allow labels to use `margin` for spacing.\n\nlabel {\n  display: inline-block; // 1\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n// See https://github.com/twbs/bootstrap/issues/24093\n\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Explicitly remove focus outline in Chromium when it shouldn't be\n// visible (e.g. as result of mouse click or touch tap). It already\n// should be doing this automatically, but seems to currently be\n// confused and applies its very visible two-tone outline anyway.\n\nbutton:focus:not(:focus-visible) {\n  outline: 0;\n}\n\n// 1. Remove the margin in Firefox and Safari\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // 1\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\n// Remove the inheritance of text transform in Firefox\nbutton,\nselect {\n  text-transform: none;\n}\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nselect {\n  // Remove the inheritance of word-wrap in Safari.\n  // See https://github.com/twbs/bootstrap/issues/24990\n  word-wrap: normal;\n\n  // Undo the opacity change from Chrome\n  &:disabled {\n    opacity: 1;\n  }\n}\n\n// Remove the dropdown arrow in Chrome from inputs built with datalists.\n// See https://stackoverflow.com/a/54997118\n\n[list]::-webkit-calendar-picker-indicator {\n  display: none;\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\n\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n\n  @if $enable-button-pointers {\n    &:not(:disabled) {\n      cursor: pointer; // 3\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\n\ntextarea {\n  resize: vertical; // 1\n}\n\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n//    So we reset that to ensure fieldsets behave more like a standard block element.\n//    See https://github.com/twbs/bootstrap/issues/12359\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\n\nfieldset {\n  min-width: 0; // 1\n  padding: 0; // 2\n  margin: 0; // 2\n  border: 0; // 2\n}\n\n// 1. By using `float: left`, the legend will behave like a block element.\n//    This way the border of a fieldset wraps around the legend if present.\n// 2. Fix wrapping bug.\n//    See https://github.com/twbs/bootstrap/issues/29712\n\nlegend {\n  float: left; // 1\n  width: 100%;\n  padding: 0;\n  margin-bottom: $legend-margin-bottom;\n  @include font-size($legend-font-size);\n  font-weight: $legend-font-weight;\n  line-height: inherit;\n\n  + * {\n    clear: left; // 2\n  }\n}\n\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\n// See https://github.com/twbs/bootstrap/issues/18842\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n// 1. Correct the outline style in Safari.\n// 2. This overrides the extra rounded corners on search inputs in iOS so that our\n//    `.form-control` class can properly style them. Note that this cannot simply\n//    be added to `.form-control` as it's not specific enough. For details, see\n//    https://github.com/twbs/bootstrap/issues/11586.\n\n[type=\"search\"] {\n  outline-offset: -2px; // 1\n  -webkit-appearance: textfield; // 2\n}\n\n// 1. A few input types should stay LTR\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\n// 2. RTL only output\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n\n// Remove the inner padding in Chrome and Safari on macOS.\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n// Remove padding around color pickers in webkit browsers\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n\n// Inherit font family and line height for file input buttons\n\n::file-selector-button {\n  font: inherit;\n}\n\n// 1. Change font properties to `inherit`\n// 2. Correct the inability to style clickable types in iOS and Safari.\n\n::-webkit-file-upload-button {\n  font: inherit; // 1\n  -webkit-appearance: button; // 2\n}\n\n// Correct element displays\n\noutput {\n  display: inline-block;\n}\n\n// Remove border from iframe\n\niframe {\n  border: 0;\n}\n\n// Summary\n//\n// 1. Add the correct display in all browsers\n\nsummary {\n  display: list-item; // 1\n  cursor: pointer;\n}\n\n\n// Progress\n//\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\n\nprogress {\n  vertical-align: baseline;\n}\n\n\n// Hidden attribute\n//\n// Always hide an element with the `hidden` HTML attribute.\n\n[hidden] {\n  display: none !important;\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n//\n// Bootstrap overrides ===================================\n//\n\n// Color system\n\n$white: #FFFFFF !default;\n$gray-100: #F9FBFD !default;\n$gray-200: #F1F4F8 !default;\n$gray-300: #1B2A4E !default;\n$gray-400: #C6D3E6 !default;\n$gray-500: #ABBCD5 !default;\n$gray-600: #869AB8 !default;\n$gray-700: #094FC1 !default;\n$gray-800: #384C74 !default;\n$gray-900: #1B2A4E !default;\n$black: #161C2D !default;\n\n$primary: #335EEA !default;\n$secondary: $gray-700 !default;\n$primary-desat: #6C8AEC !default; // NEW\n$success: #42BA96 !default;\n$info: #080d18 !default;\n$warning: #FAD776 !default;\n$danger: #DF4759 !default;\n$light: $gray-100 !default;\n$dark: $gray-900 !default;\n\n/* beautify ignore:start */\n$theme-colors: (\n  \"primary\": $primary,\n  \"secondary\": $secondary,\n  \"success\": $success,\n  \"info\": $info,\n  \"warning\": $warning,\n  \"danger\": $danger,\n  \"light\": $light,\n  \"dark\": $dark,\n  \"primary-desat\": $primary-desat, // NEW\n  \"black\": $black, // NEW\n  \"white\": $white // NEW\n);\n/* beautify ignore:end */\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio: 1.75 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$color-contrast-dark: $gray-900 !default;\n$color-contrast-light: $white !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-shadows: true !default;\n$enable-smooth-scroll: false !default;\n$enable-rfs: false !default;\n$enable-validation-icons: false !default;\n$enable-negative-margins: true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n\n/* beautify ignore:start */\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25), // 4px\n    2: ($spacer * .5), // 8px\n    3: ($spacer * .75), // 12px\n    4: $spacer, // 16px\n    5: ($spacer * 1.5), // 24px\n    6: ($spacer * 2), // 32px\n    7: ($spacer * 2.5), // 40px\n    8: ($spacer * 3), // 48px\n    9: ($spacer * 4), // 64px\n    10: ($spacer * 5), // 80px\n    11: ($spacer * 6), // 96px\n    12: ($spacer * 8), // 128px\n    13: ($spacer * 10), // 160px \n    14: ($spacer * 12), // 192px\n    15: ($spacer * 16), // 256px                                                     \n    16: ($spacer * 25), // 400px\n  ),\n  $spacers\n);\n/* beautify ignore:end */\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg: $white !default;\n$body-color: $black !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-decoration: none !default;\n$link-hover-decoration: underline !default;\n\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom: 1rem !default;\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n/* beautify ignore:start */\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1040px\n) !default;\n/* beautify ignore:end */\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-gutter-width: 2.5rem !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$border-width: 1px !default;\n$border-color: $gray-200 !default;\n\n$border-radius-sm: .25rem !default;\n$border-radius: 0.375rem !default;\n$border-radius-lg: 0.5rem !default;\n$border-radius-xl: 1rem !default; // NEW\n$border-radius-pill: 50rem !default;\n\n$box-shadow: 0 0.5rem 1.5rem fade-out($black, .9) !default;\n$box-shadow-sm: 0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg: 0 1.5rem 4rem fade-out($black, .9) !default;\n\n$box-shadow-light: 0 0.5rem 1.5rem fade-out($black, .95) !default;\n$box-shadow-light-lg: 0 1.5rem 4rem fade-out($black, .95) !default;\n\n$box-shadow-dark: 0 0.5rem 1.5rem fade-out($black, .85) !default;\n$box-shadow-dark-lg: 0 1.5rem 4rem fade-out($black, .85) !default;\n\n$box-shadow-lift: 0 1rem 2.5rem fade-out($black, .9), 0 .5rem 1rem -.75rem fade-out($black, .9) !default;\n$box-shadow-lift-lg: 0 2rem 5rem fade-out($black, .9), 0 .5rem 1rem -.75rem fade-out($black, .95) !default;\n\n$component-active-color: $white !default;\n$component-active-bg: $primary !default;\n\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n$font-family-base: 'Poppins', serif !default;\n\n$font-size-base: 1.0625rem !default; // 17px\n$font-size-xs: 0.75rem !default; // 12px NEW\n$font-size-sm: 0.9375rem !default; // 15px\n$font-size-lg: 1.1875rem !default; // 19px\n\n$font-weight-normal: 400 !default;\n$font-weight-bold: 600 !default;\n$font-weight-bolder: 700 !default;\n\n$line-height-base: 1.6 !default;\n$line-height-sm: 1.3 !default;\n$line-height-lg: 1.3 !default;\n\n$h1-font-size: 2.3125rem !default; // 37px\n$h2-font-size: 1.9375rem !default; // 31px\n$h3-font-size: 1.3125rem !default; // 21px\n$h4-font-size: 1.1875rem !default; // 19px\n$h5-font-size: 1.0625rem !default; // 17px\n$h6-font-size: 0.75rem !default; // 12px\n\n/* beautify ignore:start */\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size,\n  sm: $font-size-sm,\n  lg: $font-size-lg\n) !default;\n/* beautify ignore:end */\n\n$headings-margin-bottom: .5rem !default;\n$headings-font-weight: $font-weight-normal !default;\n$headings-line-height: 1 !default;\n\n/* beautify ignore:start */\n$display-font-sizes: (\n  1: 4.375rem, // 70px\n  2: 3.8125rem, // 61px\n  3: 3rem, // 48px\n  4: 2.6875rem // 43px\n) !default;\n/* beautify ignore:end */\n\n$display-font-weight: $font-weight-normal !default;\n\n$lead-font-size: 1.3125rem !default;\n$lead-font-weight: $font-weight-normal !default;\n\n$small-font-size: 88.2% !default;\n\n$text-muted: $gray-600 !default;\n\n$blockquote-font-size: 1.25rem !default;\n$blockquote-footer-color: $gray-600 !default;\n$blockquote-footer-font-size: $font-size-xs !default;\n\n$hr-color: $border-color !default;\n$hr-opacity: 1 !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding-y: 1.5rem !default;\n$table-cell-padding-x: 2rem !default;\n\n$table-bg: $white !default;\n\n$table-striped-bg: $gray-100 !default;\n\n$table-active-bg: $gray-100 !default;\n\n$table-hover-bg: $gray-100 !default;\n\n$table-border-color: $border-color !default;\n\n$table-group-seperator-color: inherit !default;\n\n$table-bg-scale: -90% !default;\n\n/* beautify ignore:start */\n$table-variants: (\n  \"primary\": shift-color($primary, $table-bg-scale),\n  \"secondary\": shift-color($secondary, $table-bg-scale),\n  \"success\": shift-color($success, $table-bg-scale),\n  \"info\": shift-color($info, $table-bg-scale),\n  \"warning\": shift-color($warning, $table-bg-scale),\n  \"danger\": shift-color($danger, $table-bg-scale),\n  \"light\": $light,\n  \"dark\": $gray-800,\n) !default;\n/* beautify ignore:end */\n\n$table-head-bg: $table-bg !default; // NEW\n$table-head-color: $body-color !default; // NEW\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y: .8125rem !default;\n$input-btn-padding-x: 1.25rem !default;\n$input-btn-font-size: $font-size-base !default;\n$input-btn-line-height: $line-height-base !default;\n\n$input-btn-focus-width: 0 !default;\n$input-btn-focus-color: transparent !default;\n$input-btn-focus-box-shadow: none !default;\n\n$input-btn-padding-y-sm: .5625rem !default;\n$input-btn-padding-x-sm: 1rem !default;\n$input-btn-font-size-sm: $font-size-base !default;\n$input-btn-line-height-sm: $line-height-base !default; // NEW\n\n$input-btn-padding-y-lg: 1.122rem !default;\n$input-btn-padding-x-lg: 1.5rem !default;\n$input-btn-font-size-lg: $font-size-base !default;\n$input-btn-line-height-lg: $line-height-base !default; // NEW\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-font-weight: $font-weight-bold !default;\n$btn-box-shadow: none !default;\n$btn-active-box-shadow: none !default;\n\n$btn-border-radius-sm: $border-radius !default;\n$btn-border-radius-lg: $border-radius !default;\n\n\n// Forms\n\n$input-bg: $white !default;\n\n$input-color: $body-color !default;\n$input-border-color: $border-color !default;\n$input-border-width: $border-width !default;\n$input-box-shadow: none !default;\n\n$input-border-radius-sm: $border-radius !default;\n$input-border-radius-lg: $border-radius !default;\n\n$input-focus-border-color: $primary !default;\n$input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color: $text-muted !default;\n\n$form-check-input-active-filter: none !default;\n\n$form-check-padding-start: 1.5rem !default;\n$form-check-label-cursor: pointer !default;\n\n$form-check-input-bg: $gray-300 !default;\n$form-check-input-border: transparent !default;\n$form-check-input-border-radius: $border-radius !default;\n$form-check-input-focus-border: $form-check-input-border !default;\n$form-check-input-focus-box-shadow: none !default;\n\n$form-check-input-checked-color: $component-active-color !default;\n$form-check-input-checked-bg-color: $component-active-bg !default;\n$form-check-input-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color: $component-active-color !default;\n\n$form-switch-color: $white !default;\n$form-switch-width: 2.5rem !default;\n$form-switch-padding-start: $form-switch-width + .5rem !default;\n$form-switch-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n\n$form-switch-focus-color: $form-switch-color !default;\n$form-switch-focus-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color: $form-switch-color !default;\n$form-switch-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n\n$input-group-addon-color: $input-placeholder-color;\n$input-group-addon-bg: $input-bg !default;\n\n$form-select-indicator-padding: 1.25rem !default;\n$form-select-bg-size: 1em 1em !default;\n$form-select-indicator-color: $gray-400 !default;\n$form-select-indicator: url(\"data:image/svg+xml,<svg viewBox='0 0 9 5' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1 1L4.25 4.25L7.5 1' stroke='#{$form-select-indicator-color}' stroke-width='1.08333' stroke-linecap='round' stroke-linejoin='round'/></svg>\") !default;\n\n$form-select-box-shadow: $input-box-shadow !default;\n\n$form-select-focus-border-color: $input-focus-border-color !default;\n$form-select-focus-box-shadow: $input-focus-box-shadow !default;\n\n$form-file-button-bg: $light !default;\n\n$form-floating-label-opacity: 1 !default;\n$form-floating-label-transform: translateY(-.5rem) !default;\n\n$form-group-margin-bottom: 1rem !default; // NEW\n\n\n// Navbar\n\n$navbar-padding-y: 1.25rem !default;\n$navbar-padding-x: 0 !default;\n\n$navbar-nav-link-padding-x: 1.5rem !default;\n\n$navbar-brand-font-size: 1.5rem !default;\n$navbar-brand-margin-end: 0 !default;\n\n$navbar-dark-color: $white !default;\n$navbar-dark-hover-color: $white !default;\n$navbar-dark-active-color: $white !default;\n$navbar-dark-toggler-border-color: transparent !default;\n\n$navbar-light-color: $gray-700 !default;\n$navbar-light-hover-color: $primary !default;\n$navbar-light-active-color: $primary !default;\n$navbar-light-toggler-border-color: transparent !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width: 14rem !default;\n$dropdown-padding-y: 1.625rem !default;\n$dropdown-spacer: 0 !default;\n$dropdown-font-size: $font-size-sm !default;\n$dropdown-border-width: 0px !default;\n$dropdown-box-shadow: $box-shadow !default;\n\n$dropdown-link-color: $gray-700 !default;\n$dropdown-link-hover-color: $primary !default;\n$dropdown-link-hover-bg: none !default;\n\n$dropdown-link-active-color: $primary !default;\n$dropdown-link-active-bg: none !default;\n\n$dropdown-item-padding-y: 0 !default;\n$dropdown-item-padding-x: 1.75rem !default;\n\n$dropdown-header-color: $primary !default;\n\n\n// Pagination\n\n$pagination-padding-y: $input-btn-padding-y !default;\n$pagination-padding-x: $input-btn-padding-x !default;\n$pagination-padding-y-sm: $input-btn-padding-y-sm !default;\n$pagination-padding-x-sm: $input-btn-padding-x-sm !default;\n$pagination-padding-y-lg: $input-btn-padding-y-lg !default;\n$pagination-padding-x-lg: $input-btn-padding-x-lg !default;\n\n$pagination-line-height: $line-height-base !default; // NEW\n\n\n// Cards\n\n$card-spacer-y: 2rem !default;\n$card-spacer-x: 2rem !default;\n$card-title-spacer-y: 1.5rem !default;\n$card-border-width: 0px !default;\n$card-border-color: $border-color !default;\n$card-cap-padding-y: 1.5rem !default;\n\n\n// Accordion\n\n$accordion-padding-y: 1.5rem !default;\n$accordion-padding-x: 2rem !default;\n$accordion-border-color: $border-color !default;\n\n$accordion-button-active-bg: $body-bg !default;\n$accordion-button-active-color: $body-color !default;\n$accordion-button-focus-box-shadow: none !default;\n\n$accordion-icon-width: .85rem !default;\n$accordion-icon-color: $text-muted !default;\n$accordion-icon-active-color: $accordion-icon-color !default;\n\n\n// Popovers\n\n$popover-max-width: 380px !default;\n$popover-border-color: transparent !default;\n$popover-border-radius: $border-radius !default;\n$popover-box-shadow: $box-shadow-lg !default;\n\n$popover-header-bg: transparent !default;\n$popover-header-padding-y: 1.5rem !default;\n$popover-header-padding-x: 2rem !default;\n\n$popover-body-color: $text-muted !default;\n$popover-body-padding-y: 1.5rem !default;\n$popover-body-padding-x: 2rem !default;\n\n$popover-arrow-width: 1.25rem !default;\n$popover-arrow-height: 0.625rem !default;\n\n\n// Toasts\n\n$toast-padding-y: .5rem !default;\n\n\n// Badges\n\n$badge-padding-y: .25rem !default;\n$badge-padding-x: .75rem !default;\n\n$border-radius-pill-padding-x: $badge-padding-x !default; // NEW\n\n\n// Modals\n\n$modal-inner-padding: 2.5rem !default;\n\n$modal-backdrop-opacity: .8 !default;\n\n\n// Alerts\n\n$alert-padding-y: .75rem !default;\n$alert-padding-x: 1.25rem !default;\n\n$alert-bg-scale: 0% !default;\n$alert-border-scale: 0% !default;\n$alert-color-scale: 60% !default;\n\n\n// List group\n\n$list-group-border-color: $border-color !default;\n$list-group-item-padding-y: 1.5rem !default;\n$list-group-item-padding-x: 2rem !default;\n\n\n// Figures\n\n$figure-caption-font-size: $small-font-size !default;\n$figure-caption-color: $text-muted !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y: .75rem !default;\n$breadcrumb-padding-x: 0 !default;\n$breadcrumb-margin-bottom: 0 !default;\n$breadcrumb-bg: transparent !default;\n$breadcrumb-divider-color: $gray-700 !default;\n$breadcrumb-active-color: $gray-700 !default;\n$breadcrumb-border-radius: 0 !default;\n\n\n// Close\n\n$btn-close-width: .5em !default;\n$btn-close-color: $text-muted !default;\n$btn-close-text-shadow: none !default;\n\n\n// Code\n\n$code-font-size: 0.8125rem !default;\n\n\n//\n// Theme ===================================\n//\n\n// Paths\n\n$path-to-img: \"../img\" !default;\n$path-to-fonts: \"../fonts\" !default;\n\n\n// Typography\n\n$headings-letter-spacing: -.01em !default;\n\n$h1-line-height: 1.3 !default;\n$h2-line-height: 1.35 !default;\n$h3-line-height: 1.4 !default;\n$h4-line-height: 1.45 !default;\n$h5-line-height: 1.5 !default;\n$h6-line-height: 1.55 !default;\n\n$display-line-height: 1.2 !default;\n$display-letter-spacing: -.02em !default;\n\n$h1-font-size-md: 1.875rem !default; // 30px\n$h2-font-size-md: 1.5rem !default; // 24px\n$h3-font-size-md: 1.125rem !default; // 18px\n$h4-font-size-md: 1.0625rem !default; // 17px\n\n$display1-font-size-md: 3.125rem !default; // 48px\n$display2-font-size-md: 2.5rem !default; // 38px\n$display3-font-size-md: 2.25rem !default; // 36px\n$display4-font-size-md: 2rem !default; // 32px\n\n$font-size-lg-md: 1.0625rem !default; // 17px\n$lead-font-size-md: 1.1875rem !default; // 19px\n\n$blockquote-small-font-weight: $font-weight-bold !default;\n$blockquote-small-letter-spacing: .07em !default;\n\n$letter-spacing-lg: .08em !default;\n\n\n// Buttons + Forms\n\n$input-btn-padding-y-xs: .25rem !default;\n$input-btn-padding-x-xs: .625rem !default;\n$input-btn-font-size-xs: $font-size-sm !default;\n$input-btn-line-height-xs: $line-height-base !default;\n\n\n// Buttons\n\n$btn-soft-bg-opacity: .1 !default;\n\n$btn-line-height-sm: $input-btn-line-height-sm !default;\n$btn-line-height-lg: $input-btn-line-height-lg !default;\n\n$btn-padding-y-xs: $input-btn-padding-y-xs !default;\n$btn-padding-x-xs: $input-btn-padding-x-xs !default;\n\n\n// Forms\n\n$input-line-height-sm: $input-btn-line-height-sm !default;\n$input-line-height-lg: $input-btn-line-height-lg !default;\n\n$input-padding-y-xs: $input-btn-padding-y-xs !default;\n$input-padding-x-xs: $input-btn-padding-x-xs !default;\n$input-font-size-xs: $input-btn-font-size-xs !default;\n$input-line-height-xs: $input-btn-line-height-xs !default;\n$input-height-xs: calc(#{$input-btn-padding-y-xs * 2} + #{$input-btn-font-size-xs} * #{$input-line-height-xs} + #{$input-border-width * 2});\n\n$form-check-input-bg-dark: rgba($white, .2) !default;\n$form-check-input-checked-bg-color-dark: $white !default;\n\n$form-switch-min-height: 1.375rem !default;\n\n$form-switch-color-dark: $white !default;\n$form-switch-focus-color-dark: $white !default;\n$form-switch-checked-color-dark: $primary !default;\n\n$form-switch-bg-image-dark: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color-dark}'/></svg>\") !default;\n$form-switch-focus-bg-image-dark: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color-dark}'/></svg>\") !default;\n$form-switch-checked-bg-image-dark: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color-dark}'/></svg>\") !default;\n\n$form-select-padding-y-xs: $input-padding-y-xs !default;\n$form-select-padding-x-xs: $input-padding-x-xs !default;\n$form-select-font-size-xs: $input-font-size-xs !default;\n$form-select-line-height-xs: $input-line-height-xs !default;\n$form-select-height-xs: $input-height-xs !default;\n$form-select-indicator-padding-xs: .625rem !default;\n$form-select-bg-size-xs: .75em !default;\n\n$form-floating-label-font-size: $font-size-xs !default;\n$form-floating-label-color: $input-placeholder-color !default;\n\n\n// Navbar\n\n$navbar-brand-font-weight: $font-weight-bold !default;\n\n$navbar-nav-link-font-weight: $font-weight-bold !default;\n\n$navbar-nav-item-spacing: 1.5rem !default;\n\n$navbar-dropdown-toggle-margin-bottom: 1.5rem !default;\n\n\n// Dropdowns\n\n$dropdown-item-padding-x-lg: 2.5rem !default;\n$dropdown-item-spacing-y: .45rem !default;\n\n$dropdown-padding-x: $dropdown-item-padding-x !default;\n$dropdown-padding-x-xs: 1.25rem !default;\n$dropdown-padding-y-xs: 1rem !default;\n$dropdown-padding-x-lg: $dropdown-item-padding-x-lg !default;\n$dropdown-padding-y-lg: 2.375rem !default;\n\n$dropdown-min-width-xs: 0 !default;\n$dropdown-min-width-md: 22rem !default;\n$dropdown-min-width-lg: 35rem !default;\n$dropdown-min-width-xl: 42rem !default;\n\n\n// Card\n\n$card-meta-spacer-y: 1.5rem !default;\n\n$card-row-spacer-y: 4rem !default;\n$card-row-spacer-x: 2.5rem !default;\n\n\n// Popover\n\n$popover-header-font-size: $h6-font-size !default;\n$popover-header-color: $primary !default;\n\n\n// Badges\n\n$badge-padding-y-lg: .7em !default;\n$badge-padding-x-lg: 1.15em !default;\n\n$badge-soft-bg-opacity: .1 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size: $font-size-sm !default;\n\n\n// Code\n\n$code-line-height: 1.35 !default;\n\n\n// Avatar\n\n$avatar-size-base: 1.875rem !default;\n$avatar-size-xs: 1rem !default;\n$avatar-size-sm: 1.5rem !default;\n$avatar-size-lg: 2.25rem !default;\n$avatar-size-xl: 4rem !default;\n$avatar-size-xxl: 5rem !default;\n\n$avatar-title-bg: $gray-500 !default;\n$avatar-title-color: $white !default;\n\n\n// Icons\n\n$icon-size-base: 3rem !default;\n$icon-size-xs: 1.5rem !default;\n$icon-size-sm: 2.25rem !default;\n$icon-size-lg: 4rem !default;\n$icon-size-xl: 5rem !default;\n\n$icon-circle-size: 4.1875rem !default;\n$icon-circle-font-size: 1.875rem !default;\n\n\n// Images\n\n$img-skewed-rotate-y: 35deg !default;\n$img-skewed-rotate-x: 15deg !default;\n\n\n// List\n\n$list-item-spacing-y: .35rem;\n\n$list-link-font-size: $font-size-sm !default;\n$list-link-color: $gray-700 !default;\n$list-link-hover-color: $primary !default;\n\n\n// Screenshot\n\n$screenshot-border-radius: 0.625rem !default;\n$screenshot-box-shadow: 25px 60px 125px -25px fade-out($gray-700, .9), 16px 40px 75px -40px fade-out(black, .8) !default;\n\n\n\n// \n// Utilities =====================================\n//\n\n/* beautify ignore:start */\n$utilities: (\n  \"position\": (\n    responsive: true,\n    property: position,\n    values: static relative absolute fixed sticky\n  ),\n  \"top\": (\n    property: top,\n    values: (\n      0: 0,\n    )\n  ),\n  \"right\": (\n    property: right,\n    values: (\n      0: 0,\n    )\n  ),\n  \"bottom\": (\n    property: bottom,\n    values: (\n      0: 0,\n    )\n  ),\n  \"left\": (\n    property: left,\n    values: (\n      0: 0,\n    )\n  ),\n  \"width\": (\n    responsive: true,\n    property: width,\n    class: w,\n    values: (\n      25: 25%,\n      50: 50%,\n      75: 75%,\n      100: 100%,\n      110: 110%,\n      120: 120%,\n      130: 130%,\n      140: 140%,\n      150: 150%,\n      auto: auto\n    )\n  ),\n  \"max-width\": (\n    responsive: true,\n    property: max-width,\n    class: mw,\n    values: (\n      25: 25%,\n      50: 50%,\n      75: 75%,\n      100: 100%,\n      110: 110%,\n      120: 120%,\n      130: 130%,\n      140: 140%,\n      150: 150%\n    )\n  ),\n  \"viewport-width\": (\n    responsive: true,\n    property: width,\n    class: vw,\n    values: (\n      25: 25vw,\n      50: 50vw,\n      75: 75vw,\n      100: 100vw,\n      110: 110vw,\n      120: 120vw,\n      130: 130vw,\n      140: 140vw,\n      150: 150vw,\n    )\n  ),\n  \"height\": (\n    responsive: true,\n    property: height,\n    class: h,\n    values: (\n      25: 25%,\n      50: 50%,\n      75: 75%,\n      100: 100%,\n      110: 110%,\n      120: 120%,\n      130: 130%,\n      140: 140%,\n      150: 150%,\n      auto: auto\n    )\n  ),\n  \"viewport-height\": (\n    responsive: true,\n    property: height,\n    class: vh,\n    values: (\n      25: 25vh,\n      50: 50vh,\n      75: 75vh,\n      100: 100vh,\n      110: 110vh,\n      120: 120vh,\n      130: 130vh,\n      140: 140vh,\n      150: 150vh,\n    )\n  ),\n  \"background-color\": (\n    property: background-color,\n    class: bg,\n    values: map-merge(\n      $theme-colors,\n      (\n        \"white-20\": rgba($white, .2),\n        \"gray-200\": $gray-200,\n        \"gray-300\": $gray-300,\n        \"gray-800\": $gray-800,\n        \"gray-900\": $gray-900,\n        \"gray-900-50\": rgba($gray-900, .5)\n      )\n    )\n  ),\n  \"rounded\": (\n    property: border-radius,\n    class: rounded,\n    values: (\n      null: $border-radius,\n      0: 0,\n      1: $border-radius-sm,\n      2: $border-radius,\n      3: $border-radius-lg,\n      4: $border-radius-xl,\n      circle: 50%,\n      pill: $border-radius-pill\n    )\n  ),\n  \"border\": (\n    responsive: true,\n    property: border,\n    values: (\n      null: $border-width solid $border-color,\n      0: 0,\n    )\n  ),\n  \"border-top\": (\n    responsive: true,\n    property: border-top,\n    values: (\n      null: $border-width solid $border-color,\n      0: 0,\n    )\n  ),\n  \"border-end\": (\n    responsive: true,\n    property: border-right,\n    values: (\n      null: $border-width solid $border-color,\n      0: 0,\n    )\n  ),\n  \"border-bottom\": (\n    responsive: true,\n    property: border-bottom,\n    values: (\n      null: $border-width solid $border-color,\n      0: 0,\n    )\n  ),\n  \"border-start\": (\n    responsive: true,\n    property: border-left,\n    values: (\n      null: $border-width solid $border-color,\n      0: 0,\n    )\n  ),\n  \"border-color\": (\n    property: border-color,\n    class: border,\n    values: map-merge(\n      $theme-colors,\n      (\n        \"white-10\": rgba($white, .1),\n        \"white-20\": rgba($white, .2),\n        \"gray-300\": $gray-300,\n        \"gray-800\": $gray-800,\n        \"gray-800-50\": rgba($gray-800, .5),\n        \"gray-900-50\": rgba($gray-900, .5)\n      )\n    )\n  ),\n  \"shadow\": (\n    property: box-shadow,\n    class: shadow,\n    values: (\n      null: $box-shadow,\n      \"sm\": $box-shadow-sm,\n      \"lg\": $box-shadow-lg,\n      \"light\": $box-shadow-light,\n      \"dark\": $box-shadow-dark,\n      \"light-lg\": $box-shadow-light-lg,\n      \"dark-lg\": $box-shadow-dark-lg,\n      \"lift\": $box-shadow-lift,\n      \"none\": none\n    )\n  ),\n  \"color\": (\n    property: color,\n    class: text,\n    values: map-merge(\n      $theme-colors,\n      (\n        \"gray-100\": $gray-100,\n        \"gray-200\": $gray-200,\n        \"gray-300\": $gray-300,\n        \"gray-400\": $gray-400,\n        \"gray-500\": $gray-500,\n        \"gray-600\": $gray-600,\n        \"gray-700\": $gray-700,\n        \"gray-800\": $gray-800,\n        \"gray-900\": $gray-900,\n        \"white\": $white,\n        \"body\": $body-color,\n        \"muted\": $text-muted,\n        \"muted-80\": rgba($text-muted, .8),\n        \"black-50\": rgba($black, .5),\n        \"white-50\": rgba($white, .5),\n        \"white-70\": rgba($white, .7),\n        \"white-75\": rgba($white, .75),\n        \"white-80\": rgba($white, .8),\n        \"reset\": inherit,\n      )\n    )\n  ),\n  \"letter-spacing\": (\n    property: letter-spacing,\n    values: (\n      lg: $letter-spacing-lg\n    )\n  ),\n  \"opacity\": (\n    property: opacity,\n    values: (\n      0: 0,\n      1: 1\n    )\n  ),\n);\n/* beautify ignore:end */\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/master/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: $rfs-base-value / ($rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: $rfs-base-value / ($rfs-base-value * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + ' 0';\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + ' ' + if($rfs-unit == rem, #{$value / ($value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + ' ' + if($rfs-unit == px, #{$value / ($value * 0 + 1) * $rfs-rem-value}px, $value);\n      }\n      @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + ' ' + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + ' 0';\n    }\n\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + ' ' + $value;\n      }\n\n      @else {\n        // Remove unit from $value for calculations\n        $value: $value / ($value * 0 + if($unit == px, 1, 1 / $rfs-rem-value));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + ' ' +  if($rfs-unit == rem, #{$value / $rfs-rem-value}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + (abs($value) - $rfs-base-value) / $rfs-factor;\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{$value-min / $rfs-rem-value}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{$value-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + ' calc(' + $min-width + if($value < 0, ' - ', ' + ') + $variable-width + ')';\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluidVal: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluidVal {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluidVal);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule {\n        #{$property}: if($rfs-mode == max-media-query, $fluidVal, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #CC0383 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer / 4,\n  2: $spacer / 2,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n$body-text-align:           null !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n$gutters: $spacers !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width / 2 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n\n$border-color:                $gray-300 !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .25rem !default;\n$border-radius-sm:            .2rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", \"Liberation Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$variable-prefix}font-monospace) !default;\n\n// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base effects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n$text-muted:                  $gray-600 !default;\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n$hr-height:                   $border-width !default;\n$hr-opacity:                  .25 !default;\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor:         .1 !default;\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-striped-order:         odd !default;\n\n$table-group-separator-color: currentColor !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur:          0 !default;\n$input-btn-focus-box-shadow:    0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              $link-color !default;\n$btn-link-hover-color:        $link-hover-color !default;\n$btn-link-disabled-color:     $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           $body-color !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      $box-shadow-inset !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 1px solid rgba($black, .25) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba(0, 0, 0, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $border-radius !default;\n$form-select-box-shadow:          $box-shadow-inset !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             $gray-300 !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     $box-shadow-inset !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             $gray-500 !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-group-addon-bg !default;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:            add(3.5rem, $input-height-border) !default;\n$form-floating-padding-x:         $input-padding-x !default;\n$form-floating-padding-y:         1rem !default;\n$form-floating-input-padding-t:   1.625rem !default;\n$form-floating-input-padding-b:   .625rem !default;\n$form-floating-label-opacity:     .65 !default;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-offcanvas:                  1050 !default;\n$zindex-modal:                      1060 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    $link-color !default;\n$nav-link-hover-color:              $link-hover-color !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .55) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer / 2 !default;\n$dropdown-box-shadow:               $box-shadow !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         shade-color($gray-900, 10%) !default;\n$dropdown-link-hover-bg:            $gray-200 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-500 !default;\n\n$dropdown-item-padding-y:           $spacer / 4 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-radius:          $border-radius !default;\n$pagination-margin-start:           -$pagination-border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-color:            $link-hover-color !default;\n$pagination-focus-bg:               $gray-200 !default;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       $border-radius-sm !default;\n$pagination-border-radius-lg:       $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer / 2 !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y / 2 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         $body-color !default;\n$accordion-bg:                            $body-bg !default;\n$accordion-border-width:                  $border-width !default;\n$accordion-border-color:                  rgba($black, .125) !default;\n$accordion-border-radius:                 $border-radius !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  $accordion-color !default;\n$accordion-button-bg:                     $accordion-bg !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color:           shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $accordion-color !default;\n$accordion-icon-active-color:             $accordion-button-active-color !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(-180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer / 4 !default;\n$tooltip-padding-x:                 $spacer / 2 !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-start tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                $box-shadow !default;\n\n$popover-header-bg:                 shade-color($popover-bg, 6%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               $border-radius !default;\n$toast-box-shadow:                  $box-shadow !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           $border-radius !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            $border-width !default;\n$alert-bg-scale:                -80% !default;\n$alert-border-scale:            -70% !default;\n$alert-color-scale:             40% !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               $box-shadow-inset !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  $gray-900 !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         $spacer / 2 !default;\n$list-group-item-padding-x:         $spacer !default;\n$list-group-item-bg-scale:          -80% !default;\n$list-group-item-color-scale:       40% !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              $box-shadow-sm !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              $gray-600 !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                $modal-content-bg !default;\n$offcanvas-color:                   $modal-content-color !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         null !default;\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  @if length($map) > 0 {\n    $values: map-values($map);\n    $first-value: nth($values, 1);\n    @if $first-value != 0 {\n      @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n    }\n  }\n}\n\n// Internal Bootstrap function to turn maps into its negative variant.\n// It prefixes the keys with `n` and makes the value negative.\n@function negativify-map($map) {\n  $result: ();\n  @each $key, $value in $map {\n    @if $key != 0 {\n      $result: map-merge($result, (\"n\" + $key: (-$value)));\n    }\n  }\n  @return $result;\n}\n\n// Get multiple keys from a sass map\n@function map-get-multiple($map, $values) {\n  $result: ();\n  @each $key, $value in $map {\n    @if (index($values, $key) != null) {\n      $result: map-merge($result, ($key: $value));\n    }\n  }\n  @return $result;\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n//\n// Requires the use of quotes around data URIs.\n\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      // Do not escape the url brackets\n      @if str-index($string, \"url(\") == 1 {\n        $string: url(\"#{str-replace(str-slice($string, 6, -3), $char, $encoded)}\");\n      } @else {\n        $string: str-replace($string, $char, $encoded);\n      }\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n// See https://github.com/twbs/bootstrap/pull/30168\n\n// A list of pre-calculated numbers of pow(($value / 255 + .055) / 1.055, 2.4). (from 0 to 255)\n// stylelint-disable-next-line scss/dollar-variable-default, scss/dollar-variable-pattern\n$_luminance-list: .0008 .001 .0011 .0013 .0015 .0017 .002 .0022 .0025 .0027 .003 .0033 .0037 .004 .0044 .0048 .0052 .0056 .006 .0065 .007 .0075 .008 .0086 .0091 .0097 .0103 .011 .0116 .0123 .013 .0137 .0144 .0152 .016 .0168 .0176 .0185 .0194 .0203 .0212 .0222 .0232 .0242 .0252 .0262 .0273 .0284 .0296 .0307 .0319 .0331 .0343 .0356 .0369 .0382 .0395 .0409 .0423 .0437 .0452 .0467 .0482 .0497 .0513 .0529 .0545 .0561 .0578 .0595 .0612 .063 .0648 .0666 .0685 .0704 .0723 .0742 .0762 .0782 .0802 .0823 .0844 .0865 .0887 .0908 .0931 .0953 .0976 .0999 .1022 .1046 .107 .1095 .1119 .1144 .117 .1195 .1221 .1248 .1274 .1301 .1329 .1356 .1384 .1413 .1441 .147 .15 .1529 .1559 .159 .162 .1651 .1683 .1714 .1746 .1779 .1812 .1845 .1878 .1912 .1946 .1981 .2016 .2051 .2086 .2122 .2159 .2195 .2232 .227 .2307 .2346 .2384 .2423 .2462 .2502 .2542 .2582 .2623 .2664 .2705 .2747 .2789 .2831 .2874 .2918 .2961 .3005 .305 .3095 .314 .3185 .3231 .3278 .3325 .3372 .3419 .3467 .3515 .3564 .3613 .3663 .3712 .3763 .3813 .3864 .3916 .3968 .402 .4072 .4125 .4179 .4233 .4287 .4342 .4397 .4452 .4508 .4564 .4621 .4678 .4735 .4793 .4851 .491 .4969 .5029 .5089 .5149 .521 .5271 .5333 .5395 .5457 .552 .5583 .5647 .5711 .5776 .5841 .5906 .5972 .6038 .6105 .6172 .624 .6308 .6376 .6445 .6514 .6584 .6654 .6724 .6795 .6867 .6939 .7011 .7084 .7157 .7231 .7305 .7379 .7454 .7529 .7605 .7682 .7758 .7835 .7913 .7991 .807 .8148 .8228 .8308 .8388 .8469 .855 .8632 .8714 .8796 .8879 .8963 .9047 .9131 .9216 .9301 .9387 .9473 .956 .9647 .9734 .9823 .9911 1;\n\n@function color-contrast($background, $color-contrast-dark: $color-contrast-dark, $color-contrast-light: $color-contrast-light, $min-contrast-ratio: $min-contrast-ratio) {\n  $foregrounds: $color-contrast-light, $color-contrast-dark, $white, $black;\n  $max-ratio: 0;\n  $max-ratio-color: null;\n\n  @each $color in $foregrounds {\n    $contrast-ratio: contrast-ratio($background, $color);\n    @if $contrast-ratio > $min-contrast-ratio {\n      @return $color;\n    } @else if $contrast-ratio > $max-ratio {\n      $max-ratio: $contrast-ratio;\n      $max-ratio-color: $color;\n    }\n  }\n\n  @warn \"Found no color leading to #{$min-contrast-ratio}:1 contrast ratio against #{$background}...\";\n\n  @return $max-ratio-color;\n}\n\n@function contrast-ratio($background, $foreground: $color-contrast-light) {\n  $l1: luminance($background);\n  $l2: luminance(opaque($background, $foreground));\n\n  @return if($l1 > $l2, ($l1 + .05) / ($l2 + .05), ($l2 + .05) / ($l1 + .05));\n}\n\n// Return WCAG2.0 relative luminance\n// See https://www.w3.org/WAI/GL/wiki/Relative_luminance\n// See https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n@function luminance($color) {\n  $rgb: (\n    \"r\": red($color),\n    \"g\": green($color),\n    \"b\": blue($color)\n  );\n\n  @each $name, $value in $rgb {\n    $value: if($value / 255 < .03928, $value / 255 / 12.92, nth($_luminance-list, $value + 1));\n    $rgb: map-merge($rgb, ($name: $value));\n  }\n\n  @return (map-get($rgb, \"r\") * .2126) + (map-get($rgb, \"g\") * .7152) + (map-get($rgb, \"b\") * .0722);\n}\n\n// Return opaque color\n// opaque(#fff, rgba(0, 0, 0, .5)) => #808080\n@function opaque($background, $foreground) {\n  @return mix(rgba($foreground, 1), $background, opacity($foreground) * 100);\n}\n\n// scss-docs-start color-functions\n// Tint a color: mix a color with white\n@function tint-color($color, $weight) {\n  @return mix(white, $color, $weight);\n}\n\n// Shade a color: mix a color with black\n@function shade-color($color, $weight) {\n  @return mix(black, $color, $weight);\n}\n\n// Shade the color if the weight is positive, else tint it\n@function shift-color($color, $weight) {\n  @return if($weight > 0, shade-color($color, $weight), tint-color($color, -$weight));\n}\n// scss-docs-end color-functions\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n// scss-docs-start border-radius-mixins\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n// scss-docs-end border-radius-mixins\n", "//\n// Headings\n//\n.h1 {\n  @extend h1;\n}\n\n.h2 {\n  @extend h2;\n}\n\n.h3 {\n  @extend h3;\n}\n\n.h4 {\n  @extend h4;\n}\n\n.h5 {\n  @extend h5;\n}\n\n.h6 {\n  @extend h6;\n}\n\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n@each $display, $font-size in $display-font-sizes {\n  .display-#{$display} {\n    @include font-size($font-size);\n    font-weight: $display-font-weight;\n    line-height: $display-line-height;\n  }\n}\n\n//\n// Emphasis\n//\n.small {\n  @extend small;\n}\n\n.mark {\n  @extend mark;\n}\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size($initialism-font-size);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-font-size);\n\n  > :last-child {\n    margin-bottom: 0;\n  }\n}\n\n.blockquote-footer {\n  margin-top: -$blockquote-margin-y;\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-footer-font-size);\n  color: $blockquote-footer-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer / 2;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @each $value in $shadow {\n      @if $value != null {\n        $result: append($result, $value, \"comma\");\n      }\n      @if $value == none and length($shadow) > 1 {\n        @warn \"The keyword 'none' must be used as a single argument.\";\n      }\n    }\n\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  width: 100%;\n  padding-right: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  padding-left: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  margin-right: auto;\n  margin-left: auto;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    > * {\n      @include make-col-ready();\n    }\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$variable-prefix}gutter-x: #{$gutter};\n  --#{$variable-prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: calc(var(--#{$variable-prefix}gutter-y) * -1); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(var(--#{$variable-prefix}gutter-x) / -2); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(var(--#{$variable-prefix}gutter-x) / -2); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$variable-prefix}gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$variable-prefix}gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$variable-prefix}gutter-y);\n}\n\n@mixin make-col($size: false, $columns: $grid-columns) {\n  @if $size {\n    flex: 0 0 auto;\n    width: percentage($size / $columns);\n  } @else {\n    flex: 1 1 0;\n    max-width: 100%;\n  }\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: 100% / $count;\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  --#{$variable-prefix}table-bg: #{$table-bg};\n  --#{$variable-prefix}table-accent-bg: #{$table-bg};\n  --#{$variable-prefix}table-striped-color: #{$table-striped-color};\n  --#{$variable-prefix}table-striped-bg: #{$table-striped-bg};\n  --#{$variable-prefix}table-active-color: #{$table-active-color};\n  --#{$variable-prefix}table-active-bg: #{$table-active-bg};\n  --#{$variable-prefix}table-hover-color: #{$table-hover-color};\n  --#{$variable-prefix}table-hover-bg: #{$table-hover-bg};\n\n  width: 100%;\n  margin-bottom: $spacer;\n  color: $table-color;\n  vertical-align: $table-cell-vertical-align;\n  border-color: $table-border-color;\n\n  // Target th & td\n  // We need the child combinator to prevent styles leaking to nested tables which doesn't have a `.table` class.\n  // We use the universal selectors here to simplify the selector (else we would need 6 different selectors).\n  // Another advantage is that this generates less code and makes the selector less specific making it easier to override.\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y $table-cell-padding-x;\n    background-color: var(--#{$variable-prefix}table-bg);\n    border-bottom-width: $table-border-width;\n    box-shadow: inset 0 0 0 9999px var(--#{$variable-prefix}table-accent-bg);\n  }\n\n  > tbody {\n    vertical-align: inherit;\n  }\n\n  > thead {\n    vertical-align: bottom;\n  }\n\n  // Highlight border color between thead, tbody and tfoot.\n  > :not(:last-child) > :last-child > * {\n    border-bottom-color: $table-group-separator-color;\n  }\n}\n\n\n//\n// Change placement of captions with a class\n//\n\n.caption-top {\n  caption-side: top;\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y-sm $table-cell-padding-x-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n//\n// When borders are added on all sides of the cells, the corners can render odd when\n// these borders do not have the same color or if they are semi-transparent.\n// Therefor we add top and border bottoms to the `tr`s and left and right borders\n// to the `td`s or `th`s\n\n.table-bordered {\n  > :not(caption) > * {\n    border-width: $table-border-width 0;\n\n    // stylelint-disable-next-line selector-max-universal\n    > * {\n      border-width: 0 $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    border-bottom-width: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n.table-striped {\n  > tbody > tr:nth-of-type(#{$table-striped-order}) {\n    --#{$variable-prefix}table-accent-bg: var(--#{$variable-prefix}table-striped-bg);\n    color: var(--#{$variable-prefix}table-striped-color);\n  }\n}\n\n// Active table\n//\n// The `.table-active` class can be added to highlight rows or cells\n\n.table-active {\n  --#{$variable-prefix}table-accent-bg: var(--#{$variable-prefix}table-active-bg);\n  color: var(--#{$variable-prefix}table-active-color);\n}\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  > tbody > tr:hover {\n    --#{$variable-prefix}table-accent-bg: var(--#{$variable-prefix}table-hover-bg);\n    color: var(--#{$variable-prefix}table-hover-color);\n  }\n}\n\n\n// Table variants\n//\n// Table variants set the table cell backgrounds, border colors\n// and the colors of the striped, hovered & active tables\n\n@each $color, $value in $table-variants {\n  @include table-variant($color, $value);\n}\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n  @include media-breakpoint-down($breakpoint) {\n    .table-responsive#{$infix} {\n      overflow-x: auto;\n      -webkit-overflow-scrolling: touch;\n    }\n  }\n}\n", "// scss-docs-start table-variant\n@mixin table-variant($state, $background) {\n  .table-#{$state} {\n    $color: color-contrast(opaque($body-bg, $background));\n    $hover-bg: mix($color, $background, percentage($table-hover-bg-factor));\n    $striped-bg: mix($color, $background, percentage($table-striped-bg-factor));\n    $active-bg: mix($color, $background, percentage($table-active-bg-factor));\n\n    --#{$variable-prefix}table-bg: #{$background};\n    --#{$variable-prefix}table-striped-bg: #{$striped-bg};\n    --#{$variable-prefix}table-striped-color: #{color-contrast($striped-bg)};\n    --#{$variable-prefix}table-active-bg: #{$active-bg};\n    --#{$variable-prefix}table-active-color: #{color-contrast($active-bg)};\n    --#{$variable-prefix}table-hover-bg: #{$hover-bg};\n    --#{$variable-prefix}table-hover-color: #{color-contrast($hover-bg)};\n\n    color: $color;\n    border-color: mix($color, $background, percentage($table-border-factor));\n  }\n}\n// scss-docs-end table-variant\n", "//\n// Labels\n//\n\n.form-label {\n  margin-bottom: $form-label-margin-bottom;\n  @include font-size($form-label-font-size);\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  color: $form-label-color;\n}\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  line-height: $input-line-height;\n  color: $form-label-color;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n}\n", "//\n// Form text\n//\n\n.form-text {\n  margin-top: $form-text-margin-top;\n  @include font-size($form-text-font-size);\n  font-style: $form-text-font-style;\n  font-weight: $form-text-font-weight;\n  color: $form-text-color;\n}\n", "//\n// General form controls (plus a few specific high-level interventions)\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n  appearance: none; // Fix appearance for date inputs in Safari\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  &[type=\"file\"] {\n    overflow: hidden; // prevent pseudo element button overlap\n\n    &:not(:disabled):not([readonly]) {\n      cursor: pointer;\n    }\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($input-box-shadow, $input-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n\n  // Add some height to date inputs on iOS\n  // https://github.com/twbs/bootstrap/issues/23307\n  // TODO: we can remove this workaround once https://bugs.webkit.org/show_bug.cgi?id=198959 is resolved\n  &::-webkit-date-and-time-value {\n    // Multiply line-height by 1em if it has no unit\n    height: if(unit($input-line-height) == \"\", $input-line-height * 1em, $input-line-height);\n  }\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled and read-only inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled,\n  &[readonly] {\n    background-color: $input-disabled-bg;\n    border-color: $input-disabled-border-color;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n\n  // File input buttons theming\n  &::file-selector-button {\n    padding: $input-padding-y $input-padding-x;\n    margin: (-$input-padding-y) (-$input-padding-x);\n    margin-inline-end: $input-padding-x;\n    color: $form-file-button-color;\n    @include gradient-bg($form-file-button-bg);\n    pointer-events: none;\n    border-color: inherit;\n    border-style: solid;\n    border-width: 0;\n    border-inline-end-width: $input-border-width;\n    border-radius: 0; // stylelint-disable-line property-disallowed-list\n    @include transition($btn-transition);\n  }\n\n  &:hover:not(:disabled):not([readonly])::file-selector-button {\n    background-color: $form-file-button-hover-bg;\n  }\n\n  &::-webkit-file-upload-button {\n    padding: $input-padding-y $input-padding-x;\n    margin: (-$input-padding-y) (-$input-padding-x);\n    margin-inline-end: $input-padding-x;\n    color: $form-file-button-color;\n    @include gradient-bg($form-file-button-bg);\n    pointer-events: none;\n    border-color: inherit;\n    border-style: solid;\n    border-width: 0;\n    border-inline-end-width: $input-border-width;\n    border-radius: 0; // stylelint-disable-line property-disallowed-list\n    @include transition($btn-transition);\n  }\n\n  &:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {\n    background-color: $form-file-button-hover-bg;\n  }\n}\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  min-height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n\n  &::file-selector-button {\n    padding: $input-padding-y-sm $input-padding-x-sm;\n    margin: (-$input-padding-y-sm) (-$input-padding-x-sm);\n    margin-inline-end: $input-padding-x-sm;\n  }\n\n  &::-webkit-file-upload-button {\n    padding: $input-padding-y-sm $input-padding-x-sm;\n    margin: (-$input-padding-y-sm) (-$input-padding-x-sm);\n    margin-inline-end: $input-padding-x-sm;\n  }\n}\n\n.form-control-lg {\n  min-height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n\n  &::file-selector-button {\n    padding: $input-padding-y-lg $input-padding-x-lg;\n    margin: (-$input-padding-y-lg) (-$input-padding-x-lg);\n    margin-inline-end: $input-padding-x-lg;\n  }\n\n  &::-webkit-file-upload-button {\n    padding: $input-padding-y-lg $input-padding-x-lg;\n    margin: (-$input-padding-y-lg) (-$input-padding-x-lg);\n    margin-inline-end: $input-padding-x-lg;\n  }\n}\n\n// Make sure textareas don't shrink too much when resized\n// https://github.com/twbs/bootstrap/pull/29124\n// stylelint-disable selector-no-qualifying-type\ntextarea {\n  &.form-control {\n    min-height: $input-height;\n  }\n\n  &.form-control-sm {\n    min-height: $input-height-sm;\n  }\n\n  &.form-control-lg {\n    min-height: $input-height-lg;\n  }\n}\n// stylelint-enable selector-no-qualifying-type\n\n.form-control-color {\n  max-width: 3rem;\n  height: auto; // Override fixed browser height\n  padding: $input-padding-y;\n\n  &:not(:disabled):not([readonly]) {\n    cursor: pointer;\n  }\n\n  &::-moz-color-swatch {\n    height: if(unit($input-line-height) == \"\", $input-line-height * 1em, $input-line-height);\n    @include border-radius($input-border-radius);\n  }\n\n  &::-webkit-color-swatch {\n    height: if(unit($input-line-height) == \"\", $input-line-height * 1em, $input-line-height);\n    @include border-radius($input-border-radius);\n  }\n}\n", "// stylelint-disable property-disallowed-list\n@mixin transition($transition...) {\n  @if length($transition) == 0 {\n    $transition: $transition-base;\n  }\n\n  @if length($transition) > 1 {\n    @each $value in $transition {\n      @if $value == null or $value == none {\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\n      }\n    }\n  }\n\n  @if $enable-transitions {\n    @if nth($transition, 1) != null {\n      transition: $transition;\n    }\n\n    @if $enable-reduced-motion and nth($transition, 1) != null and nth($transition, 1) != none {\n      @media (prefers-reduced-motion: reduce) {\n        transition: none;\n      }\n    }\n  }\n}\n", "// Gradients\n\n// scss-docs-start gradient-bg-mixin\n@mixin gradient-bg($color: null) {\n  background-color: $color;\n\n  @if $enable-gradients {\n    background-image: var(--#{$variable-prefix}gradient);\n  }\n}\n// scss-docs-end gradient-bg-mixin\n\n// scss-docs-start gradient-mixins\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: null, $end-percent: null) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n}\n\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n}\n\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n// scss-docs-end gradient-mixins\n", "// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n\n.form-select {\n  display: block;\n  width: 100%;\n  padding: $form-select-padding-y $form-select-indicator-padding $form-select-padding-y $form-select-padding-x;\n  font-family: $form-select-font-family;\n  @include font-size($form-select-font-size);\n  font-weight: $form-select-font-weight;\n  line-height: $form-select-line-height;\n  color: $form-select-color;\n  background-color: $form-select-bg;\n  background-image: escape-svg($form-select-indicator);\n  background-repeat: no-repeat;\n  background-position: $form-select-bg-position;\n  background-size: $form-select-bg-size;\n  border: $form-select-border-width solid $form-select-border-color;\n  @include border-radius($form-select-border-radius, 0);\n  @include box-shadow($form-select-box-shadow);\n  appearance: none;\n\n  &:focus {\n    border-color: $form-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($form-select-box-shadow, $form-select-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $form-select-focus-box-shadow;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    padding-right: $form-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $form-select-disabled-color;\n    background-color: $form-select-disabled-bg;\n    border-color: $form-select-disabled-border-color;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $form-select-color;\n  }\n}\n\n.form-select-sm {\n  padding-top: $form-select-padding-y-sm;\n  padding-bottom: $form-select-padding-y-sm;\n  padding-left: $form-select-padding-x-sm;\n  @include font-size($form-select-font-size-sm);\n}\n\n.form-select-lg {\n  padding-top: $form-select-padding-y-lg;\n  padding-bottom: $form-select-padding-y-lg;\n  padding-left: $form-select-padding-x-lg;\n  @include font-size($form-select-font-size-lg);\n}\n", "//\n// Check/radio\n//\n\n.form-check {\n  display: block;\n  min-height: $form-check-min-height;\n  padding-left: $form-check-padding-start;\n  margin-bottom: $form-check-margin-bottom;\n\n  .form-check-input {\n    float: left;\n    margin-left: $form-check-padding-start * -1;\n  }\n}\n\n.form-check-input {\n  width: $form-check-input-width;\n  height: $form-check-input-width;\n  margin-top: ($line-height-base - $form-check-input-width) / 2; // line-height minus check height\n  vertical-align: top;\n  background-color: $form-check-input-bg;\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: contain;\n  border: $form-check-input-border;\n  appearance: none;\n  color-adjust: exact; // Keep themed appearance for print\n  @include transition($form-check-transition);\n\n  &[type=\"checkbox\"] {\n    @include border-radius($form-check-input-border-radius);\n  }\n\n  &[type=\"radio\"] {\n    // stylelint-disable-next-line property-disallowed-list\n    border-radius: $form-check-radio-border-radius;\n  }\n\n  &:active {\n    filter: $form-check-input-active-filter;\n  }\n\n  &:focus {\n    border-color: $form-check-input-focus-border;\n    outline: 0;\n    box-shadow: $form-check-input-focus-box-shadow;\n  }\n\n  &:checked {\n    background-color: $form-check-input-checked-bg-color;\n    border-color: $form-check-input-checked-border-color;\n\n    &[type=\"checkbox\"] {\n      @if $enable-gradients {\n        background-image: escape-svg($form-check-input-checked-bg-image), var(--#{$variable-prefix}gradient);\n      } @else {\n        background-image: escape-svg($form-check-input-checked-bg-image);\n      }\n    }\n\n    &[type=\"radio\"] {\n      @if $enable-gradients {\n        background-image: escape-svg($form-check-radio-checked-bg-image), var(--#{$variable-prefix}gradient);\n      } @else {\n        background-image: escape-svg($form-check-radio-checked-bg-image);\n      }\n    }\n  }\n\n  &[type=\"checkbox\"]:indeterminate {\n    background-color: $form-check-input-indeterminate-bg-color;\n    border-color: $form-check-input-indeterminate-border-color;\n\n    @if $enable-gradients {\n      background-image: escape-svg($form-check-input-indeterminate-bg-image), var(--#{$variable-prefix}gradient);\n    } @else {\n      background-image: escape-svg($form-check-input-indeterminate-bg-image);\n    }\n  }\n\n  &:disabled {\n    pointer-events: none;\n    filter: none;\n    opacity: $form-check-input-disabled-opacity;\n  }\n\n  // Use disabled attribute in addition of :disabled pseudo-class\n  // See: https://github.com/twbs/bootstrap/issues/28247\n  &[disabled],\n  &:disabled {\n    ~ .form-check-label {\n      opacity: $form-check-label-disabled-opacity;\n    }\n  }\n}\n\n.form-check-label {\n  color: $form-check-label-color;\n  cursor: $form-check-label-cursor;\n}\n\n//\n// Switch\n//\n\n.form-switch {\n  padding-left: $form-switch-padding-start;\n\n  .form-check-input {\n    width: $form-switch-width;\n    margin-left: $form-switch-padding-start * -1;\n    background-image: escape-svg($form-switch-bg-image);\n    background-position: left center;\n    @include border-radius($form-switch-border-radius);\n    @include transition($form-switch-transition);\n\n    &:focus {\n      background-image: escape-svg($form-switch-focus-bg-image);\n    }\n\n    &:checked {\n      background-position: $form-switch-checked-bg-position;\n\n      @if $enable-gradients {\n        background-image: escape-svg($form-switch-checked-bg-image), var(--#{$variable-prefix}gradient);\n      } @else {\n        background-image: escape-svg($form-switch-checked-bg-image);\n      }\n    }\n  }\n}\n\n.form-check-inline {\n  display: inline-block;\n  margin-right: $form-check-inline-margin-end;\n}\n\n.btn-check {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n\n  &[disabled],\n  &:disabled {\n    + .btn {\n      pointer-events: none;\n      filter: none;\n      opacity: $form-check-btn-check-disabled-opacity;\n    }\n  }\n}\n", "// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.form-range {\n  width: 100%;\n  height: add($form-range-thumb-height, $form-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  background-color: transparent;\n  appearance: none;\n\n  &:focus {\n    outline: 0;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb { box-shadow: $form-range-thumb-focus-box-shadow; }\n    &::-moz-range-thumb     { box-shadow: $form-range-thumb-focus-box-shadow; }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    margin-top: ($form-range-track-height - $form-range-thumb-height) / 2; // Webkit specific\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent; // Why?\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent;\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent;\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &:disabled {\n    pointer-events: none;\n\n    &::-webkit-slider-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n  }\n}\n", ".form-floating {\n  position: relative;\n\n  > .form-control,\n  > .form-select {\n    height: $form-floating-height;\n    padding: $form-floating-padding-y $form-floating-padding-x;\n  }\n\n  > label {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%; // allow textareas\n    padding: $form-floating-padding-y $form-floating-padding-x;\n    pointer-events: none;\n    border: $input-border-width solid transparent; // Required for aligning label's text with the input as it affects inner box model\n    transform-origin: 0 0;\n    @include transition($form-floating-transition);\n  }\n\n  // stylelint-disable no-duplicate-selectors\n  > .form-control {\n    &::placeholder {\n      color: transparent;\n    }\n\n    &:focus,\n    &:not(:placeholder-shown) {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n    // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n    &:-webkit-autofill {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n  }\n\n  > .form-select {\n    padding-top: $form-floating-input-padding-t;\n    padding-bottom: $form-floating-input-padding-b;\n  }\n\n  > .form-control:focus,\n  > .form-control:not(:placeholder-shown),\n  > .form-select {\n    ~ label {\n      opacity: $form-floating-label-opacity;\n      transform: $form-floating-label-transform;\n    }\n  }\n  // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n  > .form-control:-webkit-autofill {\n    ~ label {\n      opacity: $form-floating-label-opacity;\n      transform: $form-floating-label-transform;\n    }\n  }\n  // stylelint-enable no-duplicate-selectors\n}\n", "//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-select {\n    position: relative; // For focus state's z-index\n    flex: 1 1 auto;\n    width: 1%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .form-select:focus {\n    z-index: 3;\n  }\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 3;\n    }\n  }\n}\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-group-addon-padding-y $input-group-addon-padding-x;\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $input-group-addon-font-weight;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control,\n.input-group-lg > .form-select,\n.input-group-lg > .input-group-text,\n.input-group-lg > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .form-select,\n.input-group-sm > .input-group-text,\n.input-group-sm > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .form-select,\n.input-group-sm > .form-select {\n  padding-right: $form-select-padding-x + $form-select-indicator-padding;\n}\n\n\n// Rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.input-group {\n  &:not(.has-validation) {\n    > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),\n    > .dropdown-toggle:nth-last-child(n + 3) {\n      @include border-end-radius(0);\n    }\n  }\n\n  &.has-validation {\n    > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu),\n    > .dropdown-toggle:nth-last-child(n + 4) {\n      @include border-end-radius(0);\n    }\n  }\n\n  $validation-messages: \"\";\n  @each $state in map-keys($form-validation-states) {\n    $validation-messages: $validation-messages + \":not(.\" + unquote($state) + \"-tooltip)\" + \":not(.\" + unquote($state) + \"-feedback)\";\n  }\n\n  > :not(:first-child):not(.dropdown-menu)#{$validation-messages} {\n    margin-left: -$input-border-width;\n    @include border-start-radius(0);\n  }\n}\n", "// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-152293725 for more details\n\n// scss-docs-start form-validation-mixins\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state(\n  $state,\n  $color,\n  $icon,\n  $tooltip-color: color-contrast($color),\n  $tooltip-bg-color: rgba($color, $form-feedback-tooltip-opacity),\n  $focus-box-shadow: 0 0 $input-btn-focus-blur $input-focus-width rgba($color, $input-btn-focus-color-opacity)\n) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    font-style: $form-feedback-font-style;\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: $tooltip-color;\n    background-color: $tooltip-bg-color;\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .form-select {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        &:not([multiple]):not([size]),\n        &:not([multiple])[size=\"1\"] {\n          padding-right: $form-select-feedback-icon-padding-end;\n          background-image: escape-svg($form-select-indicator), escape-svg($icon);\n          background-position: $form-select-bg-position, $form-select-feedback-icon-position;\n          background-size: $form-select-bg-size, $form-select-feedback-icon-size;\n        }\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      &:checked {\n        background-color: $color;\n      }\n\n      &:focus {\n        box-shadow: $focus-box-shadow;\n      }\n\n      ~ .form-check-label {\n        color: $color;\n      }\n    }\n  }\n  .form-check-inline .form-check-input {\n    ~ .#{$state}-feedback {\n      margin-left: .5em;\n    }\n  }\n\n  .input-group .form-control,\n  .input-group .form-select {\n    @include form-validation-state-selector($state) {\n      @if $state == \"valid\" {\n        z-index: 1;\n      } @else if $state == \"invalid\" {\n        z-index: 2;\n      }\n      &:focus {\n        z-index: 3;\n      }\n    }\n  }\n}\n// scss-docs-end form-validation-mixins\n", "//\n// Base styles\n//\n\n.btn {\n  display: inline-block;\n  font-family: $btn-font-family;\n  font-weight: $btn-font-weight;\n  line-height: $btn-line-height;\n  color: $body-color;\n  text-align: center;\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  cursor: if($enable-button-pointers, pointer, null);\n  user-select: none;\n  background-color: transparent;\n  border: $btn-border-width solid transparent;\n  @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-border-radius);\n  @include transition($btn-transition);\n\n  &:hover {\n    color: $body-color;\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  .btn-check:focus + &,\n  &:focus {\n    outline: 0;\n    box-shadow: $btn-focus-box-shadow;\n  }\n\n  .btn-check:checked + &,\n  .btn-check:active + &,\n  &:active,\n  &.active {\n    @include box-shadow($btn-active-box-shadow);\n\n    &:focus {\n      @include box-shadow($btn-focus-box-shadow, $btn-active-box-shadow);\n    }\n  }\n\n  &:disabled,\n  &.disabled,\n  fieldset:disabled & {\n    pointer-events: none;\n    opacity: $btn-disabled-opacity;\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Alternate buttons\n//\n\n// scss-docs-start btn-variant-loops\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @include button-variant($value, $value);\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n// scss-docs-end btn-variant-loops\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  font-weight: $font-weight-normal;\n  color: $btn-link-color;\n  text-decoration: $link-decoration;\n\n  &:hover {\n    color: $btn-link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus {\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $btn-link-disabled-color;\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-border-radius-sm);\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n// scss-docs-start btn-variant-mixin\n@mixin button-variant(\n  $background,\n  $border,\n  $color: color-contrast($background),\n  $hover-background: if($color == $color-contrast-light, shade-color($background, $btn-hover-bg-shade-amount), tint-color($background, $btn-hover-bg-tint-amount)),\n  $hover-border: if($color == $color-contrast-light, shade-color($border, $btn-hover-border-shade-amount), tint-color($border, $btn-hover-border-tint-amount)),\n  $hover-color: color-contrast($hover-background),\n  $active-background: if($color == $color-contrast-light, shade-color($background,$btn-active-bg-shade-amount), tint-color($background, $btn-active-bg-tint-amount)),\n  $active-border: if($color == $color-contrast-light, shade-color($border, $btn-active-border-shade-amount), tint-color($border, $btn-active-border-tint-amount)),\n  $active-color: color-contrast($active-background),\n  $disabled-background: $background,\n  $disabled-border: $border,\n  $disabled-color: color-contrast($disabled-background)\n) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  &:hover {\n    color: $hover-color;\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  .btn-check:focus + &,\n  &:focus {\n    color: $hover-color;\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    @if $enable-shadows {\n      @include box-shadow($btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5));\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5);\n    }\n  }\n\n  .btn-check:checked + &,\n  .btn-check:active + &,\n  &:active,\n  &.active,\n  .show > &.dropdown-toggle {\n    color: $active-color;\n    background-color: $active-background;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5);\n      }\n    }\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $disabled-color;\n    background-color: $disabled-background;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n    border-color: $disabled-border;\n  }\n}\n// scss-docs-end btn-variant-mixin\n\n// scss-docs-start btn-outline-variant-mixin\n@mixin button-outline-variant(\n  $color,\n  $color-hover: color-contrast($color),\n  $active-background: $color,\n  $active-border: $color,\n  $active-color: color-contrast($active-background)\n) {\n  color: $color;\n  border-color: $color;\n\n  &:hover {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  .btn-check:focus + &,\n  &:focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  .btn-check:checked + &,\n  .btn-check:active + &,\n  &:active,\n  &.active,\n  &.dropdown-toggle.show {\n    color: $active-color;\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $color;\n    background-color: transparent;\n  }\n}\n// scss-docs-end btn-outline-variant-mixin\n\n// scss-docs-start btn-size-mixin\n@mixin button-size($padding-y, $padding-x, $font-size, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n// scss-docs-end btn-size-mixin\n", ".fade {\n  @include transition($transition-fade);\n\n  &:not(.show) {\n    opacity: 0;\n  }\n}\n\n// scss-docs-start collapse-classes\n.collapse {\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.collapsing {\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n}\n// scss-docs-end collapse-classes\n", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropend,\n.dropdown,\n.dropstart {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n\n  // Generate the caret automatically\n  @include caret();\n}\n\n// The dropdown menu\n.dropdown-menu {\n  position: absolute;\n  z-index: $zindex-dropdown;\n  display: none; // none by default, but block on \"open\" of the menu\n  min-width: $dropdown-min-width;\n  padding: $dropdown-padding-y $dropdown-padding-x;\n  margin: 0; // Override default margin of ul\n  @include font-size($dropdown-font-size);\n  color: $dropdown-color;\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: $dropdown-bg;\n  background-clip: padding-box;\n  border: $dropdown-border-width solid $dropdown-border-color;\n  @include border-radius($dropdown-border-radius);\n  @include box-shadow($dropdown-box-shadow);\n\n  &[data-bs-popper] {\n    top: 100%;\n    left: 0;\n    margin-top: $dropdown-spacer;\n  }\n}\n\n// scss-docs-start responsive-breakpoints\n// We deliberately hardcode the `bs-` prefix because we check\n// this custom property in JS to determine <PERSON><PERSON>'s positioning\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .dropdown-menu#{$infix}-start {\n      --bs-position: start;\n\n      &[data-bs-popper] {\n        right: auto #{\"/* rtl:ignore */\"};\n        left: 0 #{\"/* rtl:ignore */\"};\n      }\n    }\n\n    .dropdown-menu#{$infix}-end {\n      --bs-position: end;\n\n      &[data-bs-popper] {\n        right: 0 #{\"/* rtl:ignore */\"};\n        left: auto #{\"/* rtl:ignore */\"};\n      }\n    }\n  }\n}\n// scss-docs-end responsive-breakpoints\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu[data-bs-popper] {\n    top: auto;\n    bottom: 100%;\n    margin-top: 0;\n    margin-bottom: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(up);\n  }\n}\n\n.dropend {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: auto;\n    left: 100%;\n    margin-top: 0;\n    margin-left: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(end);\n    &::after {\n      vertical-align: 0;\n    }\n  }\n}\n\n.dropstart {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: 100%;\n    left: auto;\n    margin-top: 0;\n    margin-right: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(start);\n    &::before {\n      vertical-align: 0;\n    }\n  }\n}\n\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  height: 0;\n  margin: $dropdown-divider-margin-y 0;\n  overflow: hidden;\n  border-top: 1px solid $dropdown-divider-bg;\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: $dropdown-link-color;\n  text-align: inherit; // For `<button>`s\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background-color: transparent; // For `<button>`s\n  border: 0; // For `<button>`s\n\n  // Prevent dropdown overflow if there's no padding\n  // See https://github.com/twbs/bootstrap/pull/27703\n  @if $dropdown-padding-y == 0 {\n    &:first-child {\n      @include border-top-radius($dropdown-inner-border-radius);\n    }\n\n    &:last-child {\n      @include border-bottom-radius($dropdown-inner-border-radius);\n    }\n  }\n\n  &:hover,\n  &:focus {\n    color: $dropdown-link-hover-color;\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    @include gradient-bg($dropdown-link-hover-bg);\n  }\n\n  &.active,\n  &:active {\n    color: $dropdown-link-active-color;\n    text-decoration: none;\n    @include gradient-bg($dropdown-link-active-bg);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $dropdown-link-disabled-color;\n    pointer-events: none;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: $dropdown-header-padding;\n  margin-bottom: 0; // for use with heading elements\n  @include font-size($font-size-sm);\n  color: $dropdown-header-color;\n  white-space: nowrap; // as with > li > a\n}\n\n// Dropdown text\n.dropdown-item-text {\n  display: block;\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  color: $dropdown-link-color;\n}\n\n// Dark dropdowns\n.dropdown-menu-dark {\n  color: $dropdown-dark-color;\n  background-color: $dropdown-dark-bg;\n  border-color: $dropdown-dark-border-color;\n  @include box-shadow($dropdown-dark-box-shadow);\n\n  .dropdown-item {\n    color: $dropdown-dark-link-color;\n\n    &:hover,\n    &:focus {\n      color: $dropdown-dark-link-hover-color;\n      @include gradient-bg($dropdown-dark-link-hover-bg);\n    }\n\n    &.active,\n    &:active {\n      color: $dropdown-dark-link-active-color;\n      @include gradient-bg($dropdown-dark-link-active-bg);\n    }\n\n    &.disabled,\n    &:disabled {\n      color: $dropdown-dark-link-disabled-color;\n    }\n  }\n\n  .dropdown-divider {\n    border-color: $dropdown-dark-divider-bg;\n  }\n\n  .dropdown-item-text {\n    color: $dropdown-dark-link-color;\n  }\n\n  .dropdown-header {\n    color: $dropdown-dark-header-color;\n  }\n}\n", "// scss-docs-start caret-mixins\n@mixin caret-down {\n  border-top: $caret-width solid;\n  border-right: $caret-width solid transparent;\n  border-bottom: 0;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-up {\n  border-top: 0;\n  border-right: $caret-width solid transparent;\n  border-bottom: $caret-width solid;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-end {\n  border-top: $caret-width solid transparent;\n  border-right: 0;\n  border-bottom: $caret-width solid transparent;\n  border-left: $caret-width solid;\n}\n\n@mixin caret-start {\n  border-top: $caret-width solid transparent;\n  border-right: $caret-width solid;\n  border-bottom: $caret-width solid transparent;\n}\n\n@mixin caret($direction: down) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $caret-spacing;\n      vertical-align: $caret-vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down();\n      } @else if $direction == up {\n        @include caret-up();\n      } @else if $direction == end {\n        @include caret-end();\n      }\n    }\n\n    @if $direction == start {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $caret-spacing;\n        vertical-align: $caret-vertical-align;\n        content: \"\";\n        @include caret-start();\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n// scss-docs-end caret-mixins\n", "// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 1 1 auto;\n  }\n\n  // Bring the hover, focused, and \"active\" buttons to the front to overlay\n  // the borders properly\n  > .btn-check:checked + .btn,\n  > .btn-check:focus + .btn,\n  > .btn:hover,\n  > .btn:focus,\n  > .btn:active,\n  > .btn.active {\n    z-index: 1;\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n  // Prevent double borders when buttons are next to each other\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-left: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-end-radius(0);\n  }\n\n  // The left radius should be 0 if the button is:\n  // - the \"third or more\" child\n  // - the second child and the previous element isn't `.btn-check` (making it the first child visually)\n  // - part of a btn-group which isn't the first child\n  > .btn:nth-child(n + 3),\n  > :not(.btn-check) + .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-start-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropend &::after {\n    margin-left: 0;\n  }\n\n  .dropstart &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  > .btn,\n  > .btn-group {\n    width: 100%;\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-top: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-bottom-radius(0);\n  }\n\n  > .btn ~ .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-top-radius(0);\n  }\n}\n", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s, `<ul>`s or `<ol>`s.\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: $nav-link-padding-y $nav-link-padding-x;\n  @include font-size($nav-link-font-size);\n  font-weight: $nav-link-font-weight;\n  color: $nav-link-color;\n  text-decoration: if($link-decoration == none, null, none);\n  @include transition($nav-link-transition);\n\n  &:hover,\n  &:focus {\n    color: $nav-link-hover-color;\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  // Disabled state lightens text\n  &.disabled {\n    color: $nav-link-disabled-color;\n    pointer-events: none;\n    cursor: default;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  border-bottom: $nav-tabs-border-width solid $nav-tabs-border-color;\n\n  .nav-link {\n    margin-bottom: -$nav-tabs-border-width;\n    background: none;\n    border: $nav-tabs-border-width solid transparent;\n    @include border-top-radius($nav-tabs-border-radius);\n\n    &:hover,\n    &:focus {\n      border-color: $nav-tabs-link-hover-border-color;\n      // Prevents active .nav-link tab overlapping focus outline of previous/next .nav-link\n      isolation: isolate;\n    }\n\n    &.disabled {\n      color: $nav-link-disabled-color;\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: $nav-tabs-link-active-color;\n    background-color: $nav-tabs-link-active-bg;\n    border-color: $nav-tabs-link-active-border-color;\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: -$nav-tabs-border-width;\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  .nav-link {\n    background: none;\n    border: 0;\n    @include border-radius($nav-pills-border-radius);\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    color: $nav-pills-link-active-color;\n    @include gradient-bg($nav-pills-link-active-bg);\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n  > .nav-link,\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n  > .nav-link,\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n.nav-fill,\n.nav-justified {\n  .nav-item .nav-link {\n    width: 100%; // Make sure button will grow\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n", "// Contents\n//\n// Navbar\n// Navbar brand\n// Navbar nav\n// Navbar text\n// Responsive navbar\n// Navbar position\n// Navbar themes\n\n\n// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding-top: $navbar-padding-y;\n  padding-right: $navbar-padding-x; // default: null\n  padding-bottom: $navbar-padding-y;\n  padding-left: $navbar-padding-x; // default: null\n  @include gradient-bg();\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properties so that content nested within behave properly.\n  // The `flex-wrap` property is inherited to simplify the expanded navbars\n  %container-flex-properties {\n    display: flex;\n    flex-wrap: inherit;\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  > .container,\n  > .container-fluid {\n    @extend %container-flex-properties;\n  }\n\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    > .container#{breakpoint-infix($breakpoint, $container-max-widths)} {\n      @extend %container-flex-properties;\n    }\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  padding-top: $navbar-brand-padding-y;\n  padding-bottom: $navbar-brand-padding-y;\n  margin-right: $navbar-brand-margin-end;\n  @include font-size($navbar-brand-font-size);\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap;\n\n  &:hover,\n  &:focus {\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .nav-link {\n    padding-right: 0;\n    padding-left: 0;\n  }\n\n  .dropdown-menu {\n    position: static;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: $navbar-toggler-padding-y $navbar-toggler-padding-x;\n  @include font-size($navbar-toggler-font-size);\n  line-height: 1;\n  background-color: transparent; // remove default button style\n  border: $border-width solid transparent; // remove default button style\n  @include border-radius($navbar-toggler-border-radius);\n  @include transition($navbar-toggler-transition);\n\n  &:hover {\n    text-decoration: none;\n  }\n\n  &:focus {\n    text-decoration: none;\n    outline: 0;\n    box-shadow: 0 0 0 $navbar-toggler-focus-width;\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: 100%;\n}\n\n.navbar-nav-scroll {\n  max-height: var(--#{$variable-prefix}scroll-height, 75vh);\n  overflow-y: auto;\n}\n\n// scss-docs-start navbar-expand-loop\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    // stylelint-disable-next-line scss/selector-no-union-class-name\n    &#{$infix} {\n      @include media-breakpoint-up($next) {\n        flex-wrap: nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .nav-link {\n            padding-right: $navbar-nav-link-padding-x;\n            padding-left: $navbar-nav-link-padding-x;\n          }\n        }\n\n        .navbar-nav-scroll {\n          overflow: visible;\n        }\n\n        .navbar-collapse {\n          display: flex !important; // stylelint-disable-line declaration-no-important\n          flex-basis: auto;\n        }\n\n        .navbar-toggler {\n          display: none;\n        }\n      }\n    }\n  }\n}\n// scss-docs-end navbar-expand-loop\n\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n// Dark links against a light background\n.navbar-light {\n  .navbar-brand {\n    color: $navbar-light-brand-color;\n\n    &:hover,\n    &:focus {\n      color: $navbar-light-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-light-color;\n\n      &:hover,\n      &:focus {\n        color: $navbar-light-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-light-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .nav-link.active {\n      color: $navbar-light-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-light-color;\n    border-color: $navbar-light-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-light-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-light-color;\n\n    a,\n    a:hover,\n    a:focus  {\n      color: $navbar-light-active-color;\n    }\n  }\n}\n\n// White links against a dark background\n.navbar-dark {\n  .navbar-brand {\n    color: $navbar-dark-brand-color;\n\n    &:hover,\n    &:focus {\n      color: $navbar-dark-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-dark-color;\n\n      &:hover,\n      &:focus {\n        color: $navbar-dark-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-dark-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .nav-link.active {\n      color: $navbar-dark-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-dark-color;\n    border-color: $navbar-dark-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-dark-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-dark-color;\n    a,\n    a:hover,\n    a:focus {\n      color: $navbar-dark-active-color;\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\n  height: $card-height;\n  word-wrap: break-word;\n  background-color: $card-bg;\n  background-clip: border-box;\n  border: $card-border-width solid $card-border-color;\n  @include border-radius($card-border-radius);\n\n  > hr {\n    margin-right: 0;\n    margin-left: 0;\n  }\n\n  > .list-group {\n    border-top: inherit;\n    border-bottom: inherit;\n\n    &:first-child {\n      border-top-width: 0;\n      @include border-top-radius($card-inner-border-radius);\n    }\n\n    &:last-child  {\n      border-bottom-width: 0;\n      @include border-bottom-radius($card-inner-border-radius);\n    }\n  }\n\n  // Due to specificity of the above selector (`.card > .list-group`), we must\n  // use a child selector here to prevent double borders.\n  > .card-header + .list-group,\n  > .list-group + .card-footer {\n    border-top: 0;\n  }\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  padding: $card-spacer-y $card-spacer-x;\n  color: $card-color;\n}\n\n.card-title {\n  margin-bottom: $card-title-spacer-y;\n}\n\n.card-subtitle {\n  margin-top: -$card-title-spacer-y / 2;\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  &:hover {\n    text-decoration: none;\n  }\n\n  + .card-link {\n    margin-left: $card-spacer-x;\n  }\n}\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: $card-cap-padding-y $card-cap-padding-x;\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  color: $card-cap-color;\n  background-color: $card-cap-bg;\n  border-bottom: $card-border-width solid $card-border-color;\n\n  &:first-child {\n    @include border-radius($card-inner-border-radius $card-inner-border-radius 0 0);\n  }\n}\n\n.card-footer {\n  padding: $card-cap-padding-y $card-cap-padding-x;\n  color: $card-cap-color;\n  background-color: $card-cap-bg;\n  border-top: $card-border-width solid $card-border-color;\n\n  &:last-child {\n    @include border-radius(0 0 $card-inner-border-radius $card-inner-border-radius);\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: -$card-cap-padding-x / 2;\n  margin-bottom: -$card-cap-padding-y;\n  margin-left: -$card-cap-padding-x / 2;\n  border-bottom: 0;\n\n  @if $nav-tabs-link-active-bg != $card-bg {\n    .nav-link.active {\n      background-color: $card-bg;\n      border-bottom-color: $card-bg;\n    }\n  }\n}\n\n.card-header-pills {\n  margin-right: -$card-cap-padding-x / 2;\n  margin-left: -$card-cap-padding-x / 2;\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: $card-img-overlay-padding;\n  @include border-radius($card-inner-border-radius);\n}\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n}\n\n.card-img,\n.card-img-top {\n  @include border-top-radius($card-inner-border-radius);\n}\n\n.card-img,\n.card-img-bottom {\n  @include border-bottom-radius($card-inner-border-radius);\n}\n\n\n//\n// Card groups\n//\n\n.card-group {\n  // The child selector allows nested `.card` within `.card-group`\n  // to display properly.\n  > .card {\n    margin-bottom: $card-group-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    // The child selector allows nested `.card` within `.card-group`\n    // to display properly.\n    > .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-bottom: 0;\n\n      + .card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:not(:last-child) {\n          @include border-end-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-right-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-right-radius: 0;\n          }\n        }\n\n        &:not(:first-child) {\n          @include border-start-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-left-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.accordion-button {\n  position: relative;\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: $accordion-button-padding-y $accordion-button-padding-x;\n  @include font-size($font-size-base);\n  color: $accordion-button-color;\n  text-align: left; // Reset button style\n  background-color: $accordion-button-bg;\n  border: 0;\n  @include border-radius(0);\n  overflow-anchor: none;\n  @include transition($accordion-transition);\n\n  &:not(.collapsed) {\n    color: $accordion-button-active-color;\n    background-color: $accordion-button-active-bg;\n    box-shadow: inset 0 ($accordion-border-width * -1) 0 $accordion-border-color;\n\n    &::after {\n      background-image: escape-svg($accordion-button-active-icon);\n      transform: $accordion-icon-transform;\n    }\n  }\n\n  // Accordion icon\n  &::after {\n    flex-shrink: 0;\n    width: $accordion-icon-width;\n    height: $accordion-icon-width;\n    margin-left: auto;\n    content: \"\";\n    background-image: escape-svg($accordion-button-icon);\n    background-repeat: no-repeat;\n    background-size: $accordion-icon-width;\n    @include transition($accordion-icon-transition);\n  }\n\n  &:hover {\n    z-index: 2;\n  }\n\n  &:focus {\n    z-index: 3;\n    border-color: $accordion-button-focus-border-color;\n    outline: 0;\n    box-shadow: $accordion-button-focus-box-shadow;\n  }\n}\n\n.accordion-header {\n  margin-bottom: 0;\n}\n\n.accordion-item {\n  background-color: $accordion-bg;\n  border: $accordion-border-width solid $accordion-border-color;\n\n  &:first-of-type {\n    @include border-top-radius($accordion-border-radius);\n\n    .accordion-button {\n      @include border-top-radius($accordion-inner-border-radius);\n    }\n  }\n\n  &:not(:first-of-type) {\n    border-top: 0;\n  }\n\n  // Only set a border-radius on the last item if the accordion is collapsed\n  &:last-of-type {\n    @include border-bottom-radius($accordion-border-radius);\n\n    .accordion-button {\n      &.collapsed {\n        @include border-bottom-radius($accordion-inner-border-radius);\n      }\n    }\n\n    .accordion-collapse {\n      @include border-bottom-radius($accordion-border-radius);\n    }\n  }\n}\n\n.accordion-body {\n  padding: $accordion-body-padding-y $accordion-body-padding-x;\n}\n\n\n// Flush accordion items\n//\n// Remove borders and border-radius to keep accordion items edge-to-edge.\n\n.accordion-flush {\n  .accordion-collapse {\n    border-width: 0;\n  }\n\n  .accordion-item {\n    border-right: 0;\n    border-left: 0;\n    @include border-radius(0);\n\n    &:first-child { border-top: 0; }\n    &:last-child { border-bottom: 0; }\n\n    .accordion-button {\n      @include border-radius(0);\n    }\n  }\n}\n", ".breadcrumb {\n  display: flex;\n  flex-wrap: wrap;\n  padding: $breadcrumb-padding-y $breadcrumb-padding-x;\n  margin-bottom: $breadcrumb-margin-bottom;\n  @include font-size($breadcrumb-font-size);\n  list-style: none;\n  background-color: $breadcrumb-bg;\n  @include border-radius($breadcrumb-border-radius);\n}\n\n.breadcrumb-item {\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\n  + .breadcrumb-item {\n    padding-left: $breadcrumb-item-padding-x;\n\n    &::before {\n      float: left; // Suppress inline spacings and underlining of the separator\n      padding-right: $breadcrumb-item-padding-x;\n      color: $breadcrumb-divider-color;\n      content: var(--#{$variable-prefix}breadcrumb-divider, escape-svg($breadcrumb-divider)) #{\"/* rtl:\"} var(--#{$variable-prefix}breadcrumb-divider, escape-svg($breadcrumb-divider-flipped)) #{\"*/\"};\n    }\n  }\n\n  &.active {\n    color: $breadcrumb-active-color;\n  }\n}\n", ".pagination {\n  display: flex;\n  @include list-unstyled();\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  color: $pagination-color;\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: $pagination-bg;\n  border: $pagination-border-width solid $pagination-border-color;\n  @include transition($pagination-transition);\n\n  &:hover {\n    z-index: 2;\n    color: $pagination-hover-color;\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    background-color: $pagination-hover-bg;\n    border-color: $pagination-hover-border-color;\n  }\n\n  &:focus {\n    z-index: 3;\n    color: $pagination-focus-color;\n    background-color: $pagination-focus-bg;\n    outline: $pagination-focus-outline;\n    box-shadow: $pagination-focus-box-shadow;\n  }\n}\n\n.page-item {\n  &:not(:first-child) .page-link {\n    margin-left: $pagination-margin-start;\n  }\n\n  &.active .page-link {\n    z-index: 3;\n    color: $pagination-active-color;\n    @include gradient-bg($pagination-active-bg);\n    border-color: $pagination-active-border-color;\n  }\n\n  &.disabled .page-link {\n    color: $pagination-disabled-color;\n    pointer-events: none;\n    background-color: $pagination-disabled-bg;\n    border-color: $pagination-disabled-border-color;\n  }\n}\n\n\n//\n// Sizing\n//\n@include pagination-size($pagination-padding-y, $pagination-padding-x, null, $pagination-border-radius);\n\n.pagination-lg {\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $pagination-border-radius-lg);\n}\n\n.pagination-sm {\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $pagination-border-radius-sm);\n}\n", "// Pagination\n\n// scss-docs-start pagination-mixin\n@mixin pagination-size($padding-y, $padding-x, $font-size, $border-radius) {\n  .page-link {\n    padding: $padding-y $padding-x;\n    @include font-size($font-size);\n  }\n\n  .page-item {\n    @if $pagination-margin-start == (-$pagination-border-width) {\n      &:first-child {\n        .page-link {\n          @include border-start-radius($border-radius);\n        }\n      }\n\n      &:last-child {\n        .page-link {\n          @include border-end-radius($border-radius);\n        }\n      }\n    } @else {\n      //Add border-radius to all pageLinks in case they have left margin\n      .page-link {\n        @include border-radius($border-radius);\n      }\n    }\n  }\n}\n// scss-docs-end pagination-mixin\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  display: inline-block;\n  padding: $badge-padding-y $badge-padding-x;\n  @include font-size($badge-font-size);\n  font-weight: $badge-font-weight;\n  line-height: 1;\n  color: $badge-color;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius($badge-border-radius);\n  @include gradient-bg();\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n", "//\n// Base styles\n//\n\n.alert {\n  position: relative;\n  padding: $alert-padding-y $alert-padding-x;\n  margin-bottom: $alert-margin-bottom;\n  border: $alert-border-width solid transparent;\n  @include border-radius($alert-border-radius);\n}\n\n// Headings for larger alerts\n.alert-heading {\n  // Specified to prevent conflicts of changing $headings-color\n  color: inherit;\n}\n\n// Provide class for links that match alerts\n.alert-link {\n  font-weight: $alert-link-font-weight;\n}\n\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissible {\n  padding-right: $alert-dismissible-padding-r;\n\n  // Adjust close link position\n  .btn-close {\n    position: absolute;\n    top: 0;\n    right: 0;\n    z-index: $stretched-link-z-index + 1;\n    padding: $alert-padding-y * 1.25 $alert-padding-x;\n  }\n}\n\n\n// scss-docs-start alert-modifiers\n// Generate contextual modifier classes for colorizing the alert.\n\n@each $state, $value in $theme-colors {\n  $alert-background: shift-color($value, $alert-bg-scale);\n  $alert-border: shift-color($value, $alert-border-scale);\n  $alert-color: shift-color($value, $alert-color-scale);\n  @if (contrast-ratio($alert-background, $alert-color) < $min-contrast-ratio) {\n    $alert-color: mix($value, color-contrast($alert-background), abs($alert-color-scale));\n  }\n  .alert-#{$state} {\n    @include alert-variant($alert-background, $alert-border, $alert-color);\n  }\n}\n// scss-docs-end alert-modifiers\n", "// scss-docs-start alert-variant-mixin\n@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  .alert-link {\n    color: shade-color($color, 20%);\n  }\n}\n// scss-docs-end alert-variant-mixin\n", "// Disable animation if transitions are disabled\n\n// scss-docs-start progress-keyframes\n@if $enable-transitions {\n  @keyframes progress-bar-stripes {\n    0% { background-position-x: $progress-height; }\n  }\n}\n// scss-docs-end progress-keyframes\n\n.progress {\n  display: flex;\n  height: $progress-height;\n  overflow: hidden; // force rounded corners by cropping it\n  @include font-size($progress-font-size);\n  background-color: $progress-bg;\n  @include border-radius($progress-border-radius);\n  @include box-shadow($progress-box-shadow);\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n  color: $progress-bar-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $progress-bar-bg;\n  @include transition($progress-bar-transition);\n}\n\n.progress-bar-striped {\n  @include gradient-striped();\n  background-size: $progress-height $progress-height;\n}\n\n@if $enable-transitions {\n  .progress-bar-animated {\n    animation: $progress-bar-animation-timing progress-bar-stripes;\n\n    @if $enable-reduced-motion {\n      @media (prefers-reduced-motion: reduce) {\n        animation: none;\n      }\n    }\n  }\n}\n", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n  @include border-radius($list-group-border-radius);\n}\n\n.list-group-numbered {\n  list-style-type: none;\n  counter-reset: section;\n\n  > li::before {\n    // Increments only this instance of the section counter\n    content: counters(section, \".\") \". \";\n    counter-increment: section;\n  }\n}\n\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: $list-group-action-color;\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  &:hover,\n  &:focus {\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\n    color: $list-group-action-hover-color;\n    text-decoration: none;\n    background-color: $list-group-hover-bg;\n  }\n\n  &:active {\n    color: $list-group-action-active-color;\n    background-color: $list-group-action-active-bg;\n  }\n}\n\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: $list-group-item-padding-y $list-group-item-padding-x;\n  color: $list-group-color;\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: $list-group-bg;\n  border: $list-group-border-width solid $list-group-border-color;\n\n  &:first-child {\n    @include border-top-radius(inherit);\n  }\n\n  &:last-child {\n    @include border-bottom-radius(inherit);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $list-group-disabled-color;\n    pointer-events: none;\n    background-color: $list-group-disabled-bg;\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: $list-group-active-color;\n    background-color: $list-group-active-bg;\n    border-color: $list-group-active-border-color;\n  }\n\n  & + & {\n    border-top-width: 0;\n\n    &.active {\n      margin-top: -$list-group-border-width;\n      border-top-width: $list-group-border-width;\n    }\n  }\n}\n\n\n// Horizontal\n//\n// Change the layout of list group items from vertical (default) to horizontal.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .list-group-horizontal#{$infix} {\n      flex-direction: row;\n\n      > .list-group-item {\n        &:first-child {\n          @include border-bottom-start-radius($list-group-border-radius);\n          @include border-top-end-radius(0);\n        }\n\n        &:last-child {\n          @include border-top-end-radius($list-group-border-radius);\n          @include border-bottom-start-radius(0);\n        }\n\n        &.active {\n          margin-top: 0;\n        }\n\n        + .list-group-item {\n          border-top-width: $list-group-border-width;\n          border-left-width: 0;\n\n          &.active {\n            margin-left: -$list-group-border-width;\n            border-left-width: $list-group-border-width;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  @include border-radius(0);\n\n  > .list-group-item {\n    border-width: 0 0 $list-group-border-width;\n\n    &:last-child {\n      border-bottom-width: 0;\n    }\n  }\n}\n\n\n// scss-docs-start list-group-modifiers\n// List group contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $state, $value in $theme-colors {\n  $list-group-variant-bg: shift-color($value, $list-group-item-bg-scale);\n  $list-group-variant-color: shift-color($value, $list-group-item-color-scale);\n  @if (contrast-ratio($list-group-variant-bg, $list-group-variant-color) < $min-contrast-ratio) {\n    $list-group-variant-color: mix($value, color-contrast($list-group-variant-bg), abs($list-group-item-color-scale));\n  }\n\n  @include list-group-item-variant($state, $list-group-variant-bg, $list-group-variant-color);\n}\n// scss-docs-end list-group-modifiers\n", "// List Groups\n\n// scss-docs-start list-group-mixin\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n\n    &.list-group-item-action {\n      &:hover,\n      &:focus {\n        color: $color;\n        background-color: shade-color($background, 10%);\n      }\n\n      &.active {\n        color: $white;\n        background-color: $color;\n        border-color: $color;\n      }\n    }\n  }\n}\n// scss-docs-end list-group-mixin\n", "// transparent background and border properties included for button version.\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n.btn-close {\n  box-sizing: content-box;\n  width: $btn-close-width;\n  height: $btn-close-height;\n  padding: $btn-close-padding-y $btn-close-padding-x;\n  color: $btn-close-color;\n  background: transparent escape-svg($btn-close-bg) center / $btn-close-width auto no-repeat; // include transparent for button elements\n  border: 0; // for button elements\n  @include border-radius();\n  opacity: $btn-close-opacity;\n\n  // Override <a>'s hover style\n  &:hover {\n    color: $btn-close-color;\n    text-decoration: none;\n    opacity: $btn-close-hover-opacity;\n  }\n\n  &:focus {\n    outline: 0;\n    box-shadow: $btn-close-focus-shadow;\n    opacity: $btn-close-focus-opacity;\n  }\n\n  &:disabled,\n  &.disabled {\n    pointer-events: none;\n    user-select: none;\n    opacity: $btn-close-disabled-opacity;\n  }\n}\n\n.btn-close-white {\n  filter: $btn-close-white-filter;\n}\n", ".toast {\n  width: $toast-max-width;\n  max-width: 100%;\n  @include font-size($toast-font-size);\n  color: $toast-color;\n  pointer-events: auto;\n  background-color: $toast-background-color;\n  background-clip: padding-box;\n  border: $toast-border-width solid $toast-border-color;\n  box-shadow: $toast-box-shadow;\n  @include border-radius($toast-border-radius);\n\n  &:not(.showing):not(.show) {\n    opacity: 0;\n  }\n\n  &.hide {\n    display: none;\n  }\n}\n\n.toast-container {\n  width: max-content;\n  max-width: 100%;\n  pointer-events: none;\n\n  > :not(:last-child) {\n    margin-bottom: $toast-spacing;\n  }\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: $toast-padding-y $toast-padding-x;\n  color: $toast-header-color;\n  background-color: $toast-header-background-color;\n  background-clip: padding-box;\n  border-bottom: $toast-border-width solid $toast-header-border-color;\n  @include border-top-radius(subtract($toast-border-radius, $toast-border-width));\n\n  .btn-close {\n    margin-right: $toast-padding-x / -2;\n    margin-left: $toast-padding-x;\n  }\n}\n\n.toast-body {\n  padding: $toast-padding-x; // apply to both vertical and horizontal\n  word-wrap: break-word;\n}\n", "// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and stuff\n\n\n// Container that the modal scrolls within\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex-modal;\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow-x: hidden;\n  overflow-y: auto;\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\n  // See also https://github.com/twbs/bootstrap/issues/17695\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: $modal-dialog-margin;\n  // allow clicks to pass through for custom click handling to close modal\n  pointer-events: none;\n\n  // When fading in the modal, animate it to slide down\n  .modal.fade & {\n    @include transition($modal-transition);\n    transform: $modal-fade-transform;\n  }\n  .modal.show & {\n    transform: $modal-show-transform;\n  }\n\n  // When trying to close, animate focus to scale\n  .modal.modal-static & {\n    transform: $modal-scale-transform;\n  }\n}\n\n.modal-dialog-scrollable {\n  height: subtract(100%, $modal-dialog-margin * 2);\n\n  .modal-content {\n    max-height: 100%;\n    overflow: hidden;\n  }\n\n  .modal-body {\n    overflow-y: auto;\n  }\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: subtract(100%, $modal-dialog-margin * 2);\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%; // Ensure `.modal-content` extends the full width of the parent `.modal-dialog`\n  // counteract the pointer-events: none; in the .modal-dialog\n  color: $modal-content-color;\n  pointer-events: auto;\n  background-color: $modal-content-bg;\n  background-clip: padding-box;\n  border: $modal-content-border-width solid $modal-content-border-color;\n  @include border-radius($modal-content-border-radius);\n  @include box-shadow($modal-content-box-shadow-xs);\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex-modal-backdrop;\n  width: 100vw;\n  height: 100vh;\n  background-color: $modal-backdrop-bg;\n\n  // Fade for backdrop\n  &.fade { opacity: 0; }\n  &.show { opacity: $modal-backdrop-opacity; }\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  display: flex;\n  flex-shrink: 0;\n  align-items: center;\n  justify-content: space-between; // Put modal header elements (title and dismiss) on opposite ends\n  padding: $modal-header-padding;\n  border-bottom: $modal-header-border-width solid $modal-header-border-color;\n  @include border-top-radius($modal-content-inner-border-radius);\n\n  .btn-close {\n    padding: ($modal-header-padding-y / 2) ($modal-header-padding-x / 2);\n    margin: ($modal-header-padding-y / -2) ($modal-header-padding-x / -2) ($modal-header-padding-y / -2) auto;\n  }\n}\n\n// Title text within header\n.modal-title {\n  margin-bottom: 0;\n  line-height: $modal-title-line-height;\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\n  // when there should be a fixed height on `.modal-dialog`.\n  flex: 1 1 auto;\n  padding: $modal-inner-padding;\n}\n\n// Footer (for actions)\n.modal-footer {\n  display: flex;\n  flex-wrap: wrap;\n  flex-shrink: 0;\n  align-items: center; // vertically center\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\n  padding: $modal-inner-padding - $modal-footer-margin-between / 2;\n  border-top: $modal-footer-border-width solid $modal-footer-border-color;\n  @include border-bottom-radius($modal-content-inner-border-radius);\n\n  // Place margin between footer elements\n  // This solution is far from ideal because of the universal selector usage,\n  // but is needed to fix https://github.com/twbs/bootstrap/issues/24800\n  > * {\n    margin: $modal-footer-margin-between / 2;\n  }\n}\n\n// Scale up the modal\n@include media-breakpoint-up(sm) {\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    max-width: $modal-md;\n    margin: $modal-dialog-margin-y-sm-up auto;\n  }\n\n  .modal-dialog-scrollable {\n    height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n  }\n\n  .modal-dialog-centered {\n    min-height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n  }\n\n  .modal-content {\n    @include box-shadow($modal-content-box-shadow-sm-up);\n  }\n\n  .modal-sm { max-width: $modal-sm; }\n}\n\n@include media-breakpoint-up(lg) {\n  .modal-lg,\n  .modal-xl {\n    max-width: $modal-lg;\n  }\n}\n\n@include media-breakpoint-up(xl) {\n  .modal-xl { max-width: $modal-xl; }\n}\n\n// scss-docs-start modal-fullscreen-loop\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n  $postfix: if($infix != \"\", $infix + \"-down\", \"\");\n\n  @include media-breakpoint-down($breakpoint) {\n    .modal-fullscreen#{$postfix} {\n      width: 100vw;\n      max-width: none;\n      height: 100%;\n      margin: 0;\n\n      .modal-content {\n        height: 100%;\n        border: 0;\n        @include border-radius(0);\n      }\n\n      .modal-header {\n        @include border-radius(0);\n      }\n\n      .modal-body {\n        overflow-y: auto;\n      }\n\n      .modal-footer {\n        @include border-radius(0);\n      }\n    }\n  }\n}\n// scss-docs-end modal-fullscreen-loop\n", "// Base class\n.tooltip {\n  position: absolute;\n  z-index: $zindex-tooltip;\n  display: block;\n  margin: $tooltip-margin;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($tooltip-font-size);\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\n  word-wrap: break-word;\n  opacity: 0;\n\n  &.show { opacity: $tooltip-opacity; }\n\n  .tooltip-arrow {\n    position: absolute;\n    display: block;\n    width: $tooltip-arrow-width;\n    height: $tooltip-arrow-height;\n\n    &::before {\n      position: absolute;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-tooltip-top {\n  padding: $tooltip-arrow-height 0;\n\n  .tooltip-arrow {\n    bottom: 0;\n\n    &::before {\n      top: -1px;\n      border-width: $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\n      border-top-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-end {\n  padding: 0 $tooltip-arrow-height;\n\n  .tooltip-arrow {\n    left: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      right: -1px;\n      border-width: ($tooltip-arrow-width / 2) $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\n      border-right-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-bottom {\n  padding: $tooltip-arrow-height 0;\n\n  .tooltip-arrow {\n    top: 0;\n\n    &::before {\n      bottom: -1px;\n      border-width: 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\n      border-bottom-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-start {\n  padding: 0 $tooltip-arrow-height;\n\n  .tooltip-arrow {\n    right: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      left: -1px;\n      border-width: ($tooltip-arrow-width / 2) 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\n      border-left-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-tooltip-top;\n  }\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-tooltip-end;\n  }\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-tooltip-bottom;\n  }\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-tooltip-start;\n  }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: $tooltip-max-width;\n  padding: $tooltip-padding-y $tooltip-padding-x;\n  color: $tooltip-color;\n  text-align: center;\n  background-color: $tooltip-bg;\n  @include border-radius($tooltip-border-radius);\n}\n", "@mixin reset-text {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or overflow-wrap / word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", ".popover {\n  position: absolute;\n  top: 0;\n  left: 0 #{\"/* rtl:ignore */\"};\n  z-index: $zindex-popover;\n  display: block;\n  max-width: $popover-max-width;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($popover-font-size);\n  // Allow breaking very long words so they don't overflow the popover's bounds\n  word-wrap: break-word;\n  background-color: $popover-bg;\n  background-clip: padding-box;\n  border: $popover-border-width solid $popover-border-color;\n  @include border-radius($popover-border-radius);\n  @include box-shadow($popover-box-shadow);\n\n  .popover-arrow {\n    position: absolute;\n    display: block;\n    width: $popover-arrow-width;\n    height: $popover-arrow-height;\n\n    &::before,\n    &::after {\n      position: absolute;\n      display: block;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-popover-top {\n  > .popover-arrow {\n    bottom: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      bottom: 0;\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-top-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      bottom: $popover-border-width;\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-top-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-end {\n  > .popover-arrow {\n    left: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n\n    &::before {\n      left: 0;\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-right-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      left: $popover-border-width;\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-right-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-bottom {\n  > .popover-arrow {\n    top: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      top: 0;\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\n      border-bottom-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      top: $popover-border-width;\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\n      border-bottom-color: $popover-arrow-color;\n    }\n  }\n\n  // This will remove the popover-header's border just below the arrow\n  .popover-header::before {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    display: block;\n    width: $popover-arrow-width;\n    margin-left: -$popover-arrow-width / 2;\n    content: \"\";\n    border-bottom: $popover-border-width solid $popover-header-bg;\n  }\n}\n\n.bs-popover-start {\n  > .popover-arrow {\n    right: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n\n    &::before {\n      right: 0;\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\n      border-left-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      right: $popover-border-width;\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\n      border-left-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-popover-top;\n  }\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-popover-end;\n  }\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-popover-bottom;\n  }\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-popover-start;\n  }\n}\n\n// Offset the popover to account for the popover arrow\n.popover-header {\n  padding: $popover-header-padding-y $popover-header-padding-x;\n  margin-bottom: 0; // Reset the default from Reboot\n  @include font-size($font-size-base);\n  color: $popover-header-color;\n  background-color: $popover-header-bg;\n  border-bottom: $popover-border-width solid shade-color($popover-header-bg, 10%);\n  @include border-top-radius($popover-inner-border-radius);\n\n  &:empty {\n    display: none;\n  }\n}\n\n.popover-body {\n  padding: $popover-body-padding-y $popover-body-padding-x;\n  color: $popover-body-color;\n}\n", "// Notes on the classes:\n//\n// 1. .carousel.pointer-event should ideally be pan-y (to allow for users to scroll vertically)\n//    even when their scroll action started on a carousel, but for compatibility (with Firefox)\n//    we're preventing all actions instead\n// 2. The .carousel-item-start and .carousel-item-end is used to indicate where\n//    the active slide is heading.\n// 3. .active.carousel-item is the current slide.\n// 4. .active.carousel-item-start and .active.carousel-item-end is the current\n//    slide in its in-transition state. Only one of these occurs at a time.\n// 5. .carousel-item-next.carousel-item-start and .carousel-item-prev.carousel-item-end\n//    is the upcoming slide in transition.\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n  @include clearfix();\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  @include transition($carousel-transition);\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n/* rtl:begin:ignore */\n.carousel-item-next:not(.carousel-item-start),\n.active.carousel-item-end {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-end),\n.active.carousel-item-start {\n  transform: translateX(-100%);\n}\n\n/* rtl:end:ignore */\n\n\n//\n// Alternate transitions\n//\n\n.carousel-fade {\n  .carousel-item {\n    opacity: 0;\n    transition-property: opacity;\n    transform: none;\n  }\n\n  .carousel-item.active,\n  .carousel-item-next.carousel-item-start,\n  .carousel-item-prev.carousel-item-end {\n    z-index: 1;\n    opacity: 1;\n  }\n\n  .active.carousel-item-start,\n  .active.carousel-item-end {\n    z-index: 0;\n    opacity: 0;\n    @include transition(opacity 0s $carousel-transition-duration);\n  }\n}\n\n\n//\n// Left/right controls for nav\n//\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  // Use flex for alignment (1-3)\n  display: flex; // 1. allow flex styles\n  align-items: center; // 2. vertically center contents\n  justify-content: center; // 3. horizontally center contents\n  width: $carousel-control-width;\n  padding: 0;\n  color: $carousel-control-color;\n  text-align: center;\n  background: none;\n  border: 0;\n  opacity: $carousel-control-opacity;\n  @include transition($carousel-control-transition);\n\n  // Hover/focus state\n  &:hover,\n  &:focus {\n    color: $carousel-control-color;\n    text-decoration: none;\n    outline: 0;\n    opacity: $carousel-control-hover-opacity;\n  }\n}\n.carousel-control-prev {\n  left: 0;\n  background-image: if($enable-gradients, linear-gradient(90deg, rgba($black, .25), rgba($black, .001)), null);\n}\n.carousel-control-next {\n  right: 0;\n  background-image: if($enable-gradients, linear-gradient(270deg, rgba($black, .25), rgba($black, .001)), null);\n}\n\n// Icons for within\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: $carousel-control-icon-width;\n  height: $carousel-control-icon-width;\n  background-repeat: no-repeat;\n  background-position: 50%;\n  background-size: 100% 100%;\n}\n\n/* rtl:options: {\n  \"autoRename\": true,\n  \"stringMap\":[ {\n    \"name\"    : \"prev-next\",\n    \"search\"  : \"prev\",\n    \"replace\" : \"next\"\n  } ]\n} */\n.carousel-control-prev-icon {\n  background-image: escape-svg($carousel-control-prev-icon-bg);\n}\n.carousel-control-next-icon {\n  background-image: escape-svg($carousel-control-next-icon-bg);\n}\n\n// Optional indicator pips/controls\n//\n// Add a container (such as a list) with the following class and add an item (ideally a focusable control,\n// like a button) with data-bs-target for each slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 2;\n  display: flex;\n  justify-content: center;\n  padding: 0;\n  // Use the .carousel-control's width as margin so we don't overlay those\n  margin-right: $carousel-control-width;\n  margin-bottom: 1rem;\n  margin-left: $carousel-control-width;\n  list-style: none;\n\n  [data-bs-target] {\n    box-sizing: content-box;\n    flex: 0 1 auto;\n    width: $carousel-indicator-width;\n    height: $carousel-indicator-height;\n    padding: 0;\n    margin-right: $carousel-indicator-spacer;\n    margin-left: $carousel-indicator-spacer;\n    text-indent: -999px;\n    cursor: pointer;\n    background-color: $carousel-indicator-active-bg;\n    background-clip: padding-box;\n    border: 0;\n    // Use transparent borders to increase the hit area by 10px on top and bottom.\n    border-top: $carousel-indicator-hit-area-height solid transparent;\n    border-bottom: $carousel-indicator-hit-area-height solid transparent;\n    opacity: $carousel-indicator-opacity;\n    @include transition($carousel-indicator-transition);\n  }\n\n  .active {\n    opacity: $carousel-indicator-active-opacity;\n  }\n}\n\n\n// Optional captions\n//\n//\n\n.carousel-caption {\n  position: absolute;\n  right: (100% - $carousel-caption-width) / 2;\n  bottom: $carousel-caption-spacer;\n  left: (100% - $carousel-caption-width) / 2;\n  padding-top: $carousel-caption-padding-y;\n  padding-bottom: $carousel-caption-padding-y;\n  color: $carousel-caption-color;\n  text-align: center;\n}\n\n// Dark mode carousel\n\n.carousel-dark {\n  .carousel-control-prev-icon,\n  .carousel-control-next-icon {\n    filter: $carousel-dark-control-icon-filter;\n  }\n\n  .carousel-indicators [data-bs-target] {\n    background-color: $carousel-dark-indicator-active-bg;\n  }\n\n  .carousel-caption {\n    color: $carousel-dark-caption-color;\n  }\n}\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "//\n// Rotating border\n//\n\n// scss-docs-start spinner-border-keyframes\n@keyframes spinner-border {\n  to { transform: rotate(360deg) #{\"/* rtl:ignore */\"}; }\n}\n// scss-docs-end spinner-border-keyframes\n\n.spinner-border {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: $spinner-vertical-align;\n  border: $spinner-border-width solid currentColor;\n  border-right-color: transparent;\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 50%;\n  animation: $spinner-animation-speed linear infinite spinner-border;\n}\n\n.spinner-border-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n  border-width: $spinner-border-width-sm;\n}\n\n//\n// Growing circle\n//\n\n// scss-docs-start spinner-grow-keyframes\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n    transform: none;\n  }\n}\n// scss-docs-end spinner-grow-keyframes\n\n.spinner-grow {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: $spinner-vertical-align;\n  background-color: currentColor;\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 50%;\n  opacity: 0;\n  animation: $spinner-animation-speed linear infinite spinner-grow;\n}\n\n.spinner-grow-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n}\n\n@if $enable-reduced-motion {\n  @media (prefers-reduced-motion: reduce) {\n    .spinner-border,\n    .spinner-grow {\n      animation-duration: $spinner-animation-speed * 2;\n    }\n  }\n}\n", ".offcanvas {\n  position: fixed;\n  bottom: 0;\n  z-index: $zindex-offcanvas;\n  display: flex;\n  flex-direction: column;\n  max-width: 100%;\n  color: $offcanvas-color;\n  visibility: hidden;\n  background-color: $offcanvas-bg-color;\n  background-clip: padding-box;\n  outline: 0;\n  @include box-shadow($offcanvas-box-shadow);\n  @include transition(transform $offcanvas-transition-duration ease-in-out);\n}\n\n.offcanvas-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: $offcanvas-padding-y $offcanvas-padding-x;\n\n  .btn-close {\n    padding: ($offcanvas-padding-y / 2) ($offcanvas-padding-x / 2);\n    margin: ($offcanvas-padding-y / -2) ($offcanvas-padding-x / -2) ($offcanvas-padding-y / -2) auto;\n  }\n}\n\n.offcanvas-title {\n  margin-bottom: 0;\n  line-height: $offcanvas-title-line-height;\n}\n\n.offcanvas-body {\n  flex-grow: 1;\n  padding: $offcanvas-padding-y $offcanvas-padding-x;\n  overflow-y: auto;\n}\n\n.offcanvas-start {\n  top: 0;\n  left: 0;\n  width: $offcanvas-horizontal-width;\n  border-right: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateX(-100%);\n}\n\n.offcanvas-end {\n  top: 0;\n  right: 0;\n  width: $offcanvas-horizontal-width;\n  border-left: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateX(100%);\n}\n\n.offcanvas-top {\n  top: 0;\n  right: 0;\n  left: 0;\n  height: $offcanvas-vertical-height;\n  max-height: 100%;\n  border-bottom: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateY(-100%);\n}\n\n.offcanvas-bottom {\n  right: 0;\n  left: 0;\n  height: $offcanvas-vertical-height;\n  max-height: 100%;\n  border-top: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateY(100%);\n}\n\n.offcanvas.show {\n  transform: none;\n}\n", "@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: $value;\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n      }\n    }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$variable-prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$variable-prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://hugogiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n      .#{$property-class + $infix + $property-class-modifier} {\n        @each $property in $properties {\n          #{$property}: $value if($enable-important-utilities, !important, null);\n        }\n      }\n\n      @each $pseudo in $state {\n        .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n          @each $property in $properties {\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n      }\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n", "//\n// background.scss\n// Extended from Bootstrap\n//\n\n.bg-cover {\n  background: no-repeat center center / cover;\n}\n\n\n// Between\n\n.bg-between {\n\n  @include media-breakpoint-down(lg) {\n    background-image: none !important;\n  }\n\n  @include media-breakpoint-up(lg) {\n    background-repeat: no-repeat;\n    background-position: left center, right center;\n    background-size: auto 70%;\n  }\n}\n\n\n// Gradients\n\n.bg-gradient-light-white {\n  background-image: linear-gradient(to bottom, $light 0%, $white 100%);\n}\n\n.bg-gradient-dark-black {\n  background-image: linear-gradient(to bottom, $dark 0%, $black 100%);\n}\n\n.bg-gradient-multicolor {\n  background-image: conic-gradient(from 52deg at 50% 50%,\n      #8024A0 0deg,\n      #31EEC1 33deg,\n      #6924A0 222deg,\n      #D6723B 264deg,\n      #D5498C 295deg,\n      #8024A0 360deg);\n}\n\n\n// Patterns\n\n.bg-pattern-2 {\n  background-image: linear-gradient(to bottom, transparent 0%, $black 100%), url('#{$path-to-img}/patterns/pattern-2.png');\n}\n", "//\n// images.scss\n// Extended from Bootstrap\n//\n\n//\n// Skewed\n//\n\n.img-skewed {\n  perspective: 1500px;\n  transform-style: preserve-3d;\n}\n\n// Left\n\n.img-skewed-start {\n  perspective-origin: left center;\n\n  .img-skewed-item {\n    transform: rotateY(-$img-skewed-rotate-y) rotateX($img-skewed-rotate-x);\n    backface-visibility: hidden;\n  }\n}\n\n// Right\n\n.img-skewed-end {\n  perspective-origin: right center;\n\n  .img-skewed-item {\n    transform: rotateY($img-skewed-rotate-y) rotateX($img-skewed-rotate-x);\n    backface-visibility: hidden;\n  }\n}", "//\n// lift.scss\n// Theme utility\n//\n\n.lift {\n  transition: box-shadow .25s ease, transform .25s ease;\n}\n\n.lift:hover,\n.lift:focus {\n  box-shadow: $box-shadow-lift !important;\n  transform: translate3d(0, -3px, 0);\n}\n\n.lift-lg:hover,\n.lift-lg:focus {\n  box-shadow: $box-shadow-lift-lg !important;\n  transform: translate3d(0, -5px, 0);\n}\n", "//\n// overlay.scss\n// Theme utility\n//\n\n.overlay {\n  position: relative;\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: 0; right: 0; bottom: 0; left: 0;\n    border-radius: inherit;\n  }\n}\n\n\n// Colors\n\n@each $color, $value in $theme-colors {\n\n  // Solid\n  .overlay-#{$color} {\n    @include overlay($value);\n  }\n\n  // Gradient\n  .overlay-gradient-#{$color}-end {\n    @include overlay-gradient($value, right);\n  }\n  .overlay-gradient-#{$color}-down {\n    @include overlay-gradient($value, bottom);\n  }\n  .overlay-gradient-#{$color}-start {\n    @include overlay-gradient($value, left);\n  }\n}\n\n\n// Opacity\n\n@for $i from 1 through 9 {\n  .overlay-#{$i * 10} {\n\n    &::before {\n      opacity: #{$i / 10};\n    }\n  }\n}", "//\n// overlay.scss\n// Theme mixin\n//\n\n@mixin overlay($color) {\n  \n  &::before {\n    background-color: $color;\n  }\n}\n\n@mixin overlay-gradient($color, $direction) {\n  \n  &::before {\n    background-image: linear-gradient(to bottom, transparent, $color);\n\n    @if($direction == \"right\" OR $direction == \"left\") {\n      @include media-breakpoint-up(lg) {\n        background-image: linear-gradient(to $direction, transparent, $color 50%, $color);\n      }\n    }\n  }\n}", "//\n// position.scss\n// Extended from Bootstrap\n//\n\n.center {\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n", "//\n// shadows.scss\n// Extended from Bootstrap\n//\n\n.shadow-multicolor {\n  box-shadow: -300px 100px 300px rgba(#501AE8, .2), 300px -100px 300px rgba(#24A1FC, .2);\n}", "//\n// sizing.scss\n// Extended from Bootstrap\n//\n\n// Cover\n//\n// Spans the container till the edge of the viewport\n\n.w-cover {\n\n  @each $width, $value in $container-max-widths {\n\n    @include media-breakpoint-up(#{$width}) {\n      width: calc(100% + (100vw - #{$value}) / 2 + #{$grid-gutter-width});\n    }\n  }\n}\n", "//\n// type.scss\n// Extended from Bootstrap\n//\n\n//\n// Font sizing\n//\n\n@include media-breakpoint-down(lg) {\n  .font-size-lg {\n    font-size: $font-size-lg-md !important;\n  }\n}\n", "//\n// reboot.scss\n// Extended from Bootstrap\n//\n\nhtml {\n  overflow-x: hidden;\n}\n\n@media (hover: none) {\n  body {\n    overflow-x: hidden;\n  }\n}", "//\n// type.scss\n// Extended from Bootstrap\n//\n\n// Font\n\n@font-face {\n  font-family: \"Poppins\";\n  font-weight: 400;\n  src: url(\"#{$path-to-fonts}/HK%20Grotesk%20Pro/Poppins-Regular.woff2\") format(\"woff2\"),\n  url(\"#{$path-to-fonts}/HK%20Grotesk%20Pro/Poppins-Regular.woff\") format(\"woff\");\n}\n\n@font-face {\n  font-family: \"Poppins\";\n  font-weight: 600;\n  src: url(\"#{$path-to-fonts}/HK%20Grotesk%20Pro/Poppins-Medium.woff2\") format(\"woff2\"),\n  url(\"#{$path-to-fonts}/HK%20Grotesk%20Pro/Poppins-Medium.woff\") format(\"woff\");\n}\n\n@font-face {\n  font-family: \"Poppins\";\n  font-weight: 700;\n  src: url(\"#{$path-to-fonts}/HK%20Grotesk%20Pro/Poppins-Bold.woff2\") format(\"woff2\"),\n  url(\"#{$path-to-fonts}/HK%20Grotesk%20Pro/Poppins-Bold.woff\") format(\"woff\");\n}\n\n\n// Feather icons\n\n@font-face {\n  font-family: 'Feather';\n  src: url('#{$path-to-fonts}/Feather/Feather.ttf?sdxovp') format('truetype'), url('#{$path-to-fonts}/Feather/Feather.woff?sdxovp') format('woff'), url('#{$path-to-fonts}/Feather/Feather.svg?sdxovp#Feather') format('svg');\n  font-weight: 400;\n  font-style: normal;\n}\n\n\n// Headings\n\nh1, h2, .h1, .h2 {\n  margin-bottom: 1rem;\n}\n\nh1, h2, h3, h4, .h1, .h2, .h3, .h4 {\n  letter-spacing: $headings-letter-spacing;\n}\n\nh1, .h1 {\n  line-height: $h1-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $h1-font-size-md;\n  }\n}\n\nh2, .h2 {\n  line-height: $h2-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $h2-font-size-md;\n  }\n}\n\nh3, .h3 {\n  line-height: $h3-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $h3-font-size-md;\n  }\n}\n\nh4, .h4 {\n  line-height: $h4-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $h4-font-size-md;\n  }\n}\n\nh5, .h5 {\n  line-height: $h5-line-height;\n}\n\nh6, .h6 {\n  line-height: $h6-line-height;\n\n  &.text-uppercase {\n    letter-spacing: .08em;\n  }\n}\n\n\n// Display\n\n.display-1, .display-2, .display-3, .display-4 {\n  letter-spacing: $display-letter-spacing;\n}\n\n.display-1 {\n  line-height: $display-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $display1-font-size-md;\n  }\n}\n\n.display-2 {\n  line-height: $display-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $display2-font-size-md;\n  }\n}\n\n.display-3 {\n  line-height: $display-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $display3-font-size-md;\n  }\n}\n\n.display-4 {\n  line-height: $display-line-height;\n\n  @include media-breakpoint-down(lg) {\n    font-size: $display4-font-size-md;\n  }\n}\n\n\n// Lead\n\n.lead {\n\n  @include media-breakpoint-down(lg) {\n    font-size: $lead-font-size-md;\n  }\n}\n\n\n// Blockquote\n\n.blockquote-img {\n  max-height: 2.5rem;\n  max-width: 100%;\n  width: auto;\n}\n\n.blockquote-footer {\n  margin-top: 0;\n\n  &::before {\n    display: none;\n  }\n}\n\n\n// Social list\n\n.list-social-icon {\n  max-width: 1.25rem;\n  height: auto;\n}\n\n\n// Dividers\n\n.hr-sm {\n  width: 60px;\n}\n\n.hr-sm:not([size]) {\n  height: $border-width * 2;\n}\n\n.hr-md {\n  width: 100px;\n  ;\n}\n\n.hr-md:not([size]) {\n  height: $border-width * 2;\n}\n\n\n// Code\n\ncode {\n  line-height: $code-line-height;\n}\n", "//\n// images.scss\n// Extended from Bootstrap\n//\n\n// Image cover\n//\n// Spans the whole with of the viewport\n\n.img-cover {\n  width: 100vw;\n  height: auto;\n  margin-left: calc(50% - 50vw);\n}\n\n// Extending .img-fluid class to support nested images\n\n.img-fluid > img,\n.img-fluid > svg {\n  max-width: inherit;\n  height: inherit;\n}\n\n// Figure\n\n.figure {\n  display: block;\n}\n\n.figure-img {\n  margin-bottom: $spacer;\n}\n", "//\n// grid.scsss\n// Extended from Bootstrap\n//\n\n[class^=\"container\"] {\n  position: relative;\n  z-index: 1;\n  padding-right: $grid-gutter-width / 2;\n  padding-left: $grid-gutter-width / 2;\n}\n\n[class^=\"col\"] {\n  position: relative;\n}\n", "//\n// tables.scss\n// Extended from Bootstrap\n//\n\n// Alignment\n\n.table-align-middle {\n\n  thead > tr > th,\n  tbody > tr > td {\n    vertical-align: middle;\n  }\n}\n", "// \n// floating-labels.scss\n// Extended from Bootstrap\n// \n\n.form-floating > label {\n  color: $form-floating-label-color;\n}\n\n.form-floating > .form-control:focus ~ label,\n.form-floating > .form-control:not(:placeholder-shown) ~ label,\n.form-floating > .form-select ~ label {\n  font-size: $form-floating-label-font-size;\n}", "// \n// form-check.scss\n// Extended from Bootstrap\n// \n\n//\n// Check\n//\n\n.form-check {\n  margin-bottom: 0;\n}\n\n.form-check + .form-check {\n  margin-top: $form-check-margin-bottom;\n}\n\n// Dark\n\n.form-check-dark .form-check-input {\n  background-color: $form-check-input-bg-dark;\n}\n\n.form-check-dark .form-check-input:checked {\n  background-color: $form-check-input-checked-bg-color-dark;\n}\n\n\n//\n// Switch\n//\n\n.form-switch {\n  min-height: $form-switch-min-height;\n}\n\n.form-switch > .form-check-input {\n  height: $form-switch-min-height;\n}\n\n.form-switch > .form-check-label {\n  margin-top: calc((#{$form-switch-min-height} - #{$line-height-base} * 1em) / 2);\n}\n\n// Dark\n\n.form-check-dark.form-switch .form-check-input {\n  background-image: escape-svg($form-switch-bg-image-dark);\n}\n\n.form-check-dark.form-switch .form-check-input:focus {\n  background-image: escape-svg($form-switch-focus-bg-image-dark);\n}\n\n.form-check-dark.form-switch .form-check-input:checked {\n  background-image: escape-svg($form-switch-checked-bg-image-dark);\n}\n", "//\n// form-control.scss\n// Extended from Bootstrap\n//\n\n// Sizing\n\n.form-control-xs {\n  min-height: $input-height-xs;\n  padding: $input-padding-y-xs $input-padding-x-xs;\n  line-height: $input-line-height-xs;\n  font-size: $input-font-size-xs;\n}\n\n\n// Form control flush\n\n.form-control-flush,\n.form-floating > .form-control-flush {\n  padding-left: 0;\n  padding-right: 0;\n  border-top-width: 0;\n  border-right-width: 0;\n  border-left-width: 0;\n  border-radius: 0;\n}\n\n.form-floating > .form-control-flush ~ label {\n  padding-left: 0;\n  padding-right: 0;\n  border-width: 0;\n}\n", "//\n// form-group.scss\n// Theme component\n//\n\n.form-group {\n  margin-bottom: $form-group-margin-bottom;\n}\n", "// \n// form-select.scss\n// Extended from Bootstrap\n// \n\n// Sizes\n\n.form-select-xs {\n  height: $form-select-height-xs;\n  padding: $form-select-padding-y-xs calc(#{$form-select-padding-x-xs} + #{$form-select-indicator-padding-xs} + #{$form-select-bg-size-xs}) $form-select-padding-y-xs $form-select-padding-x-xs;\n  line-height: $form-select-line-height-xs;\n  font-size: $form-select-font-size-xs;\n  background-size: $form-select-bg-size-xs;\n  background-position: right $form-select-padding-x-xs center;\n}\n", "//\n// input-group.scss\n//\n\n// Sizing\n\n.input-group-xs .form-control {\n  min-height: $input-height-xs;\n  padding: $input-padding-y-xs $input-padding-x-xs;\n  line-height: $input-line-height-xs;\n  font-size: $input-font-size-xs;\n}\n\n.input-group-xs .input-group-text {\n  padding: $input-padding-y-xs $input-padding-x-xs;\n  line-height: $input-line-height-xs;\n  font-size: $input-btn-font-size-xs;\n}\n", "//\n// buttons.scss\n// Extended from Bootstrap\n//\n\n.btn > img {\n  max-height: 1em;\n  width: auto;\n}\n\n.btn:not([class*=\"btn-outline\"]) {\n  border-color: transparent !important;\n}\n\n\n// Sizing\n\n.btn-xs {\n  padding: $input-btn-padding-y-xs $input-btn-padding-x-xs;\n  font-size: $input-btn-font-size-xs;\n}\n\n\n// Custom colors\n\n.btn-white {\n  background-color: $white;\n  color: $primary;\n\n  &:hover, &:focus {\n    background-color: $white;\n    color: darken($primary, 5%);\n  }\n\n  &.active {\n    background-color: darken($white, 5%);\n    color: darken($primary, 5%);\n  }\n}\n\n.btn-group .btn-white {\n  border-color: $gray-300 !important;\n  color: $text-muted;\n\n  &.active {\n    background-color: darken($white, 5%);\n    color: darken($text-muted, 5%);\n    box-shadow: none;\n  }\n}\n\n.btn-gray-300-20 {\n  background-color: rgba($gray-300, .2);\n  color: $white;\n\n  &:hover, &:focus {\n    background-color: rgba($gray-300, .25);\n    color: $white;\n  }\n\n  &.active {\n    background-color: rgba($gray-300, .25);\n    color: $white;\n  }\n}\n\n.btn-gray-400 {\n  background-color: $gray-400;\n  color: $white;\n\n  &:hover, &:focus {\n    background-color: darken($gray-400, 5%);\n    color: darken($white, 5%);\n  }\n\n  &.active {\n    background-color: darken($gray-400, 5%);\n    color: darken($white, 5%);\n  }\n}\n\n.btn-gray-400-10 {\n  background-color: rgba($gray-400, .1);\n  color: $white;\n\n  &:hover, &:focus {\n    background-color: rgba($gray-400, .15);\n    color: $white;\n  }\n\n  &.active {\n    background-color: rgba($gray-400, .15);\n    color: $white;\n  }\n}\n\n.btn-outline-white {\n  border-color: $gray-300;\n  color: $body-color;\n\n  &:hover, &:focus {\n    background-color: darken($white, 5%);\n    border-color: $gray-400;\n    color: $body-color;\n  }\n\n  &.active {\n    background-color: darken($white, 5%);\n    border-color: $gray-400;\n    color: $body-color;\n  }\n}\n\n.btn-outline-gray-300 {\n  border-color: $gray-300;\n  color: $primary;\n\n  &:hover, &:focus {\n    background-color: $primary;\n    border-color: $primary;\n    color: $white;\n  }\n\n  &.active {\n    background-color: $primary;\n    border-color: $primary;\n    color: $white;\n  }\n}\n\n\n// Soft variants\n\n@each $color, $value in $theme-colors {\n  .btn-#{$color}-soft {\n    @include btn-variant-soft(rgba($value, $btn-soft-bg-opacity), $value);\n  }\n}\n\n\n// Pill\n\n.btn-pill {\n  border-radius: $border-radius-pill;\n}\n\n\n// Rounded circle\n\n.btn-rounded-circle {\n  padding-left: $btn-padding-y;\n  padding-right: $btn-padding-y;\n  border-radius: $border-radius-pill;\n}\n\n.btn-rounded-circle > * {\n  display: block;\n  line-height: 1;\n  width: 1em;\n}\n\n.btn-rounded-circle.btn-lg {\n  padding-left: $btn-padding-y-lg;\n  padding-right: $btn-padding-y-lg;\n}\n\n.btn-rounded-circle.btn-sm {\n  padding-left: $btn-padding-y-sm;\n  padding-right: $btn-padding-y-sm;\n}\n\n.btn-rounded-circle.btn-xs {\n  padding-left: $btn-padding-y-xs;\n  padding-right: $btn-padding-y-xs;\n}\n", "//\n// buttons.scss\n// Extended from Bootstrap\n//\n\n@mixin btn-variant-soft($bg, $color) {\n  background-color: $bg;\n  color: $color;\n\n  &:hover, &:focus {\n    background-color: fade-in($bg, .05);\n    color: $color;\n  }\n\n  &.active {\n    background-color: fade-in($bg, .05);\n    color: $color;\n  }\n}\n", "//\n// dropdown.scss\n// Extended from Bootstrap\n//\n\n.dropdown-menu {\n  padding-left: $dropdown-padding-x;\n  padding-right: $dropdown-padding-x;\n}\n\n\n// Item\n\n.dropdown-item {\n  width: auto;\n  min-width: 100%;\n  margin-left: -$dropdown-padding-x;\n  margin-right: -$dropdown-padding-x;\n\n  &:hover, &:focus {\n    outline: none;\n  }\n}\n\n.dropdown-item + .dropdown-item {\n  margin-top: $dropdown-item-spacing-y;\n}\n\n\n// Link (optional)\n\n.dropdown-link {\n  color: inherit;\n\n  &:hover {\n    text-decoration: inherit;\n  }\n\n  &:hover, &:focus {\n    outline: none;\n  }\n}\n\n\n// Header\n\n.dropdown-header {\n  margin-left: -$dropdown-padding-x;\n  margin-right: -$dropdown-padding-x;\n  padding-top: 0;\n  padding-bottom: $headings-margin-bottom;\n  font-size: $h6-font-size;\n  font-weight: $font-weight-bold;\n  line-height: $h6-line-height;\n  text-transform: uppercase;\n  letter-spacing: .08em;\n}\n\n\n// Toggle\n\n.dropdown-toggle::after {\n  font-family: \"Feather\";\n  vertical-align: middle;\n  border: none !important;\n  content: \"\\e92e\";\n}\n\n.dropend > .dropdown-toggle::after {\n  content: \"\\e930\";\n}\n\n\n// Sizing\n\n.dropdown-menu-xs {\n  min-width: $dropdown-min-width-xs;\n  padding: $dropdown-padding-y-xs $dropdown-padding-x-xs;\n}\n\n.dropdown-menu-md {\n  min-width: $dropdown-min-width-md;\n}\n\n.dropdown-menu-lg {\n  min-width: $dropdown-min-width-lg;\n}\n\n.dropdown-menu-xl {\n  min-width: $dropdown-min-width-xl;\n}\n\n.dropdown-menu-lg,\n.dropdown-menu-xl {\n  padding: $dropdown-padding-y-lg $dropdown-padding-x-lg;\n}\n\n.dropdown-menu-lg .dropdown-item,\n.dropdown-menu-xl .dropdown-header {\n  margin-left: -$dropdown-padding-x-lg;\n  margin-right: -$dropdown-padding-x-lg;\n  padding-left: $dropdown-item-padding-x-lg;\n  padding-right: $dropdown-item-padding-x-lg;\n}\n\n\n// Positioning\n\n.dropend > .dropdown-menu {\n  top: -$dropdown-padding-y;\n}\n", "//\n// navbar.scss\n// Extended from Bootstrap\n//\n\n.navbar {\n  transition: $transition-base;\n  transition-property: background-color, color;\n  z-index: $zindex-fixed;\n}\n\n\n// Dark version\n\n.navbar-dark {\n\n  @include media-breakpoint-down(lg) {\n\n    .navbar-nav .nav-item .nav-link {\n      color: $navbar-light-color;\n\n      &:hover, &:focus {\n        color: $navbar-light-hover-color;\n      }\n    }\n\n    .navbar-nav .nav-item.active .nav-link,\n    .navbar-nav .nav-item .nav-link.active {\n      color: $navbar-light-active-color;\n    }\n\n    .navbar-collapse .navbar-toggler {\n      color: $navbar-light-color;\n    }\n  }\n}\n\n\n// Contaner\n\n.navbar > .container,\n.navbar > .container-fluid {\n  padding-left: $grid-gutter-width / 2 !important;\n  padding-right: $grid-gutter-width / 2 !important;\n}\n\n\n// Brand\n\n.navbar-brand {\n  font-weight: $navbar-brand-font-weight;\n}\n\n.navbar-brand-img {\n  max-height: $navbar-brand-height;\n  width: auto;\n}\n\n.navbar-dark .navbar-brand {\n  filter: brightness(0) invert(1);\n}\n\n\n// Button\n\n.navbar-btn {\n\n  @include media-breakpoint-down(lg) {\n    width: 100%;\n    padding: $btn-padding-y $btn-padding-x;\n    border-top-left-radius: 0;\n    border-top-right-radius: 0;\n    font-size: $btn-font-size;\n  }\n}\n\n\n// Navigation\n\n.navbar-nav .nav-link {\n  font-weight: $navbar-nav-link-font-weight;\n\n  &:hover, &:focus {\n    outline: none;\n  }\n}\n\n\n// Boxed layout\n\n.navbar-dark.fixed-top > .container {\n  position: relative;\n\n  &::after {\n    content: \"\";\n    position: absolute;\n    right: 0;\n    bottom: -$navbar-padding-y;\n    left: 0;\n    border-top: $border-width solid fade-out($white, .8);\n  }\n}\n\n\n// Collapse\n\n@include media-breakpoint-down(lg) {\n\n  .navbar-collapse {\n    position: fixed;\n    top: $spacer;\n    left: $spacer;\n    height: auto;\n    max-height: calc(100% - #{$spacer * 2}) !important;\n    width: calc(100% - #{$spacer * 2});\n    background-color: $dropdown-bg;\n    border-radius: $dropdown-border-radius;\n    box-shadow: $box-shadow-dark-lg;\n    overflow-x: hidden;\n    overflow-y: scroll;\n  }\n\n  // Animation\n\n  .navbar-collapse.show,\n  .navbar-collapse.collapsing {\n    transition: $transition-base;\n    transition-property: opacity, transform, -webkit-transform;\n    transform-origin: top right;\n  }\n\n  .navbar-collapse.show {\n    opacity: 1;\n    transform: scale(1);\n  }\n\n  .navbar-collapse.collapsing {\n    opacity: 0;\n    transform: scale(.9);\n  }\n\n}\n\n\n// Toggler\n\n.navbar .navbar-toggler {\n  margin-left: auto;\n}\n\n.navbar-collapse .navbar-toggler {\n  position: absolute;\n  top: $spacer;\n  right: $spacer;\n  z-index: 1;\n\n  @include media-breakpoint-up(lg) {\n    display: none;\n  }\n}\n\n\n// Navigation\n\n.navbar-collapse .navbar-nav .nav-item {\n\n  @include media-breakpoint-down(lg) {\n    padding: $navbar-nav-item-spacing;\n\n    + .nav-item {\n      border-top: $border-width solid $gray-200;\n    }\n  }\n}\n\n.navbar-collapse .navbar-nav .nav-link {\n\n  @include media-breakpoint-down(lg) {\n    padding-top: 0;\n    padding-bottom: 0;\n  }\n}\n\n\n// Dropdown menu\n\n.navbar-nav .dropdown-menu {\n  box-shadow: none;\n\n  @include media-breakpoint-up(lg) {\n    box-shadow: $box-shadow-dark-lg;\n  }\n}\n\n.navbar-collapse .navbar-nav .dropdown-menu {\n\n  @include media-breakpoint-down(lg) {\n    min-width: 0;\n    padding: 0;\n  }\n}\n\n.navbar-collapse .navbar-nav .dropdown > .dropdown-menu {\n\n  @include media-breakpoint-down(lg) {\n    display: block !important;\n  }\n}\n\n.navbar-collapse .navbar-nav .dropend > .dropdown-menu {\n\n  @include media-breakpoint-down(lg) {\n    padding-top: $spacer;\n    padding-bottom: $spacer;\n    padding-left: $spacer / 2;\n  }\n\n  @include media-breakpoint-up(lg) {\n    left: 100%;\n    right: auto;\n  }\n}\n\n\n// Dropdown item\n\n.navbar-collapse .navbar-nav .dropdown-item,\n.navbar-collapse .navbar-nav .dropdown-header {\n\n  @include media-breakpoint-down(lg) {\n    margin-left: 0;\n    margin-right: 0;\n    padding-left: 0;\n    padding-right: 0;\n  }\n}\n\n\n// Dropdown toggle\n\n.navbar-nav .dropdown > .dropdown-toggle {\n\n  &::after {\n    display: none;\n  }\n}\n\n.navbar-collapse .navbar-nav .dropdown > .dropdown-toggle {\n\n  @include media-breakpoint-down(lg) {\n    margin-bottom: $navbar-dropdown-toggle-margin-bottom;\n    pointer-events: none;\n  }\n}\n\n.navbar-nav .dropend > .dropdown-toggle {\n  display: flex;\n\n  &::after {\n    margin-left: auto;\n  }\n}\n\n.navbar-collapse .navbar-nav .dropend > .dropdown-toggle {\n\n  @include media-breakpoint-down(lg) {\n\n    &::after {\n      content: \"\\e92e\";\n    }\n\n    &[aria-expanded=\"true\"]::after {\n      transform-origin: center center;\n      transform: rotate(180deg);\n    }\n  }\n}\n\n\n// Dropdown image\n\n.navbar-nav .dropdown-img-start {\n  height: 100%;\n  display: none;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: $dropdown-padding-y $dropdown-padding-x;\n  background: no-repeat center center / cover;\n  border-top-left-radius: $border-radius;\n  border-bottom-left-radius: $border-radius;\n\n  @include media-breakpoint-up(lg) {\n    display: flex;\n  }\n\n  // Overlay\n\n  &::before {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    background-color: fade-out($primary, .2);\n    border-radius: inherit;\n  }\n\n  // Content\n\n  * {\n    position: relative;\n  }\n}\n\n.navbar-nav .dropdown-menu-lg .dropdown-img-start,\n.navbar-nav .dropdown-menu-xl .dropdown-img-start {\n\n  @include media-breakpoint-up(lg) {\n    padding: $dropdown-padding-y-lg $dropdown-padding-x-lg;\n  }\n}\n\n\n// Dropdown body\n\n.navbar-nav .dropdown-body {\n\n  @include media-breakpoint-up(lg) {\n    padding: $dropdown-padding-y $dropdown-padding-x;\n  }\n}\n\n.navbar-nav .dropdown-menu-lg .dropdown-body,\n.navbar-nav .dropdown-menu-xl .dropdown-body {\n\n  @include media-breakpoint-up(lg) {\n    padding: $dropdown-padding-y-lg $dropdown-padding-x-lg;\n  }\n}\n\n// Dropdown list group\n\n.navbar-nav .dropdown-menu .list-group-item {\n  display: flex;\n  align-items: center;\n  padding-top: $spacer;\n  padding-bottom: $spacer;\n  color: inherit;\n\n  &:hover {\n    text-decoration: none;\n  }\n}\n\n\n// Toggle dropdown on hover\n\n@include media-breakpoint-up(lg) {\n\n  .navbar-nav .dropdown-menu {\n    display: none;\n    opacity: 0;\n    transition: $transition-base;\n    transition-property: opacity, transform, -webkit-transform;\n  }\n\n  .navbar-nav .dropend > .dropdown-menu {\n    transform: translateY(10px);\n  }\n\n  .navbar-nav .dropdown > .dropdown-menu {\n    left: 50%;\n    transform: translate(-50%, 10px);\n  }\n\n  .navbar-nav .dropdown-menu.showing {\n    display: block;\n  }\n\n  .navbar-nav .dropdown-menu.show {\n    display: block;\n    opacity: 1;\n  }\n\n  .navbar-nav .dropend > .dropdown-menu.show {\n    transform: translateY(0);\n  }\n\n  .navbar-nav .dropdown > .dropdown-menu.show {\n    transform: translate(-50%, 0);\n  }\n}\n", "//\n// card.scss\n// Extended from Bootstrap\n//\n\n// Card resets\n\n.card {\n  position: relative;\n  width: 100%;\n}\n\n.card-header {\n  background-color: transparent;\n  border-bottom: $border-width solid $border-color;\n}\n\n.card-body {\n  display: block;\n  flex-grow: 0;\n}\n\n.card-footer {\n  background-color: unset;\n}\n\n.card-body, .card-footer, .card-meta, .card-img, .card-img-top, .card-img-start, .card-img-end {\n  position: relative;\n  min-height: 1px;\n}\n\n\n// Card action\n\na.card, a.card-body, a.card-footer, a.card-meta {\n  color: inherit;\n\n  &:hover {\n    text-decoration: none;\n  }\n}\n\n\n// Card flush\n\n.card-flush {\n  background-color: unset;\n}\n\n.card-flush > *:not(.card-btn) {\n  padding-left: 0;\n  padding-right: 0;\n}\n\n\n// Card border\n\n.card-border {\n  position: relative;\n}\n\n.card-border::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 100%;\n  border-top-width: $border-width * 2;\n  border-bottom-width: calc(#{$card-border-radius} - #{$border-width * 2});\n  border-top-style: solid;\n  border-bottom-style: solid;\n  border-top-color: inherit;\n  border-bottom-color: transparent;\n  border-top-left-radius: $card-border-radius;\n  border-top-right-radius: $card-border-radius;\n}\n\n.card-border-lg::after {\n  border-top-width: $border-width * 3;\n  border-bottom-width: calc(#{$card-border-radius} - #{$border-width * 3});\n}\n\n.card-border-xl::after {\n  border-top-width: $border-width * 4;\n  border-bottom-width: calc(#{$card-border-radius} - #{$border-width * 3});\n}\n\n\n// Card row\n\n.card-row {\n\n  .card-body {\n\n    @include media-breakpoint-up(md) {\n      padding: $card-row-spacer-y $card-row-spacer-x;\n    }\n  }\n\n  .card-meta {\n\n    @include media-breakpoint-up(md) {\n      padding-left: $card-row-spacer-x;\n      padding-right: $card-row-spacer-x;\n    }\n  }\n}\n\n\n// Card image\n\n.card-img-end {\n  border-radius: $border-radius $border-radius 0 0;\n\n  @include media-breakpoint-up(md) {\n    border-radius: 0 $border-radius $border-radius 0;\n  }\n}\n\n.card-img-start {\n  border-radius: $border-radius $border-radius 0 0;\n\n  @include media-breakpoint-up(md) {\n    border-radius: $border-radius 0 0 $border-radius;\n  }\n}\n\n\n// Card image slider\n\n.card-img-slider {\n\n  @include media-breakpoint-up(md) {\n    height: 100%;\n    width: 100%;\n\n    * {\n      height: inherit !important;\n      width: inherit !important;\n    }\n  }\n}\n\n\n// Card image overlay\n\n.card-img-overlay {\n  display: flex;\n  flex-direction: column;\n  padding: $card-spacer-x;\n}\n\n.card-img-overlay .card-body {\n  margin: -$card-spacer-x;\n  margin-top: auto;\n  border-bottom-right-radius: $card-inner-border-radius;\n  border-bottom-left-radius: $card-inner-border-radius;\n}\n\n\n.card-img-overlay-hover {\n  overflow: hidden;\n}\n\n.card-img-overlay-hover .card-body {\n  transform: translateY(110%);\n  transition: all .3s ease;\n}\n\n.card-img-overlay-hover:hover .card-body {\n  transform: translateY(0);\n}\n\n\n\n// Card group\n\n.card-group {\n\n  @include media-breakpoint-only(sm) {\n    flex-direction: column !important;\n\n    > .card {\n      flex: auto;\n    }\n  }\n}\n\n\n// Card meta\n\n.card-meta {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  padding: 0 $card-spacer-x $card-meta-spacer-y;\n}\n\n.card-meta-divider {\n  width: 100%;\n  margin: 0 0 $card-meta-spacer-y;\n}\n\n\n// Card button\n\n.card-btn:last-child {\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n}\n\n\n// Zoom\n\n.card-zoom {\n  overflow: hidden !important;\n  border-radius: inherit;\n}\n\n.card-zoom > [class*=\"card-img\"] {\n  transition: all .3s ease;\n  transform-origin: center center;\n}\n\n.card:hover > .card-zoom > [class*=\"card-img\"] {\n  transform: scale(1.1);\n}\n\n\n// Card list\n\n.card-list .list-link {\n  position: relative;\n}\n\n.card-list .list-link::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: -$card-spacer-x;\n  border-right: $border-width * 2 solid $primary;\n  display: none;\n}\n\n.card-list .list-link:hover {\n  color: $body-color !important;\n}\n\n.card-list .active .list-link {\n  color: $body-color !important;\n}\n\n.card-list .active .list-link::before {\n  display: block;\n}\n\n\n// Card bleed\n\n@include media-breakpoint-down(md) {\n\n  .card-bleed {\n    width: auto;\n    min-width: 100%;\n    margin-right: -$grid-gutter-width / 2;\n    margin-left: -$grid-gutter-width / 2;\n    border-radius: 0;\n  }\n\n  .card-bleed .card-footer,\n  .card-bleed .card-header,\n  .card-bleed .card-body {\n    padding-right: $grid-gutter-width / 2;\n    padding-left: $grid-gutter-width / 2;\n    border-radius: 0;\n  }\n\n  .card-bleed .card-list .list-link::before {\n    left: -$grid-gutter-width / 2;\n  }\n}\n", "//\n// accordion.scss\n// Extended from Bootstrap\n//\n\n// Button\n\n.accordion-button:not(.collapsed) {\n  box-shadow: none;\n}\n\n.accordion-button:not([data-bs-toggle=\"collapse\"])::after {\n  display: none;\n}\n\n.accordion-button::after {\n  margin-left: $spacer;\n}\n\n// Body\n\n.accordion-body {\n  padding-top: 0;\n}", "//\n// breadcrumb.scss\n// Extended from Bootstrap\n//\n\n.breadcrumb-item {\n  font-size: $breadcrumb-font-size;\n\n  + .breadcrumb-item {\n\n    &::before {\n      content: \"\\e930\";\n      font-family: \"Feather\";\n    }\n  }\n}\n\n\n// Horizontal scroll\n\n.breadcrumb-scroll {\n  display: flex;\n  flex-wrap: nowrap;\n  overflow-y: auto;\n\n  .breadcrumb-item {\n    white-space: nowrap;\n  }\n\n  // Hide scrollbar\n\n  &::-webkit-scrollbar {\n    display: none;\n  }\n}", "//\n// pagination.scss\n// Extended from Bootstrap\n//\n\n.pagination-sm .page-link {\n  line-height: $btn-line-height-sm;\n  font-size: $btn-font-size-sm;\n}\n\n.pagination-sm .page-item:first-child .page-link {\n  border-top-left-radius: $btn-border-radius-sm;\n  border-bottom-left-radius: $btn-border-radius-sm;\n}\n\n.pagination-sm .page-item:last-child .page-link {\n  border-top-right-radius: $btn-border-radius-sm;\n  border-bottom-right-radius: $btn-border-radius-sm;\n}\n\n.pagination-lg .page-link {\n  line-height: $btn-line-height-lg;\n  font-size: $btn-font-size-lg;\n}\n\n.pagination-lg .page-item:first-child .page-link {\n  border-top-left-radius: $btn-border-radius-lg;\n  border-bottom-left-radius: $btn-border-radius-lg;\n}\n\n.pagination-lg .page-item:last-child .page-link {\n  border-top-right-radius: $btn-border-radius-lg;\n  border-bottom-right-radius: $btn-border-radius-lg;\n}\n", "//\n// badge.scss\n// Extended from Bootstrap\n//\n\n// Links\n\n.badge[href] {\n\n  &:hover {\n    text-decoration: none;\n  }\n}\n\n\n// Sizing\n\n.badge-lg {\n  padding: $badge-padding-y-lg $badge-padding-x-lg;\n}\n\n\n// Rounded circle\n\n.badge-rounded-circle {\n  height: calc(1em + #{$badge-padding-y * 2});\n  padding-left: $badge-padding-y;\n  padding-right: $badge-padding-y;\n  border-radius: $border-radius-pill;\n\n  > * {\n    display: block;\n    width: 1em;\n  }\n\n\n  &.badge-lg {\n    height: calc(1em + #{$badge-padding-y-lg * 2});\n    padding-left: $badge-padding-y-lg;\n    padding-right: $badge-padding-y-lg;\n  }\n}\n\n\n// Positioning\n\n.badge-float {\n  position: absolute;\n  z-index: $zindex-dropdown;\n}\n\n.badge-float-inside {\n  top: 1rem;\n  right: 1rem;\n}\n\n.badge-float-outside {\n  top: -.5rem;\n  right: -.5rem;\n}\n\n\n// Active states\n\n.badge.bg-white-soft.active {\n  background-color: $white !important;\n  color: $primary !important;\n\n  &:hover, &:focus {\n    background-color: darken($white, 5%);\n    color: $primary;\n  }\n}\n\n.badge.bg-secondary-soft.active {\n  background-color: $primary !important;\n  color: color-contrast($primary) !important;\n\n  &:hover, &:focus {\n    background-color: darken($primary, 5%);\n    color: color-contrast(darken($primary, 5%));\n  }\n}\n\n\n// Creates the \"soft\" badge variant\n\n@each $color, $value in $theme-colors {\n  .badge.bg-#{$color}-soft {\n    @include badge-variant-soft($value, $badge-soft-bg-opacity);\n  }\n}\n\n.badge.bg-gray-700-soft {\n  @include badge-variant-soft($gray-700, $badge-soft-bg-opacity);\n}\n\n\n// Color options\n\n.badge.bg-gray-600 {\n  background-color: $gray-600;\n  color: $white;\n}\n\n\n// Text color\n//\n// Replacing the default white text color\n\n@each $color, $value in $theme-colors {\n  .badge.bg-#{$color} {\n    color: color-contrast($value);\n  }\n}\n", "//\n// badge.scss\n// Extended from Bootstrap\n//\n\n@mixin badge-variant-soft($color, $badge-soft-bg-opacity) {\n  background-color: rgba($color, $badge-soft-bg-opacity);\n  color: $color;\n\n  &[href]:hover,\n  &[href]:focus {\n    background-color: rgba($color, $badge-soft-bg-opacity * 2);\n    color: $color;\n  }\n}", "//\n// alert.scss\n// Extended from Bootstrap\n//\n\n// Color variants\n//\n// Using Bootstrap's core alert-variant mixin to generate solid background color + yiq colorized text (and making close/links match those colors)\n\n@each $color, $value in $theme-colors {\n  .alert-#{$color} {\n    @include alert-variant(shift-color($value, $alert-bg-scale), shift-color($value, $alert-border-scale), color-contrast(shift-color($value, $alert-bg-scale)));\n\n    .alert-link, .btn-close {\n      color: color-contrast(shift-color($value, $alert-bg-scale));\n    }\n\n    hr {\n      background-color: darken(shift-color($value, $alert-border-scale), 5%);\n    }\n  }\n}", "//\n// list-group.scss\n// Extended from Bootstrap\n//\n\n.list-group-flush {\n\n  .list-group-item {\n    padding-left: 0;\n    padding-right: 0;\n\n    &:first-child {\n      padding-top: 0 !important;\n      border-top: 0;\n    }\n\n    &:last-child {\n      padding-bottom: 0 !important;\n      border-bottom: 0;\n    }\n  }\n}\n", "//\n// close.scss\n// Extended from Bootstrap\n//\n\n.btn-close {\n\n  &:hover, &:focus {\n    outline: none;\n  }\n}", "//\n// modal.scss\n// Extended from Bootstrap\n//\n\n.modal-open .navbar.fixed-top {\n  padding-right: inherit;\n}\n\n\n// Close\n\n.modal .btn-close {\n  position: absolute;\n  top: $spacer * 1.5;\n  right: $spacer * 1.5;\n  z-index: $zindex-dropdown;\n}\n", "//\n// popover.scss\n// Extended from Bootstrap\n//\n\n.popover-header {\n  padding-bottom: 0;\n  margin-bottom: $headings-margin-bottom;\n  font-size: $popover-header-font-size;\n  color: $popover-header-color;\n}\n\n.popover-header:not(:empty) ~ .popover-body {\n  padding-top: 0;\n}", "//\n// avatar.scss\n// Theme component\n//\n\n// General\n\n.avatar {\n  position: relative;\n  display: inline-block;\n  width: $avatar-size-base;\n  height: $avatar-size-base;\n  font-size: $avatar-size-base / 3;\n\n  // Loads mask images so they don't lag on hover\n\n  &:after {\n    content: '';\n    position: absolute;\n    width: 0;\n    height: 0;\n    background-image: url(#{$path-to-img}/masks/avatar-status.svg),\n    url(#{$path-to-img}/masks/avatar-group.svg),\n    url(#{$path-to-img}/masks/avatar-group-hover.svg),\n    url(#{$path-to-img}/masks/avatar-group-hover-last.svg);\n  }\n}\n\n.avatar-img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-title {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  background-color: $avatar-title-bg;\n  color: $avatar-title-color;\n}\n\n\n// Status\n\n.avatar-online,\n.avatar-offline {\n\n  &::before {\n    content: '';\n    position: absolute;\n    bottom: 5%;\n    right: 5%;\n    width: 20%;\n    height: 20%;\n    border-radius: 50%;\n  }\n\n  .avatar-img {\n    mask-image: url(#{$path-to-img}/masks/avatar-status.svg);\n    mask-size: 100% 100%;\n  }\n}\n\n.avatar-online::before {\n  background-color: $success;\n}\n\n.avatar-offline::before {\n  background-color: $gray-500;\n}\n\n\n// Sizing\n\n.avatar-xs {\n  width: $avatar-size-xs;\n  height: $avatar-size-xs;\n  font-size: $avatar-size-xs / 3;\n}\n\n.avatar-sm {\n  width: $avatar-size-sm;\n  height: $avatar-size-sm;\n  font-size: $avatar-size-sm / 3;\n}\n\n.avatar-lg {\n  width: $avatar-size-lg;\n  height: $avatar-size-lg;\n  font-size: $avatar-size-lg / 3;\n}\n\n.avatar-xl {\n  width: $avatar-size-lg;\n  height: $avatar-size-lg;\n  font-size: $avatar-size-lg / 3;\n\n  @include media-breakpoint-up(lg) {\n    width: $avatar-size-xl;\n    height: $avatar-size-xl;\n    font-size: $avatar-size-xl / 3;\n  }\n}\n\n.avatar-xxl {\n  width: $avatar-size-xl;\n  height: $avatar-size-xl;\n  font-size: $avatar-size-xl / 3;\n\n  @include media-breakpoint-up(lg) {\n    width: $avatar-size-xxl;\n    height: $avatar-size-xxl;\n    font-size: $avatar-size-xxl / 3;\n  }\n}\n\n\n// Ratio\n\n.avatar.avatar-4by3 {\n  width: $avatar-size-base * 4 / 3;\n}\n\n.avatar-xs.avatar-4by3 {\n  width: $avatar-size-xs * 4 / 3;\n}\n\n.avatar-sm.avatar-4by3 {\n  width: $avatar-size-sm * 4 / 3;\n}\n\n.avatar-lg.avatar-4by3 {\n  width: $avatar-size-lg * 4 / 3;\n}\n\n.avatar-xl.avatar-4by3 {\n  width: $avatar-size-xl * 4 / 3;\n}\n\n.avatar-xxl.avatar-4by3 {\n  width: $avatar-size-xxl * 4 / 3;\n}\n\n\n// Group\n\n.avatar-group {\n  display: inline-flex;\n\n  // Shift every next avatar left\n\n  .avatar + .avatar {\n    margin-left: -$avatar-size-base / 4;\n  }\n\n  .avatar-xs + .avatar-xs {\n    margin-left: -$avatar-size-xs / 4;\n  }\n\n  .avatar-sm + .avatar-sm {\n    margin-left: -$avatar-size-sm / 4;\n  }\n\n  .avatar-lg + .avatar-lg {\n    margin-left: -$avatar-size-lg / 4;\n  }\n\n  .avatar-xl + .avatar-xl {\n    margin-left: -$avatar-size-xl / 4;\n  }\n\n  .avatar-xxl + .avatar-xxl {\n    margin-left: -$avatar-size-xxl / 4;\n  }\n\n  // Add some spacing between avatars\n\n  .avatar:not(:last-child) {\n    mask-image: url(#{$path-to-img}/masks/avatar-group.svg);\n    mask-size: 100% 100%;\n  }\n\n  // Bring an avatar to front on hover\n\n  .avatar:hover {\n    mask-image: none;\n    z-index: 1;\n\n    + .avatar {\n      mask-image: url(#{$path-to-img}/masks/avatar-group-hover.svg);\n      mask-size: 100% 100%;\n\n      &:last-child {\n        mask-image: url(#{$path-to-img}/masks/avatar-group-hover-last.svg);\n      }\n    }\n  }\n\n}\n", "//\n// device.scss\n// Theme component\n//\n\n.device {\n  position: relative;\n\n  > .img-fluid {\n    position: relative;\n  }\n}\n\n.device::before {\n  content: \"\";\n  background-color: $light;\n}\n\n.device::before,\n.device-screen {\n  position: absolute;\n  object-fit: cover;\n}\n\n\n//\n// Single devices\n//\n\n.device-iphonex::before,\n.device-iphonex > .device-screen {\n  top: 7.784431138%; left: 16.4021164%;\n  width: 66.137566137%; height: 80.838323353%;\n}\n\n.device-macbook::before,\n.device-macbook > .device-screen {\n  top: 11.53846154%; left: 13.38709677%;\n  width: 73.548387096%; height: 73.076923076%;\n}\n\n\n//\n// Combos\n//\n\n.device-combo {\n  position: relative;\n}\n\n.device-combo > .device {\n  position: absolute;\n}\n\n.device-combo-iphonex-iphonex {\n  padding-bottom: 130.250482%;\n}\n\n.device-combo-iphonex-iphonex > .device-iphonex {\n  \n  &:first-child {\n    bottom: 0; left: 0;\n    width: 65.5260116%;\n    z-index: 1;\n  }\n\n  &:last-child {\n    top: 0; right: 0;\n    width: 72.8323699%;\n  }\n}\n\n.device-combo-macbook-iphonex,\n.device-combo-iphonex-macbook {\n  padding-bottom: 62.4260355%;\n}\n\n.device-combo-macbook-iphonex > .device-macbook,\n.device-combo-iphonex-macbook > .device-macbook {\n  width: 91.7159763%;\n}\n\n.device-combo-macbook-iphonex > .device-iphonex,\n.device-combo-iphonex-macbook > .device-iphonex {\n  width: 27.9585799%;\n  z-index: 1;\n}\n\n.device-combo-macbook-iphonex > .device-macbook {\n  top: 0; left: 0;\n}\n\n.device-combo-iphonex-macbook > .device-macbook {\n  top: 0; right: 0;\n}\n\n.device-combo-macbook-iphonex > .device-iphonex {\n  bottom: 0; right: 0;\n}\n\n.device-combo-iphonex-macbook > .device-iphonex {\n  bottom: 0; left: 0;\n}", "//\n// footer.scss\n// Theme component\n//\n\n// Brand image\n\n.bg-dark .footer-brand {\n  filter: brightness(0) invert(1);\n}", "//\n// icon.scss\n// Theme component\n//\n\n.icon > svg {\n  width: $icon-size-base;\n  height: $icon-size-base;\n}\n\n// Use \"text-\" utilities to fill svg icons\n.icon[class*=\"text-\"] > svg [fill]:not([fill=\"none\"]) {\n  fill: currentColor !important;\n}\n\n\n// Sizing\n\n.icon-xs > svg {\n  width: $icon-size-xs;\n  height: $icon-size-xs;\n}\n\n.icon-sm > svg {\n  width: $icon-size-sm;\n  height: $icon-size-sm;\n}\n\n.icon-lg > svg {\n  width: $icon-size-lg;\n  height: $icon-size-lg;\n}\n\n.icon-xl > svg {\n  width: $icon-size-xl;\n  height: $icon-size-xl;\n}\n\n\n// Circle\n\n.icon-circle {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: $icon-circle-size;\n  height: $icon-circle-size;\n  border-radius: 50%;\n\n  > .fe {\n    font-size: $icon-circle-font-size;\n  }\n}\n", "//\n// list.scss\n// Theme component\n//\n\n.list {\n  margin-bottom: 0;\n  padding-left: 0;\n  list-style-type: none;\n}\n\n.list-item + .list-item {\n  margin-top: $list-item-spacing-y;\n}\n\n.list-link {\n  display: flex;\n  flex-wrap: nowrap;\n  align-items: center;\n  font-size: $list-link-font-size;\n  color: $list-link-color;\n\n  &:hover, &:focus {\n    text-decoration: none;\n    color: $list-link-hover-color;\n  }\n}", "//\n// screenshot.scss\n// Theme component\n//\n\n.screenshot {\n  border-radius: $screenshot-border-radius;\n  box-shadow: $screenshot-box-shadow;\n}", "//\n// section.scss\n// Theme module\n//\n\n.section-border {\n  position: relative;\n}\n\n.section-border::before {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  border-width: $border-width * 2 0 0 0;\n  border-style: solid;\n  border-color: inherit;\n  content: \"\";\n}", "//\n// shapes.scss\n// Theme component\n//\n\n.shape {\n  position: absolute;\n  pointer-events: none;\n}\n\n.shape > * {\n  display: block;\n}\n\n.shape:not([class*=\"shape-blur\"]) {\n  overflow: hidden;\n}\n\n.shape:not([class*=\"shape-blur\"]) > * {\n  transform: scale(2);\n}\n\n\n// Hide SVG images in IE\n\n@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n\n  .shape > svg {\n    display: none;\n  }\n}\n\n\n// Position\n\n.shape-top {\n  top: 0;\n  right: 0;\n  left: 0;\n}\n\n.shape-top > * {\n  transform-origin: bottom center;\n}\n\n.shape-end {\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n\n.shape-end > * {\n  position: inherit;\n  top: inherit;\n  bottom: inherit;\n  right: inherit;\n  transform-origin: center left;\n}\n\n.shape-bottom {\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n\n.shape-bottom > * {\n  transform-origin: top center;\n}\n\n.shape-bottom-100 {\n  right: 0;\n  bottom: 100%;\n  left: 0;\n}\n\n.shape-bottom-100 > * {\n  transform-origin: top center;\n}\n\n.shape-start {\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n\n.shape-start > * {\n  position: inherit;\n  top: inherit;\n  bottom: inherit;\n  left: inherit;\n  transform-origin: center right;\n}\n\n\n// Orientation\n\n.shape-fluid-x > * {\n  width: 100%;\n  height: auto;\n}\n\n.shape-fluid-y > * {\n  width: auto;\n  height: 100%;\n}\n\n\n// Direction\n\n.shape-flip-x {\n  transform: scaleX(-1);\n}\n\n\n// Blurs\n\n.shape-blur-1 {\n  top: 0;\n  right: 0;\n  left: 0;\n\n  @include media-breakpoint-up(lg) {\n    bottom: 0;\n    left: 50%;\n    right: auto;\n  }\n}\n\n.shape-blur-1 > * {\n  width: 100%;\n  height: auto;\n\n  @include media-breakpoint-up(lg) {\n    width: auto;\n    height: 100%;\n  }\n}\n\n.shape-blur-2 {\n  top: 0;\n  right: 0;\n  left: 0;\n\n  @include media-breakpoint-up(lg) {\n    bottom: 0;\n    right: 50%;\n    left: auto;\n  }\n}\n\n.shape-blur-2 > * {\n  width: 100%;\n  height: auto;\n\n  @include media-breakpoint-up(lg) {\n    width: auto;\n    height: 100%;\n    float: right;\n  }\n}\n\n.shape-blur-3 {\n  top: 0;\n  right: 0;\n  left: 0;\n}\n\n.shape-blur-3 > * {\n  width: 100%;\n  height: auto;\n}\n\n.shape-blur-4 {\n  top: 0;\n  bottom: 0;\n  left: 50%;\n  width: 100%;\n  transform: translateX(-50%) scale(1.3);\n}\n\n.shape-blur-4 > * {\n  width: auto;\n  height: 100%;\n  margin-left: auto;\n  margin-right: auto;\n}\n", "//\n// sidenav.scss\n// Theme components\n//\n\n.sidenav {\n\n  @include media-breakpoint-up(lg) {\n    position: fixed;\n    top: 84px;\n    height: calc(100vh - 84px);\n    overflow: auto;\n  }\n}\n\n.sidenav-start {\n\n  @include media-breakpoint-up(lg) {\n    left: 0;\n  }\n}\n\n.sidenav-end {\n\n  @include media-breakpoint-up(lg) {\n    right: 0;\n  }\n}", "//\n// aos.scss\n// Plugin overrides\n//\n\n[data-aos=\"img-skewed-item-start\"],\n[data-aos=\"img-skewed-item-end\"] {\n  opacity: 0;\n  transition-property: opacity, transform, -webkit-transform;\n  will-change: opacity, transform, -webkit-transform;\n  backface-visibility: hidden;\n\n  &.aos-animate {\n    opacity: 1;\n  }\n}\n\n[data-aos=\"img-skewed-item-start\"] {\n  transform: rotateY(-$img-skewed-rotate-y + 15deg) rotateX($img-skewed-rotate-x - 10deg) translate3d(0, 100px, 0);\n\n  &.aos-animate {\n    transform: rotateY(-$img-skewed-rotate-y) rotateX($img-skewed-rotate-x) translate3d(0, 0, 0);\n  }\n}\n\n[data-aos=\"img-skewed-item-end\"] {\n  transform: rotateY($img-skewed-rotate-y - 15deg) rotateX($img-skewed-rotate-x - 10deg) translate3d(0, 100px, 0);\n\n  &.aos-animate {\n    transform: rotateY($img-skewed-rotate-y) rotateX($img-skewed-rotate-x) translate3d(0, 0, 0);\n  }\n}\n", "//\n// bigpicture.scss\n// Plugin overrides\n//\n\n#bp_container {\n  background: rgba($white, .873) !important;\n}\n\n#bp_container > * {\n  box-shadow: none !important;\n}\n\n.bp-x {\n  display: none;\n}\n", "//\n// choices.scss\n// Plugin overrides\n//\n\n.choices {\n  position: relative;\n  cursor: pointer;\n}\n\n.choices:focus {\n  outline: none;\n}\n\n.choices::after {\n  display: none;\n}\n\n\n// Dropdown menu\n\n.choices > .dropdown-menu {\n  width: 100%;\n}\n\n.choices > .dropdown-menu [class^=\"form-control\"] {\n  width: 100%;\n  margin-bottom: $spacer;\n}\n\n.choices > .dropdown-menu > .dropdown-item.is-highlighted {\n  color: $dropdown-link-hover-color;\n}\n\n// Sizing\n\n.choices > .form-control-xs + .dropdown-menu,\n.choices > .form-select-xs + .dropdown-menu {\n  min-width: 100%;\n  width: auto;\n  padding: $dropdown-padding-y-xs $dropdown-padding-x-xs;\n}\n\n.choices > .form-select-xs + .dropdown-menu,\n.choices > .form-control-xs + .dropdown-menu {\n  padding-left: $dropdown-padding-x-xs;\n  padding-right: $dropdown-padding-x-xs;\n}\n\n.choices > .form-select-xs + .dropdown-menu > .dropdown-item,\n.choices > .form-control-xs + .dropdown-menu > .dropdown-item {\n  margin-left: -$dropdown-padding-x-xs;\n  margin-right: -$dropdown-padding-x-xs;\n  padding-left: -$dropdown-padding-x-xs;\n  padding-left: -$dropdown-padding-x-xs;\n}\n\n.choices > .form-select-lg + .dropdown-menu,\n.choices > .form-control-lg + .dropdown-menu {\n  padding-left: $dropdown-padding-x-lg;\n  padding-right: $dropdown-padding-x-lg;\n}\n\n.choices > .form-select-lg + .dropdown-menu > .dropdown-item,\n.choices > .form-control-lg + .dropdown-menu > .dropdown-item {\n  margin-left: -$dropdown-padding-x-lg;\n  margin-right: -$dropdown-padding-x-lg;\n  padding-left: -$dropdown-padding-x-lg;\n  padding-left: -$dropdown-padding-x-lg;\n}\n\n\n// Placeholder\n\n.choices__placeholder {\n  color: $input-placeholder-color;\n  opacity: 1;\n}\n\n\n// Open\n\n.choices.is-open > .form-control,\n.choices.is-open > .form-select {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.choices.is-open > .form-control + .dropdown-menu,\n.choices.is-open > .form-select + .dropdown-menu {\n  margin-top: -$dropdown-border-width;\n  border-top-right-radius: 0;\n  border-top-left-radius: 0;\n}\n\n\n// Multiple\n\n.choices__list--multiple + .form-control {\n  display: inline-block;\n  min-height: 0;\n  width: auto;\n  padding: 0;\n  border-width: 0;\n  border-radius: 0;\n  background-color: transparent;\n  font-size: $input-font-size;\n}\n\n.form-control-lg + .choices__list--multiple + .form-control,\n.form-select-lg + .choices__list--multiple + .form-control {\n  font-size: $input-font-size-lg;\n}\n\n.form-control-sm + .choices__list--multiple + .form-control,\n.form-select-sm + .choices__list--multiple + .form-control {\n  font-size: $input-font-size-sm;\n}\n\n.choices__list--multiple {\n  display: inline;\n}\n\n.choices__list--multiple > .choices__item {\n  display: inline-block;\n  margin-right: .2rem;\n  padding-right: .375rem;\n  padding-left: .375rem;\n  font-size: $font-size-sm;\n  background-color: $light;\n  border-radius: $border-radius-sm;\n}\n\n.choices__list--multiple > .choices__item > .choices__button {\n  width: .5rem;\n  margin-left: .5rem;\n  padding: 0;\n  outline: none;\n  border-width: 0;\n  background-color: transparent;\n  background-repeat: no-repeat;\n  background-position: center center;\n  background-image: url(escape-svg(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='#{$body-color}' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><line x1='18' y1='6' x2='6' y2='18'></line><line x1='6' y1='6' x2='18' y2='18'></line></svg>\"));\n  background-size: 100% auto;\n  text-indent: -9999px;\n  opacity: .5;\n  transition: $input-transition;\n}\n\n.choices__list--multiple > .choices__item:hover > .choices__button {\n  opacity: 1;\n}\n", "//\n// dropzone.scss\n// Plugin overrides\n//\n\n.dropzone {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n}\n\n.dz-message {\n  padding: 5rem 1rem;\n  background-color: $input-bg;\n  border: $input-border-width dashed $gray-400;\n  border-radius: $border-radius;\n  text-align: center;\n  color: $text-muted;\n  transition: $transition-base;\n  order: -1;\n  cursor: pointer;\n  z-index: 999;\n\n  &:hover {\n    border-color: $text-muted;\n    color: $body-color;\n  }\n}\n\n.dz-button {\n  background: none;\n  border: 0;\n  font-size: inherit;\n  color: inherit;\n}\n\n.dz-drag-hover .dz-message {\n  border-color: $primary;\n  color: $primary;\n}\n\n.dropzone-multiple .dz-message {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n\n.dropzone-single.dz-max-files-reached .dz-message {\n  background-color: fade-out($black, .1);\n  color: white;\n  opacity: 0;\n\n  &:hover {\n    opacity: 1;\n  }\n}\n\n.dz-preview-single {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  border-radius: $border-radius;\n}\n\n.dz-preview-cover {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  border-radius: $border-radius;\n}\n\n.dz-preview-img {\n  object-fit: cover;\n  width: 100%;\n  height: 100%;\n  border-radius: $border-radius;\n}\n\n.dz-preview-multiple .list-group-item:last-child {\n  padding-bottom: 0;\n  border-bottom: 0;\n}\n\n[data-dz-size] strong {\n  font-weight: $font-weight-normal;\n}\n", "//\n// feather.scss\n// Plugin overrides\n//\n\n.fe {\n  font-family: 'Feather' !important;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.fe-activity:before {\n  content: \"\\e900\";\n}\n\n.fe-airplay:before {\n  content: \"\\e901\";\n}\n\n.fe-alert-circle:before {\n  content: \"\\e902\";\n}\n\n.fe-alert-octagon:before {\n  content: \"\\e903\";\n}\n\n.fe-alert-triangle:before {\n  content: \"\\e904\";\n}\n\n.fe-align-center:before {\n  content: \"\\e905\";\n}\n\n.fe-align-justify:before {\n  content: \"\\e906\";\n}\n\n.fe-align-left:before {\n  content: \"\\e907\";\n}\n\n.fe-align-right:before {\n  content: \"\\e908\";\n}\n\n.fe-anchor:before {\n  content: \"\\e909\";\n}\n\n.fe-aperture:before {\n  content: \"\\e90a\";\n}\n\n.fe-archive:before {\n  content: \"\\e90b\";\n}\n\n.fe-arrow-down:before {\n  content: \"\\e90c\";\n}\n\n.fe-arrow-down-circle:before {\n  content: \"\\e90d\";\n}\n\n.fe-arrow-down-left:before {\n  content: \"\\e90e\";\n}\n\n.fe-arrow-down-right:before {\n  content: \"\\e90f\";\n}\n\n.fe-arrow-left:before {\n  content: \"\\e910\";\n}\n\n.fe-arrow-left-circle:before {\n  content: \"\\e911\";\n}\n\n.fe-arrow-right:before {\n  content: \"\\e912\";\n}\n\n.fe-arrow-right-circle:before {\n  content: \"\\e913\";\n}\n\n.fe-arrow-up:before {\n  content: \"\\e914\";\n}\n\n.fe-arrow-up-circle:before {\n  content: \"\\e915\";\n}\n\n.fe-arrow-up-left:before {\n  content: \"\\e916\";\n}\n\n.fe-arrow-up-right:before {\n  content: \"\\e917\";\n}\n\n.fe-at-sign:before {\n  content: \"\\e918\";\n}\n\n.fe-award:before {\n  content: \"\\e919\";\n}\n\n.fe-bar-chart:before {\n  content: \"\\e91a\";\n}\n\n.fe-bar-chart-2:before {\n  content: \"\\e91b\";\n}\n\n.fe-battery:before {\n  content: \"\\e91c\";\n}\n\n.fe-battery-charging:before {\n  content: \"\\e91d\";\n}\n\n.fe-bell:before {\n  content: \"\\e91e\";\n}\n\n.fe-bell-off:before {\n  content: \"\\e91f\";\n}\n\n.fe-bluetooth:before {\n  content: \"\\e920\";\n}\n\n.fe-bold:before {\n  content: \"\\e921\";\n}\n\n.fe-book:before {\n  content: \"\\e922\";\n}\n\n.fe-book-open:before {\n  content: \"\\e923\";\n}\n\n.fe-bookmark:before {\n  content: \"\\e924\";\n}\n\n.fe-box:before {\n  content: \"\\e925\";\n}\n\n.fe-briefcase:before {\n  content: \"\\e926\";\n}\n\n.fe-calendar:before {\n  content: \"\\e927\";\n}\n\n.fe-camera:before {\n  content: \"\\e928\";\n}\n\n.fe-camera-off:before {\n  content: \"\\e929\";\n}\n\n.fe-cast:before {\n  content: \"\\e92a\";\n}\n\n.fe-check:before {\n  content: \"\\e92b\";\n}\n\n.fe-check-circle:before {\n  content: \"\\e92c\";\n}\n\n.fe-check-square:before {\n  content: \"\\e92d\";\n}\n\n.fe-chevron-down:before {\n  content: \"\\e92e\";\n}\n\n.fe-chevron-left:before {\n  content: \"\\e92f\";\n}\n\n.fe-chevron-right:before {\n  content: \"\\e930\";\n}\n\n.fe-chevron-up:before {\n  content: \"\\e931\";\n}\n\n.fe-chevrons-down:before {\n  content: \"\\e932\";\n}\n\n.fe-chevrons-left:before {\n  content: \"\\e933\";\n}\n\n.fe-chevrons-right:before {\n  content: \"\\e934\";\n}\n\n.fe-chevrons-up:before {\n  content: \"\\e935\";\n}\n\n.fe-chrome:before {\n  content: \"\\e936\";\n}\n\n.fe-circle:before {\n  content: \"\\e937\";\n}\n\n.fe-clipboard:before {\n  content: \"\\e938\";\n}\n\n.fe-clock:before {\n  content: \"\\e939\";\n}\n\n.fe-cloud:before {\n  content: \"\\e93a\";\n}\n\n.fe-cloud-drizzle:before {\n  content: \"\\e93b\";\n}\n\n.fe-cloud-lightning:before {\n  content: \"\\e93c\";\n}\n\n.fe-cloud-off:before {\n  content: \"\\e93d\";\n}\n\n.fe-cloud-rain:before {\n  content: \"\\e93e\";\n}\n\n.fe-cloud-snow:before {\n  content: \"\\e93f\";\n}\n\n.fe-code:before {\n  content: \"\\e940\";\n}\n\n.fe-codepen:before {\n  content: \"\\e941\";\n}\n\n.fe-command:before {\n  content: \"\\e942\";\n}\n\n.fe-compass:before {\n  content: \"\\e943\";\n}\n\n.fe-copy:before {\n  content: \"\\e944\";\n}\n\n.fe-corner-down-left:before {\n  content: \"\\e945\";\n}\n\n.fe-corner-down-right:before {\n  content: \"\\e946\";\n}\n\n.fe-corner-left-down:before {\n  content: \"\\e947\";\n}\n\n.fe-corner-left-up:before {\n  content: \"\\e948\";\n}\n\n.fe-corner-right-down:before {\n  content: \"\\e949\";\n}\n\n.fe-corner-right-up:before {\n  content: \"\\e94a\";\n}\n\n.fe-corner-up-left:before {\n  content: \"\\e94b\";\n}\n\n.fe-corner-up-right:before {\n  content: \"\\e94c\";\n}\n\n.fe-cpu:before {\n  content: \"\\e94d\";\n}\n\n.fe-credit-card:before {\n  content: \"\\e94e\";\n}\n\n.fe-crop:before {\n  content: \"\\e94f\";\n}\n\n.fe-crosshair:before {\n  content: \"\\e950\";\n}\n\n.fe-database:before {\n  content: \"\\e951\";\n}\n\n.fe-delete:before {\n  content: \"\\e952\";\n}\n\n.fe-disc:before {\n  content: \"\\e953\";\n}\n\n.fe-dollar-sign:before {\n  content: \"\\e954\";\n}\n\n.fe-download:before {\n  content: \"\\e955\";\n}\n\n.fe-download-cloud:before {\n  content: \"\\e956\";\n}\n\n.fe-droplet:before {\n  content: \"\\e957\";\n}\n\n.fe-edit:before {\n  content: \"\\e958\";\n}\n\n.fe-edit-2:before {\n  content: \"\\e959\";\n}\n\n.fe-edit-3:before {\n  content: \"\\e95a\";\n}\n\n.fe-external-link:before {\n  content: \"\\e95b\";\n}\n\n.fe-eye:before {\n  content: \"\\e95c\";\n}\n\n.fe-eye-off:before {\n  content: \"\\e95d\";\n}\n\n.fe-facebook:before {\n  content: \"\\e95e\";\n}\n\n.fe-fast-forward:before {\n  content: \"\\e95f\";\n}\n\n.fe-feather:before {\n  content: \"\\e960\";\n}\n\n.fe-file:before {\n  content: \"\\e961\";\n}\n\n.fe-file-minus:before {\n  content: \"\\e962\";\n}\n\n.fe-file-plus:before {\n  content: \"\\e963\";\n}\n\n.fe-file-text:before {\n  content: \"\\e964\";\n}\n\n.fe-film:before {\n  content: \"\\e965\";\n}\n\n.fe-filter:before {\n  content: \"\\e966\";\n}\n\n.fe-flag:before {\n  content: \"\\e967\";\n}\n\n.fe-folder:before {\n  content: \"\\e968\";\n}\n\n.fe-folder-minus:before {\n  content: \"\\e969\";\n}\n\n.fe-folder-plus:before {\n  content: \"\\e96a\";\n}\n\n.fe-gift:before {\n  content: \"\\e96b\";\n}\n\n.fe-git-branch:before {\n  content: \"\\e96c\";\n}\n\n.fe-git-commit:before {\n  content: \"\\e96d\";\n}\n\n.fe-git-merge:before {\n  content: \"\\e96e\";\n}\n\n.fe-git-pull-request:before {\n  content: \"\\e96f\";\n}\n\n.fe-github:before {\n  content: \"\\e970\";\n}\n\n.fe-gitlab:before {\n  content: \"\\e971\";\n}\n\n.fe-globe:before {\n  content: \"\\e972\";\n}\n\n.fe-grid:before {\n  content: \"\\e973\";\n}\n\n.fe-hard-drive:before {\n  content: \"\\e974\";\n}\n\n.fe-hash:before {\n  content: \"\\e975\";\n}\n\n.fe-headphones:before {\n  content: \"\\e976\";\n}\n\n.fe-heart:before {\n  content: \"\\e977\";\n}\n\n.fe-help-circle:before {\n  content: \"\\e978\";\n}\n\n.fe-home:before {\n  content: \"\\e979\";\n}\n\n.fe-image:before {\n  content: \"\\e97a\";\n}\n\n.fe-inbox:before {\n  content: \"\\e97b\";\n}\n\n.fe-info:before {\n  content: \"\\e97c\";\n}\n\n.fe-instagram:before {\n  content: \"\\e97d\";\n}\n\n.fe-italic:before {\n  content: \"\\e97e\";\n}\n\n.fe-layers:before {\n  content: \"\\e97f\";\n}\n\n.fe-layout:before {\n  content: \"\\e980\";\n}\n\n.fe-life-buoy:before {\n  content: \"\\e981\";\n}\n\n.fe-link:before {\n  content: \"\\e982\";\n}\n\n.fe-link-2:before {\n  content: \"\\e983\";\n}\n\n.fe-linkedin:before {\n  content: \"\\e984\";\n}\n\n.fe-list:before {\n  content: \"\\e985\";\n}\n\n.fe-loader:before {\n  content: \"\\e986\";\n}\n\n.fe-lock:before {\n  content: \"\\e987\";\n}\n\n.fe-log-in:before {\n  content: \"\\e988\";\n}\n\n.fe-log-out:before {\n  content: \"\\e989\";\n}\n\n.fe-mail:before {\n  content: \"\\e98a\";\n}\n\n.fe-map:before {\n  content: \"\\e98b\";\n}\n\n.fe-map-pin:before {\n  content: \"\\e98c\";\n}\n\n.fe-maximize:before {\n  content: \"\\e98d\";\n}\n\n.fe-maximize-2:before {\n  content: \"\\e98e\";\n}\n\n.fe-menu:before {\n  content: \"\\e98f\";\n}\n\n.fe-message-circle:before {\n  content: \"\\e990\";\n}\n\n.fe-message-square:before {\n  content: \"\\e991\";\n}\n\n.fe-mic:before {\n  content: \"\\e992\";\n}\n\n.fe-mic-off:before {\n  content: \"\\e993\";\n}\n\n.fe-minimize:before {\n  content: \"\\e994\";\n}\n\n.fe-minimize-2:before {\n  content: \"\\e995\";\n}\n\n.fe-minus:before {\n  content: \"\\e996\";\n}\n\n.fe-minus-circle:before {\n  content: \"\\e997\";\n}\n\n.fe-minus-square:before {\n  content: \"\\e998\";\n}\n\n.fe-monitor:before {\n  content: \"\\e999\";\n}\n\n.fe-moon:before {\n  content: \"\\e99a\";\n}\n\n.fe-more-horizontal:before {\n  content: \"\\e99b\";\n}\n\n.fe-more-vertical:before {\n  content: \"\\e99c\";\n}\n\n.fe-move:before {\n  content: \"\\e99d\";\n}\n\n.fe-music:before {\n  content: \"\\e99e\";\n}\n\n.fe-navigation:before {\n  content: \"\\e99f\";\n}\n\n.fe-navigation-2:before {\n  content: \"\\e9a0\";\n}\n\n.fe-octagon:before {\n  content: \"\\e9a1\";\n}\n\n.fe-package:before {\n  content: \"\\e9a2\";\n}\n\n.fe-paperclip:before {\n  content: \"\\e9a3\";\n}\n\n.fe-pause:before {\n  content: \"\\e9a4\";\n}\n\n.fe-pause-circle:before {\n  content: \"\\e9a5\";\n}\n\n.fe-percent:before {\n  content: \"\\e9a6\";\n}\n\n.fe-phone:before {\n  content: \"\\e9a7\";\n}\n\n.fe-phone-call:before {\n  content: \"\\e9a8\";\n}\n\n.fe-phone-forwarded:before {\n  content: \"\\e9a9\";\n}\n\n.fe-phone-incoming:before {\n  content: \"\\e9aa\";\n}\n\n.fe-phone-missed:before {\n  content: \"\\e9ab\";\n}\n\n.fe-phone-off:before {\n  content: \"\\e9ac\";\n}\n\n.fe-phone-outgoing:before {\n  content: \"\\e9ad\";\n}\n\n.fe-pie-chart:before {\n  content: \"\\e9ae\";\n}\n\n.fe-play:before {\n  content: \"\\e9af\";\n}\n\n.fe-play-circle:before {\n  content: \"\\e9b0\";\n}\n\n.fe-plus:before {\n  content: \"\\e9b1\";\n}\n\n.fe-plus-circle:before {\n  content: \"\\e9b2\";\n}\n\n.fe-plus-square:before {\n  content: \"\\e9b3\";\n}\n\n.fe-pocket:before {\n  content: \"\\e9b4\";\n}\n\n.fe-power:before {\n  content: \"\\e9b5\";\n}\n\n.fe-printer:before {\n  content: \"\\e9b6\";\n}\n\n.fe-radio:before {\n  content: \"\\e9b7\";\n}\n\n.fe-refresh-ccw:before {\n  content: \"\\e9b8\";\n}\n\n.fe-refresh-cw:before {\n  content: \"\\e9b9\";\n}\n\n.fe-repeat:before {\n  content: \"\\e9ba\";\n}\n\n.fe-rewind:before {\n  content: \"\\e9bb\";\n}\n\n.fe-rotate-ccw:before {\n  content: \"\\e9bc\";\n}\n\n.fe-rotate-cw:before {\n  content: \"\\e9bd\";\n}\n\n.fe-rss:before {\n  content: \"\\e9be\";\n}\n\n.fe-save:before {\n  content: \"\\e9bf\";\n}\n\n.fe-scissors:before {\n  content: \"\\e9c0\";\n}\n\n.fe-search:before {\n  content: \"\\e9c1\";\n}\n\n.fe-send:before {\n  content: \"\\e9c2\";\n}\n\n.fe-server:before {\n  content: \"\\e9c3\";\n}\n\n.fe-settings:before {\n  content: \"\\e9c4\";\n}\n\n.fe-share:before {\n  content: \"\\e9c5\";\n}\n\n.fe-share-2:before {\n  content: \"\\e9c6\";\n}\n\n.fe-shield:before {\n  content: \"\\e9c7\";\n}\n\n.fe-shield-off:before {\n  content: \"\\e9c8\";\n}\n\n.fe-shopping-bag:before {\n  content: \"\\e9c9\";\n}\n\n.fe-shopping-cart:before {\n  content: \"\\e9ca\";\n}\n\n.fe-shuffle:before {\n  content: \"\\e9cb\";\n}\n\n.fe-sidebar:before {\n  content: \"\\e9cc\";\n}\n\n.fe-skip-back:before {\n  content: \"\\e9cd\";\n}\n\n.fe-skip-forward:before {\n  content: \"\\e9ce\";\n}\n\n.fe-slack:before {\n  content: \"\\e9cf\";\n}\n\n.fe-slash:before {\n  content: \"\\e9d0\";\n}\n\n.fe-sliders:before {\n  content: \"\\e9d1\";\n}\n\n.fe-smartphone:before {\n  content: \"\\e9d2\";\n}\n\n.fe-speaker:before {\n  content: \"\\e9d3\";\n}\n\n.fe-square:before {\n  content: \"\\e9d4\";\n}\n\n.fe-star:before {\n  content: \"\\e9d5\";\n}\n\n.fe-stop-circle:before {\n  content: \"\\e9d6\";\n}\n\n.fe-sun:before {\n  content: \"\\e9d7\";\n}\n\n.fe-sunrise:before {\n  content: \"\\e9d8\";\n}\n\n.fe-sunset:before {\n  content: \"\\e9d9\";\n}\n\n.fe-tablet:before {\n  content: \"\\e9da\";\n}\n\n.fe-tag:before {\n  content: \"\\e9db\";\n}\n\n.fe-target:before {\n  content: \"\\e9dc\";\n}\n\n.fe-terminal:before {\n  content: \"\\e9dd\";\n}\n\n.fe-thermometer:before {\n  content: \"\\e9de\";\n}\n\n.fe-thumbs-down:before {\n  content: \"\\e9df\";\n}\n\n.fe-thumbs-up:before {\n  content: \"\\e9e0\";\n}\n\n.fe-toggle-left:before {\n  content: \"\\e9e1\";\n}\n\n.fe-toggle-right:before {\n  content: \"\\e9e2\";\n}\n\n.fe-trash:before {\n  content: \"\\e9e3\";\n}\n\n.fe-trash-2:before {\n  content: \"\\e9e4\";\n}\n\n.fe-trending-down:before {\n  content: \"\\e9e5\";\n}\n\n.fe-trending-up:before {\n  content: \"\\e9e6\";\n}\n\n.fe-triangle:before {\n  content: \"\\e9e7\";\n}\n\n.fe-truck:before {\n  content: \"\\e9e8\";\n}\n\n.fe-tv:before {\n  content: \"\\e9e9\";\n}\n\n.fe-twitter:before {\n  content: \"\\e9ea\";\n}\n\n.fe-type:before {\n  content: \"\\e9eb\";\n}\n\n.fe-umbrella:before {\n  content: \"\\e9ec\";\n}\n\n.fe-underline:before {\n  content: \"\\e9ed\";\n}\n\n.fe-unlock:before {\n  content: \"\\e9ee\";\n}\n\n.fe-upload:before {\n  content: \"\\e9ef\";\n}\n\n.fe-upload-cloud:before {\n  content: \"\\e9f0\";\n}\n\n.fe-user:before {\n  content: \"\\e9f1\";\n}\n\n.fe-user-check:before {\n  content: \"\\e9f2\";\n}\n\n.fe-user-minus:before {\n  content: \"\\e9f3\";\n}\n\n.fe-user-plus:before {\n  content: \"\\e9f4\";\n}\n\n.fe-user-x:before {\n  content: \"\\e9f5\";\n}\n\n.fe-users:before {\n  content: \"\\e9f6\";\n}\n\n.fe-video:before {\n  content: \"\\e9f7\";\n}\n\n.fe-video-off:before {\n  content: \"\\e9f8\";\n}\n\n.fe-voicemail:before {\n  content: \"\\e9f9\";\n}\n\n.fe-volume:before {\n  content: \"\\e9fa\";\n}\n\n.fe-volume-1:before {\n  content: \"\\e9fb\";\n}\n\n.fe-volume-2:before {\n  content: \"\\e9fc\";\n}\n\n.fe-volume-x:before {\n  content: \"\\e9fd\";\n}\n\n.fe-watch:before {\n  content: \"\\e9fe\";\n}\n\n.fe-wifi:before {\n  content: \"\\e9ff\";\n}\n\n.fe-wifi-off:before {\n  content: \"\\ea00\";\n}\n\n.fe-wind:before {\n  content: \"\\ea01\";\n}\n\n.fe-x:before {\n  content: \"\\ea02\";\n}\n\n.fe-x-circle:before {\n  content: \"\\ea03\";\n}\n\n.fe-x-square:before {\n  content: \"\\ea04\";\n}\n\n.fe-youtube:before {\n  content: \"\\ea05\";\n}\n\n.fe-zap:before {\n  content: \"\\ea06\";\n}\n\n.fe-zap-off:before {\n  content: \"\\ea07\";\n}\n\n.fe-zoom-in:before {\n  content: \"\\ea08\";\n}\n\n.fe-zoom-out:before {\n  content: \"\\ea09\";\n}\n\n// Sizing\n\n.fe-lg {\n  font-size: $h3-font-size;\n\n  @include media-breakpoint-up(lg) {\n    font-size: $h3-font-size-md;\n  }\n}\n", "//\n// flickity.scss\n// Plugin overrides\n//\n\n// Controls\n\n.flickity-prev-next-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: $input-height;\n  height: $input-height;\n  background-color: $primary;\n  box-shadow: $box-shadow-light;\n  color: $white;\n\n  &::before, &::after {\n    font-family: \"Feather\";\n  }\n\n  &:hover, &:focus {\n    background-color: $primary;\n    box-shadow: $box-shadow;\n  }\n}\n\n.flickity-prev-next-button.previous {\n  left: 0;\n  transform: translate(calc(-50% + #{$grid-gutter-width / 4}), -50%);\n\n  @include media-breakpoint-up(lg) {\n    transform: translate(-50%, -50%);\n  }\n\n  &::before {\n    content: \"\\e910\";\n  }\n}\n\n.flickity-prev-next-button.next {\n  right: 0;\n  transform: translate(calc(50% - #{$grid-gutter-width / 4}), -50%);\n\n  @include media-breakpoint-up(lg) {\n    transform: translate(50%, -50%);\n  }\n\n  &::before {\n    content: \"\\e912\";\n  }\n}\n\n.flickity-button-icon {\n  display: none;\n}\n\n\n// Button white\n\n.flickity-button-white .flickity-prev-next-button {\n  background-color: $white;\n  color: $primary;\n}\n\n\n// Button bottom\n\n.flickity-button-bottom .flickity-prev-next-button {\n  top: auto;\n  bottom: 1.5rem;\n  transform: none;\n}\n\n.flickity-button-bottom .flickity-prev-next-button.previous {\n  left: auto;\n  right: 6.375rem;\n}\n\n.flickity-button-bottom .flickity-prev-next-button.next {\n  left: auto;\n  right: 2.5rem;\n}\n\n\n// Button inset\n\n.flickity-button-inset .flickity-prev-next-button {\n  transform: translateY(-50%);\n}\n\n.flickity-button-inset .flickity-prev-next-button.previous {\n  left: 1.5rem;\n}\n\n.flickity-button-inset .flickity-prev-next-button.next {\n  right: 1.5rem;\n}\n\n\n// Viewport\n\n.flickity-viewport-visible .flickity-viewport {\n  overflow: visible;\n}\n\n\n// Adaptive height\n\n[data-flickity*='\"adaptiveHeight\": true'] .flickity-viewport {\n  transition: all .5s ease; // Make the transition match the rough duration of flicking between items\n}\n\n\n// Fixing .col gutters because Bootstrap sets it to 0 if they are not direct children of a .row\n\n.flickity-slider > [class^=\"col\"] {\n  padding-left: $grid-gutter-width / 2;\n  padding-right: $grid-gutter-width / 2;\n}\n", "//\n// highlight.js\n// Plugin overrides\n//\n\n.hljs {\n  background-color: transparent;\n}\n", "//\n// quill.scss\n// Quill plugin overrides\n//\n\n.ql-container {\n  font-family: $font-family-base;\n}\n\n.ql-toolbar {\n  position: relative;\n  padding: $input-padding-y $input-padding-x;\n  background-color: $input-bg;\n  border: $input-border-width solid $input-border-color;\n  border-radius: $input-border-radius $input-border-radius 0 0;\n  color: $input-color;\n}\n\n.ql-toolbar + .ql-container {\n  margin-top: -$border-width;\n}\n\n.ql-toolbar + .ql-container .ql-editor {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.ql-editor {\n  min-height: $line-height-base * $font-size-base * 4;\n  display: block;\n  width: 100%;\n  padding: $input-padding-y $input-padding-x;\n  font-size: $font-size-base;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @if $enable-rounded {\n    // Manually use the if/else instead of the mixin to account for iOS override\n    border-radius: $input-border-radius;\n  }\n\n  @else {\n    // Otherwise undo the iOS default\n    border-radius: 0;\n  }\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  // Unstyle the caret on `<select>`s in IE10+.\n  &::-ms-expand {\n    background-color: transparent;\n    border: 0;\n  }\n\n  &:focus {\n    border-color: $input-focus-border-color;\n  }\n}\n\n.ql-hidden {\n  position: absolute;\n  transform: scale(0);\n}\n\n\n// Placeholder\n\n.ql-editor.ql-blank::before {\n  top: $input-padding-y;\n  left: $input-padding-x;\n  font-style: normal;\n  color: $input-placeholder-color;\n}\n\n.ql-editor:focus::before {\n  display: none;\n}\n\n\n// Toolbar\n\n.ql-formats {\n  padding-left: .5rem;\n  padding-right: .5rem;\n\n  &:first-child {\n    padding-left: 0;\n  }\n\n  &:last-child {\n    padding-right: 0;\n  }\n}\n\n.ql-toolbar button {\n  padding: 0 .25rem;\n  background: none;\n  border: none;\n  color: $body-color;\n  cursor: pointer;\n  transition: $transition-base;\n\n  &:hover {\n    color: $primary;\n  }\n\n  &:first-child {\n    margin-left: -.25rem;\n  }\n}\n\n.ql-toolbar .ql-active {\n  color: $primary;\n}\n\n.ql-toolbar button svg {\n  height: $font-size-lg;\n  width: $font-size-lg;\n}\n\n.ql-toolbar .ql-stroke {\n  stroke: currentColor;\n  stroke-width: 2;\n  stroke-linecap: round;\n  stroke-linejoin: round;\n  fill: none;\n}\n\n.ql-toolbar .ql-thin {\n  stroke-width: 1;\n}\n\n.ql-toolbar .ql-fill {\n  fill: currentColor;\n}\n\n.ql-toolbar input.ql-image {\n  position: absolute;\n  transform: scale(0);\n}\n\n\n// Tooltip\n\n.ql-tooltip {\n  position: absolute;\n  display: flex;\n  flex-wrap: nowrap;\n  width: 18.5rem;\n  background-color: $popover-bg;\n  border: $popover-border-width solid $popover-border-color;\n  border-radius: $popover-border-radius;\n  padding: $input-padding-y $input-padding-x;\n  margin-top: .6rem; // arrow width\n  box-shadow: $popover-box-shadow;\n\n  // Arrow\n\n  &:before, &:after {\n    content: '';\n    position: absolute;\n    left: 50%;\n    bottom: 100%;\n    transform: translateX(-50%);\n  }\n\n  &:before {\n    border-bottom: .6rem solid $popover-border-color;\n    border-left: .6rem solid transparent;\n    border-right: .6rem solid transparent;\n  }\n\n  &:after {\n    border-bottom: .5rem solid $popover-bg;\n    border-left: .5rem solid transparent;\n    border-right: .5rem solid transparent;\n  }\n}\n\n.ql-container .ql-tooltip:hover {\n  display: flex !important;\n}\n\n.ql-tooltip .ql-preview {\n  width: 100%;\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $font-size-sm, $btn-border-radius-sm);\n  @include transition($btn-transition);\n}\n\n.ql-tooltip.ql-editing .ql-preview {\n  display: none;\n}\n\n.ql-tooltip input {\n  display: none;\n  width: 100%;\n  padding-top: calc(#{$input-padding-y-sm} + #{$input-border-width});\n  padding-bottom: calc(#{$input-padding-y-sm} + #{$input-border-width});\n  background-color: transparent;\n  font-size: $font-size-sm;\n  line-height: $input-line-height-sm;\n  border: none;\n  color: $input-color;\n\n  &:focus {\n    outline: none;\n  }\n}\n\n.ql-tooltip.ql-editing input {\n  display: block;\n}\n\n\n.ql-tooltip .ql-action,\n.ql-tooltip .ql-remove {\n  margin-left: .25rem;\n}\n\n.ql-tooltip .ql-action::before,\n.ql-tooltip .ql-remove::before {\n  display: inline-block;\n  font-weight: $btn-font-weight;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: middle;\n  user-select: none;\n  border: $btn-border-width solid transparent;\n  cursor: pointer;\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $font-size-sm, $btn-border-radius-sm);\n  @include transition($btn-transition);\n\n  // Share hover and focus styles\n  &:hover, &:focus {\n    text-decoration: none;\n  }\n\n  &:focus,\n  &.focus {\n    outline: 0;\n    box-shadow: $btn-focus-box-shadow;\n  }\n}\n\n.ql-tooltip .ql-action::before,\n.ql-tooltip.ql-editing .ql-action::before {\n  @include button-variant($primary, $primary);\n}\n\n.ql-tooltip .ql-action::before {\n  content: 'Edit';\n}\n\n.ql-tooltip.ql-editing .ql-action::before {\n  content: 'Save';\n}\n\n.ql-tooltip .ql-remove::before {\n  @include button-variant($white, $white);\n  content: 'Remove';\n  border-color: $gray-300;\n}\n\n.ql-tooltip.ql-editing .ql-remove::before {\n  display: none;\n}\n\n\n// Formatting\n\n.ql-editor blockquote {\n  margin-bottom: $spacer;\n  font-size: $blockquote-font-size;\n}\n\n.ql-editor img {\n  max-width: 100%;\n  height: auto;\n}\n"], "sourceRoot": ""}