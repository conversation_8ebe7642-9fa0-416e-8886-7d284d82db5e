## Loops email traffic
import requests

LOOPS_API_KEY = "dba0c78c97cc2259f5753e79c4c1d6c3"

def form_submit(form_name: str, content: dict):
    url = "https://app.loops.so/api/v1/transactional"

    payload = {
        "email": "<EMAIL>",
        "transactionalId": "cm80o0cuz04w87m9b6oyplffa",
        "addToAudience": True,
        "dataVariables": {
            "name":content["name"],
            "email":content["email"],
            "tel":content["tel"],
            "subject":content["subject"],
            "message":content["message"],
        },
        
    }
    headers = {
        "Authorization": f"Bearer {LOOPS_API_KEY}",
        "Content-Type": "application/json"
    }

    response = requests.request("POST", url, json=payload, headers=headers)

    print(response.text)

    if response.status_code == 200:
        return True
    else:
        return False
    

def add_to_contacts(role, email):
    url = "https://app.loops.so/api/v1/contacts/create"

    payload = {
        "role": role,
        "email": email,
        "firstName": "",
        "lastName": "",
        "source": "",
        "subscribed": True,
        "userGroup": "",
        "userId": "",
        "mailingLists": {}
    }
    headers = {
        "Authorization": f"Bearer {LOOPS_API_KEY}",
        "Content-Type": "application/json"
    }

    response = requests.request("POST", url, json=payload, headers=headers)

    if response.status_code == 200:
        return True
    else:
        return False




