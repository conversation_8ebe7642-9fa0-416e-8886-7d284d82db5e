import mysql.connector
import random
import socket

def load(command, fetch="all"):
    try:
        connection = mysql.connector.connect(host='*************',
        database='crashevents_db',
        user='ce_website',
        password="2Z!h[ClLTGAmKFvF",
        use_unicode=True
        )

        cursor = connection.cursor()     # get the cursor

        cursor.execute(command)   
        if fetch == "one":
            content = cursor.fetchone()
        else:
            content = cursor.fetchall()

        cursor.close()
        connection.close()

        return content

    except Exception as e:
        print("unable to connect to database")
        print(str(e))
        cursor = None
        return None
    

