import sqlite3


if __name__ == "__main__":
    connection = sqlite3.connect('database/artists.db')


    with open('database/schema.sql') as f:
        connection.executescript(f.read())

    cur = connection.cursor()

    cur.execute("INSERT INTO members (firstname, lastname, artistname, genre, email, phone, job, birth, location, instagram, about) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                ('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>tsuj', 'Allround', '<EMAIL>', '+32 468 29 37 47', 'DJ','07-02-2004', '<PERSON>rug<PERSON>', 'dj_nitsuj', 'dit is een tekstje over de artiest')
                )

    connection.commit()
    connection.close()