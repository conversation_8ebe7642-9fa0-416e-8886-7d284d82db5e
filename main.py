# Imports
from flask import (
    Flask,
    render_template,
    url_for,
    request,
    session,
    redirect,
    flash,
    send_from_directory,
    make_response,
)
from flask_babel import Babel, gettext
from werkzeug.utils import secure_filename
from werkzeug.utils import redirect
import os
from pprint import pprint
import random
from operator import itemgetter

import database.connect as connect
from database.googli import update_sheet_artiesten

from mailing import add_to_contacts

# Email
import smtplib
from email.message import EmailMessage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

API_KEY = "*************************************"
LIST_ID = "a308abc9f6"
UPLOAD_FOLDER = "uploads"
ALLOWED_EXTENSIONS = {"png", "jpg", "jpeg", "pdf"}

app = Flask(__name__)
app.secret_key = "ljdsfhfoerarr9892JDdpsqjU87892"

app.config["UPLOAD_FOLDER"] = UPLOAD_FOLDER
app.config["MAX_CONTENT_LENGTH"] = 4 * 1024 * 1024
app.config["BABEL_TRANSLATION_DIRECTORIES"] = "./translations"

LANGUAGES = {"nl": "Nederlands", "en": "English", "fr": "Francais"}


def get_locale():
    language = request.cookies.get("lang", "nl")
    if language == None:
        return "en"
    else:
        return language


app.config["LANGUAGES"] = LANGUAGES

babel = Babel(app, locale_selector=get_locale)


# File allowed?
def allowed_file(filename):
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


def get_extension(filename):
    return filename.rsplit(".", 1)[1].lower()


# Send an email
def sendemail(subject, to, message, attachment=None):
    msg = EmailMessage()
    msg.set_content(message)

    msg["Subject"] = subject
    msg["From"] = "<EMAIL>"
    msg["To"] = to

    # Send the message via our own SMTP server.
    server = smtplib.SMTP_SSL("crash.events", 465)
    server.login("<EMAIL>", "LeThoJu69.420!")
    server.send_message(msg)
    server.quit()
    print("sent3")


def confirmation(subject, email, message):
    msg = EmailMessage()
    msg.set_content(message)

    msg["Subject"] = "Crash Events bevestiging"
    msg["From"] = "<EMAIL>"
    msg["To"] = email

    # Send the message via our own SMTP server.
    server = smtplib.SMTP_SSL("crash.events", 465)
    server.login("<EMAIL>", "LeThoJu69.420!")
    server.send_message(msg)
    server.quit()


## WEBSITE
@app.route("/")
def home():
    if "theme" not in session:
        session["theme"] = "night"
    return redirect(url_for("artists"))


@app.route("/language/<language>")
def set_language(language=None):
    response = make_response(redirect(request.referrer or "/"))
    response.set_cookie("lang", language)
    return response


@app.route("/toggle-theme")
def toggle_theme():
    current_theme = session.get("theme")
    if current_theme == "night":
        session["theme"] = "day"
    else:
        session["theme"] = "night"

    return redirect(request.args.get("current_page"))


@app.route("/artists", methods=["POST", "GET"])
def artists():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version
    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    artists = connect.load(f"SELECT * FROM artists")

    if session["theme"] == "night":
        return render_template(
            "night/artists.html", theme=session["theme"], artists=artists, lang=lang
        )
    if session["theme"] == "day":
        return render_template(
            "day/artists.html", theme=session["theme"], artists=artists, lang=lang
        )


@app.route("/agency")
def agency():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    if session["theme"] == "night":
        return render_template("night/agency.html", theme=session["theme"], lang=lang)
    if session["theme"] == "day":
        return render_template("day/agency.html", theme=session["theme"], lang=lang)


## TER INFO
@app.route("/about", methods=["POST", "GET"])
def about():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    success = False
    if request.method == "POST":
        name = request.form["name"]
        email = request.form["email"]
        event = request.form["event"]
        phone = request.form["tel"]
        interest = request.form["interest"]
        message = request.form["message"]
        sendemail(
            f"{name} heeft het contactformulier ingevuld.",
            "<EMAIL>",
            f"""
{name} vulde het contactformulier in

Taal: {lang}
Email: {email}
phone: {phone}
Evenement: {event}
Geinteresserd in: {interest}

bericht: {message}
""",
        )
        success = True
        # Send confirmation email to user
        confirmation(
            "Crash Events - Bevestiging",
            email,
            f"""
Beste {name},

Bedankt voor je bericht. We hebben je aanvraag goed ontvangen en zullen zo snel mogelijk contact met je opnemen.

Met vriendelijke groet,
Team Crash Events
"""
        )

        add_to_contacts("ce client", email)


    if session["theme"] == "night":
        return render_template("night/about.html", theme=session["theme"], lang=lang, success=success)
    if session["theme"] == "day":
        return render_template("day/about.html", theme=session["theme"], lang=lang, success=success)


@app.route("/<name>", methods=["POST", "GET"])
def artist(name):
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    if request.method == "POST":
        event = request.form["event"]
        contactp = request.form["contact"]
        date = request.form["date"]
        start = request.form["start"]
        end = request.form["end"]
        address = request.form["location"]
        email = request.form["email"]
        tel = request.form["tel"]
        message = request.form["message"]

        sendemail(
            f"Nieuwe boeking: {event}",
            "<EMAIL>",
            f"""
Nieuwe aanvraag:

Gewenste artiest: {name}

Taal: {lang}
Contactpersoon: {contactp}
Naam Evenement: {event}
Email: {email}
Tel. nr.: {tel}
Datum: {date}
Draaien: {start}u - {end}u
Adres: {address}

Extra bericht: {message}

""",
        )
        add_to_contacts("ce client", email)

    if "theme" not in session:
        session["theme"] = "night"

    artist = connect.load(
        f"SELECT * FROM artists WHERE instagram='{name}'", fetch="one"
    )
    images = os.listdir(os.path.join(app.static_folder, f"img/artists/{ artist[7] }"))

    if session["theme"] == "night":
        return render_template(
            "night/artist.html",
            theme=session["theme"],
            artist=artist,
            images=images,
            lang=lang,
        )
    if session["theme"] == "day":
        return render_template(
            "day/artist.html",
            theme=session["theme"],
            artist=artist,
            images=images,
            lang=lang,
        )


# CASES
@app.route("/case")
def case():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    return render_template("night/casestudy.html", theme=session["theme"], lang=lang)


@app.route("/case-zwienoclock")
def case_zwienoclock():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    images = os.listdir(
        os.path.join(app.static_folder, "img/casestudies/zwienoclock/photos")
    )
    return render_template(
        "night/case-zwienoclock.html", theme=session["theme"], images=images, lang=lang
    )


@app.route("/case-sss")
def case_sss():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    images = os.listdir(os.path.join(app.static_folder, "img/casestudies/sss/photos"))
    return render_template(
        "night/case-sss.html", theme=session["theme"], images=images, lang=lang
    )


@app.route("/case-khneon")
def case_khneon():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    return render_template("night/case-khneon.html", theme=session["theme"], lang=lang)


@app.route("/case-khkaboem")
def case_khkaboem():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    return render_template("day/case-khkaboem.html", theme=session["theme"], lang=lang)


@app.route("/case-tt")
def case_tt():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    images = os.listdir(os.path.join(app.static_folder, "img/casestudies/tt/photos"))
    return render_template(
        "day/case-tt.html", theme=session["theme"], images=images, lang=lang
    )


@app.route("/case-pernodricard")
def case_parnodricard():
    version = request.args.get("v")
    if version == "day" or version == "night":
        session["theme"] = version

    if "theme" not in session:
        session["theme"] = "night"

    lang = request.cookies.get("lang")
    if lang == None:
        return redirect(url_for("set_language", language="nl"))

    images = os.listdir(
        os.path.join(app.static_folder, "img/casestudies/pernodricard/photos")
    )
    return render_template(
        "day/case-pernodricard.html", theme=session["theme"], images=images, lang=lang
    )


@app.errorhandler(413)
def request_entity_too_large(error):
    flash("Bestand is te groot. Upload een kleiner bestand.", "warning")
    return redirect(request.url)


if __name__ == "__main__":
    app.run()
