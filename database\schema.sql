DROP TABLE IF EXISTS members;

CREATE TABLE members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    joined TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    lastname VA<PERSON><PERSON>R(100) NOT NULL,
    firstname VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    artistname VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    genre VARCHAR(100) NOT NULL,
    address VARCHAR(100),
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(100) NOT NULL,
    job VARCHAR(100) NOT NULL,
    birth VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    instagram VARCHAR(100) NOT NULL,
    account_number VARCHAR(100),
    account_name VARCHAR(100),
    price INT,
    BTW_number VARCHAR(100),
    ond_number VARCHAR(100),
    photo VARCHAR(100),
    about VARCHAR(100)

    
);

CREATE TABLE members (
    joined TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    address VA<PERSON><PERSON>R(100),
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(100) NOT NULL,
    birth VARCHAR(100) NOT NULL,
    account_number VARCHAR(100),
    btw VARCHAR(100),

    
);