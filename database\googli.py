import gspread
from google.oauth2 import service_account

def next_available_row(worksheet):
    str_list = list(filter(None, worksheet.col_values(1)))
    return str(len(str_list)+1)

def update_sheet_artiesten(naam, djnaam, email, tel, adres, instagram, mixtape, cv, foto, ssz, ondnr, motivatie):

    client = gspread.service_account('crash-events-4bbb55e1d3a2.json')
    sheet = client.open_by_key('1Urnz0WoqsLcWT_QKBMWjr6ArVDh6JJmSVXYKa7LSs0I')
    worksheet = sheet.get_worksheet(0)
    next_row = next_available_row(worksheet)

    ## ADD ALL NEW INFO

    # name = request.form["name"]
    worksheet.update_acell("A{}".format(next_row), naam)
    # djnaam = request.form["djnaam"]
    worksheet.update_acell("B{}".format(next_row), djnaam)
    # email = request.form["email"]
    worksheet.update_acell("C{}".format(next_row), email)
    # tel = request.form["tel"]
    worksheet.update_acell("D{}".format(next_row), tel)
    # adres = request.form["adres"]
    worksheet.update_acell("E{}".format(next_row), adres)
    # instagram = request.form["instagram"]
    worksheet.update_acell("F{}".format(next_row), instagram)
    # mixtape = request.form["mixtape"]
    worksheet.update_acell("G{}".format(next_row), mixtape)

    # cv = request.form["cv"]
    cv_link = f"Geen CV"
    worksheet.update_acell("H{}".format(next_row), cv_link)
    # foto = request.form["foto"]
    foto_link = f"https://www.crash.events/foto-ingezond-artiest-{foto}"
    worksheet.update_acell("I{}".format(next_row), foto_link)

    # ssz = request.form["ssz"]
    worksheet.update_acell("J{}".format(next_row), ssz)
    # ondnr = request.form["ondnr"]
    worksheet.update_acell("K{}".format(next_row), ondnr)
    # motivatie = request.form["motivatie"]
    worksheet.update_acell("L{}".format(next_row), motivatie)